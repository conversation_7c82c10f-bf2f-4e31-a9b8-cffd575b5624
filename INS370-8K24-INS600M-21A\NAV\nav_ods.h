/*!
    \file  nav_ods.h
    \brief Navigation odometry system header file
*/

#ifndef __NAV_ODS_H
#define __NAV_ODS_H

#include <stdint.h>

typedef enum {
    NAV_ODS_STATUS_OK = 0,
    NAV_ODS_STATUS_ERROR
} nav_ods_status_t;

typedef struct {
    float velocity;
    float distance;
    uint32_t timestamp;
} nav_ods_data_t;

nav_ods_status_t nav_ods_init(void);
nav_ods_status_t nav_ods_update(const nav_ods_data_t *ods_data);

#endif /* __NAV_ODS_H */
