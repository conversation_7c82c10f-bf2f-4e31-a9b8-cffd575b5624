/*****************************************************文件说明******************************************************************************/
/*文件名称：INAV_MAIN.C                                                                                                                    */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                                          */
/*编写人：                                                                                                                                 */
/*包含文件：                                                                                                                               */
/*测试用例：                                                                                                                               */
/*说明：本文件为INAV算法的主入口文件，整合了导航解算、Kalman滤波、初始对准等核心算法                                                      */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "CONST.h"
#include <string.h>

/*********************************************函数说明*******************************************************/
/*函数名称：INAV_Init                                                                                       */
/*函数功能描述：INAV算法初始化                                                                              */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：系统启动时调用此函数进行算法初始化                                                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void INAV_Init(void)
{
    // 系统初始化
    SysInit();
    
    // 设置默认初始装订参数
    g_InitBind.r_InitLati = DEFAULT_LATI * D2R;
    g_InitBind.r_InitLogi = DEFAULT_LOGI * D2R;
    g_InitBind.InitHeight = DEFAULT_HEI;
    g_InitBind.InitVn[0] = DEFAULT_VN;
    g_InitBind.InitVn[1] = DEFAULT_VU;
    g_InitBind.InitVn[2] = DEFAULT_VE;
    g_InitBind.isBind = YES;
    g_InitBind.isHeadBind = NO;
    
    // 初始化惯性系对准结构体
    InertialSysAlign_Init(&g_InitBind, &g_InertialSysAlign);
    
    // 设置系统工作阶段为待机
    g_SysVar.WorkPhase = PHASE_STANDBY;
}

/*********************************************函数说明*******************************************************/
/*函数名称：INAV_Process                                                                                    */
/*函数功能描述：INAV算法主处理函数                                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：陀螺数据、加速度计数据、GNSS数据                                                               */
/*输出变量：导航结果                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：每个导航周期调用此函数                                                                             */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void INAV_Process(DPARA Gyro[3], DPARA Acc[3], PAOCHE_FRAME_STRUCT* gnss_data)
{
    static DPARA LastGyro[3] = {0};
    static DPARA LastAcc[3] = {0};
    static COUNT process_count = 0;
    
    // 更新系统时间
    g_SysVar.Time += TIME_NAVI;
    
    // 处理GNSS数据
    if(gnss_data != NULL)
    {
        Read_And_Check_GNSS_Data(&g_GNSSData_In_Use, gnss_data, &g_Navi, &g_SysVar);
    }
    
    // 根据系统工作阶段执行相应算法
    switch(g_SysVar.WorkPhase)
    {
        case PHASE_STANDBY:
        {
            // 待机阶段，等待启动条件
            if(g_SysVar.Time > TIME_START_DELAY)
            {
                g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;
            }
            break;
        }
        
        case PHASE_COARSE_ALIGN:
        {
            // 粗对准阶段
            InertialSysAlignCompute(Gyro, LastGyro, Acc, LastAcc, &g_InertialSysAlign);
            
            // 检查对准是否完成
            if(g_InertialSysAlign.AlignTime >= TIME_COARSE_ALIGN)
            {
                FinishInertialSysAlign(&g_InertialSysAlign);
                
                // 初始化导航结构体
                Navi_Init(&g_InitBind, &g_InertialSysAlign, &g_Navi);
                
                // 如果有GNSS数据且有效，进入组合导航
                if(g_SysVar.isGNSSValid == YES)
                {
                    g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;
                }
                else
                {
                    g_SysVar.WorkPhase = PHASE_INS_NAVI;
                }
            }
            break;
        }
        
        case PHASE_INS_NAVI:
        {
            // 纯惯性导航阶段
            NaviCompute(Gyro, LastGyro, Acc, LastAcc, &g_Navi);
            
            // 检查是否有GNSS数据恢复
            if(g_SysVar.isGNSSValid == YES)
            {
                g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;
                g_SysVar.Time_INS_Alone = 0.0;
            }
            else
            {
                g_SysVar.Time_INS_Alone += TIME_NAVI;
            }
            break;
        }
        
        case PHASE_INTEGRATED_NAVI:
        {
            // 组合导航阶段
            NaviCompute(Gyro, LastGyro, Acc, LastAcc, &g_Navi);
            
            // 每个导航周期累加Fn矩阵
            ComputeFn(&g_Navi, g_Kalman.Fn);
            
            // 每个滤波周期执行Kalman滤波
            process_count++;
            if(process_count >= (COUNT)(TIME_FILTER / TIME_NAVI))
            {
                process_count = 0;
                
                // 启动Kalman滤波
                Kalman_StartUp(&g_Kalman, &g_GNSSData_In_Use, &g_SysVar, &g_Navi);
                
                if(g_Kalman.isKalmanStart == YES)
                {
                    KalCompute(&g_GNSSData_In_Use, &g_Kalman);
                    g_Kalman.isKalmanStart = NO;
                }
            }
            
            // 检查GNSS是否失效
            if(g_SysVar.isGNSSValid == NO)
            {
                g_SysVar.Time_Since_Last_GNSS += TIME_NAVI;
                if(g_SysVar.Time_Since_Last_GNSS > TIME_GNSS_LOST)
                {
                    g_SysVar.WorkPhase = PHASE_INS_NAVI;
                    g_SysVar.Time_INS_Alone = 0.0;
                }
            }
            else
            {
                g_SysVar.Time_Since_Last_GNSS = 0.0;
                g_SysVar.Time_Integrated_Navi += TIME_NAVI;
            }
            break;
        }
        
        default:
        {
            // 异常状态，重新初始化
            g_SysVar.WorkPhase = PHASE_STANDBY;
            break;
        }
    }
    
    // 保存当前数据作为下一周期的历史数据
    memcpy(LastGyro, Gyro, sizeof(DPARA) * 3);
    memcpy(LastAcc, Acc, sizeof(DPARA) * 3);
}

/*********************************************函数说明*******************************************************/
/*函数名称：INAV_GetNaviResult                                                                              */
/*函数功能描述：获取导航结果                                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：导航结果指针                                                                                    */
/*测试用例：暂缺                                                                                            */
/*备注1：外部程序调用此函数获取导航结果                                                                     */
/*返回值：导航结构体指针                                                                                    */
/************************************************************************************************************/
p_Navi INAV_GetNaviResult(void)
{
    return &g_Navi;
}

/*********************************************函数说明*******************************************************/
/*函数名称：INAV_GetSystemStatus                                                                            */
/*函数功能描述：获取系统状态                                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：系统状态指针                                                                                    */
/*测试用例：暂缺                                                                                            */
/*备注1：外部程序调用此函数获取系统状态                                                                     */
/*返回值：系统变量结构体指针                                                                                */
/************************************************************************************************************/
p_SysVar INAV_GetSystemStatus(void)
{
    return &g_SysVar;
}

/*********************************************函数说明*******************************************************/
/*函数名称：INAV_SetInitialPosition                                                                         */
/*函数功能描述：设置初始位置                                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：纬度、经度、高度（度）                                                                          */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：在系统启动前调用此函数设置初始位置                                                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void INAV_SetInitialPosition(DPARA latitude_deg, DPARA longitude_deg, DPARA height_m)
{
    g_InitBind.r_InitLati = latitude_deg * D2R;
    g_InitBind.r_InitLogi = longitude_deg * D2R;
    g_InitBind.InitHeight = height_m;
    g_InitBind.isBind = YES;
}
