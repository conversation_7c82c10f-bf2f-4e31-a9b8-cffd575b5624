# Keil项目配置更新脚本

## 自动化配置方法

如果您希望通过脚本自动更新项目配置，可以使用以下方法：

### 方法1：PowerShell脚本更新

创建一个PowerShell脚本来自动更新项目文件：

```powershell
# 项目配置更新脚本
$projectFile = "INS370-8K24-INS600M-21A/Project/INS.uvprojx"

# 备份原始项目文件
Copy-Item $projectFile "$projectFile.backup"

# 读取项目文件内容
$content = Get-Content $projectFile -Raw

# 移除不存在的文件引用
$filesToRemove = @(
    "nav.c", "nav_app.c", "nav_kf.c", "nav_math.c",
    "nav_ods.c", "nav_sins.c", "navlog.c", 
    "nav_gnss.c", "nav_imu.c", "nav_magnet.c",
    "nav_mahony.c", "nav_uwb.c", "transplant.c"
)

foreach ($file in $filesToRemove) {
    $pattern = "<File>.*?<FileName>$file</FileName>.*?</File>"
    $content = $content -replace $pattern, ""
}

# 添加INAV文件组
$inavFiles = @(
    "AnnTempCompen.c", "adxl355.c", "ahrs.c", "align.c",
    "compen.c", "dynamic_align.c", "fuseBodyOdomVel.c",
    "fuseGnssVelPosHeight.c", "fuseTwoAntHeading.c", "ins.c",
    "interrupt.c", "kalman.c", "matvecmath.c", "navi.c",
    "private_math.c", "read_and_check_gnss_data.c",
    "readpaoche.c", "readpaoche2.c", "readpaoche3.c",
    "sins.c", "sins2.c"
)

$inavGroup = @"
      <Group>
        <GroupName>INAV</GroupName>
        <Files>
"@

foreach ($file in $inavFiles) {
    $inavGroup += @"
          <File>
            <FileName>$file</FileName>
            <FileType>1</FileType>
            <FilePath>..\INAV\$file</FilePath>
          </File>
"@
}

$inavGroup += @"
        </Files>
      </Group>
"@

# 在Groups节点中插入INAV组
$content = $content -replace "(\s*</Groups>)", "`r`n$inavGroup`r`n`$1"

# 保存更新后的项目文件
Set-Content $projectFile $content -Encoding UTF8
```

### 方法2：手动编辑XML文件

1. **备份项目文件**：
   ```
   copy INS370-8K24-INS600M-21A/Project/INS.uvprojx INS370-8K24-INS600M-21A/Project/INS.uvprojx.backup
   ```

2. **用文本编辑器打开**：`INS370-8K24-INS600M-21A/Project/INS.uvprojx`

3. **移除不存在文件的引用**：
   删除包含以下文件名的`<File>`节点：
   - nav.c, nav_app.c, nav_kf.c, nav_math.c
   - nav_ods.c, nav_sins.c, navlog.c
   - nav_gnss.c, nav_imu.c, nav_magnet.c
   - nav_mahony.c, nav_uwb.c, transplant.c

4. **添加INAV组**：
   在`<Groups>`节点中添加：
   ```xml
   <Group>
     <GroupName>INAV</GroupName>
     <Files>
       <File>
         <FileName>navi.c</FileName>
         <FileType>1</FileType>
         <FilePath>..\INAV\navi.c</FilePath>
       </File>
       <File>
         <FileName>kalman.c</FileName>
         <FileType>1</FileType>
         <FilePath>..\INAV\kalman.c</FilePath>
       </File>
       <!-- 继续添加其他21个文件 -->
     </Files>
   </Group>
   ```

5. **添加包含路径**：
   在`<IncludePath>`节点中添加：`../INAV`

### 验证配置更新

更新完成后，验证以下内容：

1. **文件组织**：
   - NAV组只包含nav_cli.c
   - INAV组包含25个算法文件
   - Protocol组不包含transplant.c

2. **包含路径**：
   - 确认../INAV在包含路径中

3. **编译测试**：
   - 打开Keil项目
   - 执行Clean
   - 执行Rebuild All
   - 检查编译输出是否包含INAV文件

### 故障排除

如果更新后出现问题：

1. **恢复备份**：
   ```
   copy INS370-8K24-INS600M-21A/Project/INS.uvprojx.backup INS370-8K24-INS600M-21A/Project/INS.uvprojx
   ```

2. **重新手动配置**：
   使用方法1中的手动步骤

3. **检查文件路径**：
   确认所有文件路径相对于项目文件位置正确
