/***********************************************************************************
nav cli module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-17          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_CLI_H__
#define __NAV_CLI_H__
#include "nav_type.h"

void CliShowBuildVersion();
void CliShowNavStatus();
void CliShowAbormal();
void CliShowNavSet(char *pData);
void CliShowErrorCmd(char *pData);
void CliSetRestartNav();
void CliSetStopNav();
void CliShowHelp();
void ParseStrCmd(char *pData);
void CliReadConfig();
void CliReadKalmanErrMat();
void PrintOutMatrix(char *Name,double * mat, unsigned short row,unsigned short col);



#endif

