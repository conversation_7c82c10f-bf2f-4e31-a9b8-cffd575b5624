#ifndef ____INS_TLHTYPE_H____
#define ____INS_TLHTYPE_H____
#include "stdint.h"


#pragma pack(1)
typedef struct  poll_data
{
	uint16_t	data1;
	uint16_t 	data2;
	uint16_t	data3;
	uint32_t	gps_time;
	uint8_t	type;
} POLL_DATA, *pPOLL_DATA;


typedef struct{
		uint8_t 		header[3];	//0xbd,0xdb,0x0b
		short 			roll;		//???
		short 			pitch;		//???
		short			azimuth;	//???
		short 			gyroX;		//??x?
		short 			gyroY;		//??y?
		long			gyroZ;		//??z?
		short 			accelX;		//??x?
		short 			accelY;		//??y?
		short			accelZ;		//??z?
		long			latitude;	//??
		long			longitude;	//??
		long			altitude;	//??
	    short			ve;			//????
	    short			vn;			//????
	    short			vu;			//????

		short			speed_N;	//x
		short			speed_E;	//x
		short			speed_G;	//x
	
		uint8_t			status;
		uint8_t			reserved[6];
	    POLL_DATA		poll_frame;
	    uint32_t		gps_time;
	    uint8_t			type;
		uint8_t			xor_verify1;
		uint32_t		gps_week;
		uint8_t			xor_verify2;
	    uint8_t			GpsFlag_Pos;
	    uint8_t			NumSV;
	    uint8_t 		GpsFlag_heading;
	    uint8_t         Gps_Age;
	    uint8_t         Car_Status;
	    uint8_t			INS_status;

		short 			std_lat;
		short 			std_lon;
		short			std_height;
		short			std_heading;
		
		short			std_ve;			
	    short			std_vn;			
	    short			std_vu;			

		short			std_roll;			
	    short			std_pitch;			
	    short			devicetemperature;			

		uint32_t		gpssecond;
		//
		uint8_t			Nav_Standard_flag;  //0:??? 1:??? 2:????
	   /*0:NormalSts
		1:FaltSts
		2:Reserved1
		3:Reserved2
		4:Reserved3
		5:Reserved4
		6:Reserved5
		7:Reserved6*/
		uint8_t			INS_States;
		uint8_t 		Gear;/* ???? */

		short   WheelSpeed;
		


} DATA_STREAMx;


typedef enum coordinate_id_t
{
    coordinate_id_Airy_1830							= 1,
    coordinate_id_Modified_Airy						= 2,
    coordinate_id_Australian_National				= 3,
    coordinate_id_Bessel_1841						= 4,
    coordinate_id_Clarke_1866						= 5,
    coordinate_id_Clarke_1880						= 6,
    coordinate_id_Everest_India_1830				= 7,
    coordinate_id_Everest_Brunei_E_Malaysia			= 8,
    coordinate_id_Everest_W_Malaysia_Singapore		= 9,
    coordinate_id_Geodetic_Reference_System_1980	= 10,
    coordinate_id_Helmert_1906						= 11,
    coordinate_id_Hough_1960						= 12,
    coordinate_id_Parameters_of_the_Earth			= 13,
    coordinate_id_South_American_1969				= 14,
    coordinate_id_World_Geogetic_System_1972		= 15,
    coordinate_id_World_Geogetic_System_1984		= 16,
} coordinateIDTypeDef;

typedef enum pos_speed_type_t
{
    pos_type_NONE				=	0,		//	??
    pos_type_FixedPOS			=	1,		//	???Fix Position????
    pos_type_FixedHeight		=	2,		//	????
    pos_type_DopplerVelocity	=	8,		//	????????????
    pos_type_Signle				=	16,		//	????
    pos_type_PSRDiff			=	17,		//	?????
    pos_type_SBAS				=	18,		//	SBAS??
    pos_type_L1_Float			=	32,		//	L1???
    pos_type_IoNoFree_Float		=	33,		//	???????
    pos_type_Narrow_Float		=	34,		//	?????
    pos_type_L1_INT				=	48,		//	L1???
    pos_type_Wide_INT			=	49,		//	?????
    pos_type_Narrow_INT			=	50,		//	?????
    pos_type_INS				=	52,		//	??????
    pos_type_INS_PSRSP			=	53,		//	??????????
    pos_type_INS_PSDiff			=	54,		//	????????????
    pos_type_INS_RTKFloat		=	55,		//	???????????????
    pos_type_INS_RTKFixed		=	56,		//	??????????????
    pos_type_INS_Invalid		=	0xff	//	?????
} GNSS_POS_TypeDef, GNSS_VEL_TypeDef;

typedef enum resolve_type_t
{
    resolve_SOL_Computed				=	0,		//	???
    resolve_INSSufficient_OBS			=	1,		//	??????
    resolve_No_Convergence				=	2,		//	????
    resolve_Singularity					=	3,		//	????????
    resolve_COV_Trace					=	4,		//	???????????? ( ? > 1000m )
    resolve_Test_Dist					=	5,		//	?????? ( ????10km, ????3?  )
    resolve_Cold_Start					=	6,		//	????????
    resolve_V_H_Limit					=	7,		//	?????????
    resolve_Variance					=	8,		//	??????
    resolve_Residuals					=	9,		//	????
    resolve_Integrity_Warning			=	13,		//	???????????
    resolve_Integrity_Invalid			=	0xff	//	?????
} Resolve_TypeDef;

typedef enum single_mask_t
{
    single_mask_GPS_L1			=	1,			//	??GPS L1??
    single_mask_GPS_L2			=	2,			//	??GPS L2??
    single_mask_GPS_L5			=	4,			//	??GPS L5??
    single_mask_BDS_B3			=	8,			//	??BDS B3??
    single_mask_GOLNASS_L1		=	16,			//	??GOLNASS L1??
    single_mask_GOLNASS_L2		=	32,			//	??GOLNASS L1??
    single_mask_BDS_B1			=	64,			//	??BDS B1??
    single_mask_BDS_B2			=	128,		//	??BDS B2??
} Single_Mask_TypeDef;

typedef enum gnss_state_t
{
    gnss_state_NONE				= 0,
    gnss_state_SIGNLE_POINT		= 1,
    gnss_state_SBAS				= 2,
    gnss_state_INVALID_PPS		= 3,
    gnss_state_RTK_STABLE		= 4,
    gnss_state_RTK_FLOAT		= 5,
    gnss_state_VALUEING			= 6,
    gnss_state_MANUAL_BOOT		= 7,
    gnss_state_RTK_WIDE_LANE	= 8,
    gnss_state_PSEUDORANGE		= 9,
} GNSS_State_TypeDef;


typedef struct gnss_tra_t
{
    float timestamp;					//???
    double headingAngle;				//???
    double pitchAngle;					//???
    double rollAngle;					//???
    GNSS_State_TypeDef SolStatus;		//GPS?????
    int starNum;						//???
    float Age;							//????
    uint32_t stationID;					//???
    uint32_t checkCode;					//???

} GNSS_TRA_DataTypeDef;

typedef struct gnss_rmc_t
{
    uint8_t hour;						//??
    uint8_t minute;						//??
    uint8_t second;						//?
    uint16_t microSecond;				//??
    float timestamp;					//???
    char valid;							//???????
    double latitude;					//??
    char LatHemisphere;					//????
    double longitude;					//??
    char LonHemisphere;					//????
    double knot;						//????
    double trackTrue;					//??????
    uint32_t date;						//??
    uint8_t day;						//?
    uint8_t month;						//?
    uint8_t year;						//?
    double magDeclination;				//???
    double MagDeclinationDir;			//?????
    char mode;							//??
} GNSS_RMC_DataTypeDef;

typedef struct gnss_time_syn_t
{
    uint8_t year;
    uint8_t month;
    uint8_t date;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} GNSS_TIME_SYN_DataTypeDef;

typedef struct gnss_vtg_t
{
    double courseNorthAngle;			//?????????????
    char   trueNorth;					//?????
    double courseMagAngle;				//?????????????
    char   magneticNorth;				//?????
    double rateKnots;					//????(?)
    char   N;
    double rateKm;						//????(??/??)
    char   K;
    char mode;							//??
} GNSS_VTG_DataTypeDef;

typedef struct gnss_zda_t
{
    uint8_t hour;						//??
    uint8_t minute;						//??
    uint8_t second;						//?
    uint16_t microSecond;				//??
    float timestamp;
    uint8_t day;						//?
    uint8_t month;						//?
    uint16_t year;						//?
    double hour_timeDomain;				//?????????????
    double minute_timeDomain;			//?????????????
} GNSS_ZDA_DataTypeDef;


typedef struct
{
    char sync[10];			//????
    float CPUIDle;  		//?????????????,???? 1 ?
    uint16_t gpsWn;			//GPS ??
    uint32_t gpsMs;			//GPS ???,??? ms

} BESTPOS_Header_TypeDef, BESTVEL_Header_TypeDef, HEADING_Header_TypeDef;


typedef struct gnss_heading_t
{
    HEADING_Header_TypeDef header;
    Resolve_TypeDef calcState;					//????
    GNSS_POS_TypeDef posType;					//????
    float 	baseLen;							//???
    double courseAngle;							//???
    double pitchAngle;							//???
    double courseStandardDeviation;				//??????
    double pitchStandardDeviation;				//??????
    uint32_t baseStationID;						//??id
    uint8_t numSatellitesTracked;				//??????
    uint8_t numSatellitesUsed;					//??????
    uint8_t numSatellitesAboveCutoffAngle;		//???????????
    uint8_t numSatellitesAboveCutoffAngleL2;	//????????L2??????
    uint8_t solutionState;						//???
    Single_Mask_TypeDef sigMask;				//????
} GNSS_Heading_DataTypeDef;

typedef struct gnss_bestpos_t
{
    BESTPOS_Header_TypeDef header;
    Resolve_TypeDef calcState;					//????
    GNSS_POS_TypeDef posType;					//????
    double latitude;							//??
    double longitude;							//??
    double height;								//??
    double heightVariance;						//????
    coordinateIDTypeDef coordinateID;			//???id?
    double latitudeStandardDeviation;			//?????
    double longitudeStandardDeviation;			//?????
    double heightStandardDeviation;				//?????
    uint8_t stationID;							//??ID
    double differPeriod;						//????
    double solutionPeriod;						//????
    uint8_t numSatellitesTracked;				//??????
    uint8_t numSatellitesUsed;					//??????
} GNSS_BestPos_DataTypeDef;

typedef struct gnss_bestvel_t
{
    BESTVEL_Header_TypeDef header;
    Resolve_TypeDef calcState;					//????
    GNSS_VEL_TypeDef velType;					//????
    float latency;								//????????????,?????
    float age;									//????
    double horSpd;								//??????,m/s
    double trkGnd;								//??????????????(??????),deg
    double vertSpd;								//????,m/s,????????(??),????????(??)

} GNSS_BestVel_DataTypeDef;


typedef struct gnss_gga_t
{
    float timestamp;							//???
    double latitude;							//??
    char LatHemisphere;							//????
    double longitude;							//??
    char LonHemisphere;							//????
    GNSS_State_TypeDef GPSState;				//GPS??
    uint8_t numSatellitesUsed;					//??????
    float HDOP;									//HDOP??????
    float height;								//????
    char a_units ;								//??????(M = m)
    float undulation;							//????????????? WGS84 ??????????????????????,??,???
    char u_units ;								//?????????
    uint16_t age;  								//??????,????
    uint16_t stn_ID;  							//???? ID
    uint32_t gps_week;							//GPS?
    uint32_t gps_time;							//GPS???
} GNSS_GGA_DataTypeDef;


typedef struct GPS_Data_t
{
    float timestamp;					/* ???, ??: s , ??: 0.0001*/
    uint8_t StarNum;					/* ?? */
    uint8_t 		leapsec;
    uint8_t PositioningState;			/* ???? */
    Resolve_TypeDef ResolveState[3];	/* ???? */
    GNSS_POS_TypeDef PositionType[3];	/* ??/??/???? */
    char LonHemisphere;					/* ???? E?? ? W??  */
    double Lon;							/* ??, ??: , ??: 1e-7*/
    char LatHemisphere;					/* ???? N?? ? S?? */
    double Lat;							/* ??, ??: , ??: 1e-7*/
    float Altitude;						/* ??, ??: m, ??:0.01*/
    float Heading;						/* ???, ??: , ??: 0.01*/
    float Pitch;						/* ???, ??: , ??: 0.01*/
    float Roll;							/* ???, ??: , ??: 0.01*/
    float baseline;						/* ???, ??: m*/
	float		Age;
	float 		trackTrue;
    float HDOP;							/* HDOP?????? 0.5 - 99.9 */
    float GroundSpeed;					/* ??, ??: m/s, ??: 0.1*/
    unsigned int	gpsweek;
    uint32_t 		gpssecond;
    float			ve;
    float 		vn;
    float			vu;
		
    unsigned int	rtkStatus;
    unsigned char supportposvelstd;

    float LonStd;						/* ?????, ??: */
    float LatStd;						/* ?????, ??: */
    float AltitudeStd;					/* ?????, ??: m*/

    double hdgstddev;					/* ??????, ??: m*/
    double ptchstddev;					/* ??????, ??: m */

    double		vestd;
    double		vnstd;
    double		vustd;

	
    uint32_t	ppsDelay;
} GPSDataTypeDef;


/******************??kalman??????***********************/
typedef struct
{
/******************************************kalman P??????****************************************/
	/*********************************??????????**********************************/
	double  attBias[3];								//unit:degree
	double  velBias[3];								//unit:m/s
	double	posBias[3];								//unit:m,m,m
	/*******************************??????******************************/
	double  leverBias[3];							//unit:m
	/*******************************IMU??******************************/
	//??????
	double	gyroBias[3];							//unit:dps
	//????
	double	accBias[3];								//unit:mg

/******************************************kalman Q??????****************************************/	
	//????????
	double	angRandomWalk[3];					//unit:dpsh
	//???????
	double	velRandomWalk[3];					//unit:MGPSH	


/******************************************kalman R??????****************************************/		
	/*******************************GNSS??******************************/
	double gnssAttErr[3];						//unit:degree
	double gnssVelErr[3];						//unit:m/s
	double gnssSppPosErr[3];					//unit:m,m,m
	double gnssRtkPosErr[3];					//unit:m,m,m
	/*******************************ODSMETER??******************************/
	double odsVelErr[3];						//unit:m/s
	/*******************************?????******************************/
	double magAttErr[3];						//unit:degree
	/*******************************UWB??*********************************/
	double uwbPosErr[3];						//unit:m,m,m
} KalmanErrorMat_t;

#ifndef linux
#pragma pack(1)
#endif
typedef struct
{
    float gnssArmLength[3];
    float gnssAtt_from_vehicle[3];//?????IMU?????
    float OBArmLength[3];
    float OBAtt_from_vehicle[3];//?????IMU?????
    unsigned char flag;				//bit0:gps???????
    							//bit1:?????IMU??????????
    							//bit2:????????????
    							//bit3:?????IMU??????????
    							//bit4:??????????? 
   float wb_set[3];				//degree/h
   unsigned char methodType;	//0:??kalmnan , 1:????????
   unsigned char sim; 				//????0:???? 1:??
   unsigned int  lostepoch;			//????????
   unsigned int  HP;			//?????????,0:???;1:??

   KalmanErrorMat_t mKFErrMat;
} Param_t; //

typedef struct
{
	double gyro_off[3]; 			  //??????rad/s
	double acc_off[3];				  //??????m/s2
	double gnssAtt_from_vehicle2[3];  //?????????,???
	unsigned char Nav_Standard_flag;		  //0:??? 1:??? 2:????,??????0?2
	double att_ods2_b_filte_2;		//IMU????y???	
}AdjPara_t;

#ifndef linux
#pragma pack()
#endif




#endif //____INS_TLHTYPE_H____


