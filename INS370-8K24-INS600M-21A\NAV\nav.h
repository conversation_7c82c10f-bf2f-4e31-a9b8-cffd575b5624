/*!
    \file  nav.h
    \brief Navigation system header file
    \note  This file provides compatibility layer for original NAV interface
*/

#ifndef __NAV_H
#define __NAV_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "nav_type.h"
#include "nav_const.h"

/* Navigation status enumeration */
typedef enum {
    NAV_STATUS_IDLE = 0,
    NAV_STATUS_INIT,
    NAV_STATUS_RUNNING,
    NAV_STATUS_ERROR
} nav_status_t;

/* Navigation parameter IDs */
#define NAV_PARAM_INIT_LAT      0x01
#define NAV_PARAM_INIT_LON      0x02
#define NAV_PARAM_INIT_ALT      0x03

/* IMU data structure */
typedef struct {
    float gyro[3];      /* Gyroscope data (rad/s) */
    float acc[3];       /* Accelerometer data (m/s²) */
    uint32_t timestamp; /* Timestamp */
    bool valid;         /* Data validity flag */
} nav_imu_data_t;

/* GNSS data structure */
typedef struct {
    double latitude;    /* Latitude (degrees) */
    double longitude;   /* Longitude (degrees) */
    float altitude;     /* Altitude (meters) */
    float velocity[3];  /* Velocity NED (m/s) */
    float heading;      /* Heading (degrees) */
    uint32_t timestamp; /* Timestamp */
    bool valid;         /* Data validity flag */
} nav_gnss_data_t;

/* Navigation result structure */
typedef struct {
    double latitude;    /* Latitude (degrees) */
    double longitude;   /* Longitude (degrees) */
    float altitude;     /* Altitude (meters) */
    float velocity[3];  /* Velocity NED (m/s) */
    float attitude[3];  /* Attitude: heading, pitch, roll (degrees) */
    uint32_t timestamp; /* Timestamp */
    nav_status_t status; /* Navigation status */
} nav_result_t;

/* Function prototypes */
nav_status_t nav_init(void);
nav_status_t nav_update(const nav_imu_data_t *imu_data, const nav_gnss_data_t *gnss_data, nav_result_t *nav_result);
nav_status_t nav_get_status(void);
nav_status_t nav_reset(void);
nav_status_t nav_set_param(uint32_t param_id, float value);
nav_status_t nav_get_param(uint32_t param_id, float *value);

#ifdef __cplusplus
}
#endif

#endif /* __NAV_H */
