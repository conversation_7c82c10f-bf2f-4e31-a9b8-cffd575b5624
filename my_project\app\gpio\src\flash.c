//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：flash.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.21
//---------------------------------------------------------

#include "flash.h"


static xpi_nor_config_t s_xpi_nor_config;
int norflash_init(void)
{
    xpi_nor_config_option_t option;
    option.header.U = BOARD_APP_XPI_NOR_CFG_OPT_HDR;
    option.option0.U = BOARD_APP_XPI_NOR_CFG_OPT_OPT0;
    option.option1.U = BOARD_APP_XPI_NOR_CFG_OPT_OPT1;

    hpm_stat_t status = rom_xpi_nor_auto_config(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config, &option);
    if (status != status_success) {
        printf("Error: rom_xpi_nor_auto_config\n");
        while (1);
    }
    return 0;
}

int norflash_read(uint32_t offset, void *buf, uint32_t size_bytes)
{
    hpm_stat_t status = rom_xpi_nor_read(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto,
                        &s_xpi_nor_config, (uint32_t *)buf, offset, size_bytes);
    if (status != status_success) {
        return -1;
    }

    return 0;
}

int norflash_read_mem(uint32_t offset, void *buf, uint32_t size_bytes)
{
    uint32_t flash_addr = 0x80000000 + offset;
    uint32_t aligned_start = HPM_L1C_CACHELINE_ALIGN_DOWN(flash_addr);
    uint32_t aligned_end = HPM_L1C_CACHELINE_ALIGN_UP(flash_addr + size_bytes);
    uint32_t aligned_size = aligned_end - aligned_start;

    l1c_dc_invalidate(aligned_start, aligned_size);

    memcpy(buf, (void *)flash_addr, size_bytes);
    return 0;
}

int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes)
{
    hpm_stat_t status = rom_xpi_nor_program(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto,
                                 &s_xpi_nor_config, (const uint32_t *)buf, offset, size_bytes);
    if (status != status_success) {
        return -1;
    }
    return 0;
}

int norflash_erase_chip(void)
{
    hpm_stat_t status = rom_xpi_nor_erase_chip(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto, &s_xpi_nor_config);
    if (status != status_success) {
        return -1;
    }
    return 0;
}

int norflash_erase_block(uint32_t offset)
{
    hpm_stat_t status = rom_xpi_nor_erase_block(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto, &s_xpi_nor_config, offset);
    if (status != status_success) {
        return -1;
    }
    return 0;
}

//扇区擦除
int norflash_erase_sector(uint32_t offset)
{
    hpm_stat_t status = rom_xpi_nor_erase_sector(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto, &s_xpi_nor_config, offset);
    if (status != status_success) {
        return -1;
    }
    return 0;
}


uint32_t norflash_get_chip_size(void)
{
    uint32_t flash_size;
    rom_xpi_nor_get_property(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config, 
            xpi_nor_property_total_size, &flash_size);
    return flash_size;
}

uint32_t norflash_get_block_size(void)
{
    uint32_t block_size;
    rom_xpi_nor_get_property(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config, 
            xpi_nor_property_block_size, &block_size);
    return block_size;
}

uint8_t norflash_get_erase_value(void)
{
    return 0xffu;
}

////擦除扇区
//void  Drv_FlashErase(uint32_t address)
//{
//    disable_global_irq(CSR_MSTATUS_MIE_MASK);
//    norflash_erase_sector(address);
//    enable_global_irq(CSR_MSTATUS_MIE_MASK);
    
//}

////写入FLASH
//void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
//{
//    disable_global_irq(CSR_MSTATUS_MIE_MASK);
//    norflash_write(uiAddress, pucBuff, uiLen);
//    enable_global_irq(CSR_MSTATUS_MIE_MASK);
//}

////读取FLASH
//void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
//{
//  norflash_read(uiAddress, pucBuff, uiLen);
//}

////测试FLASH
//uint8_t WriteTestData[20] = {0x055,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x06,0x04,0x55,0x15};
//uint8_t WriteTestData1[20] = {0xAA,0x0B,0x0C,0x0D,0x0E,0x0F,0x43,0x48,0x69,0x7A,0xCB,0xAC,0x1D,0x6E,0x8F,0x06,0x04,0x35,0x97,0xAA};

//uint8_t ReadTestData[10];
//uint8_t AppReadTestData[10];
//void FlashTest(void)
//{
//    uint8_t temp[5];
//    Drv_FlashErase(APP_UPDATE_ADDRESS);
//    //模拟写入到备份区
//    memset(ReadTestData,0,sizeof(ReadTestData));
//    Drv_FlashWrite(WriteTestData,APP_UPDATE_ADDRESS,20);
//    Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);

//    memset(ReadTestData,0,sizeof(ReadTestData));
//    Drv_FlashWrite(WriteTestData1,APP_UPDATE_ADDRESS+20,20);
//    Drv_FlashRead(ReadTestData+5,APP_UPDATE_ADDRESS+20,5);


//    //模拟Bootloader读取备份区数据，并写入运行区
//    memset(ReadTestData,0,sizeof(ReadTestData));
//    Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);

//}
