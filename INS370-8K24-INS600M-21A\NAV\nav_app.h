/*!
    \file  nav_app.h
    \brief Navigation application header file
*/

#ifndef __NAV_APP_H
#define __NAV_APP_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "nav.h"

/* Navigation application status */
typedef enum {
    NAV_APP_STATUS_OK = 0,
    NAV_APP_STATUS_ERROR
} nav_app_status_t;

/* Navigation application state */
typedef enum {
    NAV_APP_STATE_IDLE = 0,
    NAV_APP_STATE_READY,
    NAV_APP_STATE_RUNNING,
    NAV_APP_STATE_ERROR
} nav_app_state_t;

/* Navigation application configuration */
typedef struct {
    uint32_t update_rate;       /* Update rate in Hz */
    uint32_t output_rate;       /* Output rate in Hz */
    float init_latitude;        /* Initial latitude in degrees */
    float init_longitude;       /* Initial longitude in degrees */
    float init_altitude;        /* Initial altitude in meters */
    bool enable_gnss;           /* Enable GNSS fusion */
    bool enable_mag;            /* Enable magnetometer */
    bool enable_baro;           /* Enable barometer */
} nav_app_config_t;

/* Function prototypes */
nav_app_status_t nav_app_init(const nav_app_config_t *config);
nav_app_status_t nav_app_start(void);
nav_app_status_t nav_app_stop(void);
nav_app_status_t nav_app_process(const nav_imu_data_t *imu_data, const nav_gnss_data_t *gnss_data, nav_result_t *nav_result);
nav_app_state_t nav_app_get_state(void);
nav_app_status_t nav_app_reset(void);
nav_app_status_t nav_app_get_config(nav_app_config_t *config);
nav_app_status_t nav_app_set_config(const nav_app_config_t *config);

#ifdef __cplusplus
}
#endif

#endif /* __NAV_APP_H */
