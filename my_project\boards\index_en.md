# Evaluation Boards

This chapter describes the board-related configuration

:::{eval-rst}
.. toctree::
   :maxdepth: 3
   :numbered:

   hpm6750evk/README_en
   hpm6750evk2/README_en
   hpm6750evkmini/README_en
   hpm6300evk/README_en
   hpm6200evk/README_en
   hpm5300evk/README_en
   hpm5301evklite/README_en
   hpm6800evk/README_en
   hpm6e00evk/README_en
   extension/README_en

:::

**Board level vocabulary cross-reference table**

(lab_board_resource)=

:::{tab} HPM6750EVK
Click [here](lab_hpm6750_evk_board) to view the HPM6750EVK development board
:::
:::{tab} HPM6750EVK2
Click [here](lab_hpm6750_evk2_board) to view the HPM6750EVK2 development board
:::
:::{tab} HPM6750EVKMINI
Click [here](lab_hpm6750_evkmini_board) to view the HPM6750EVKMINI development board
:::
:::{tab} HPM6300EVK
Click [here](lab_hpm6300_evk_board) to view the HPM6300EVK development board
:::
:::{tab} HPM6200EVK
Click [here](lab_hpm6200_evk_board) to view the HPM6200EVK development board
:::
:::{tab} HPM5300EVK
Click [here](lab_hpm5300_evk_board) to view the HPM5300EVK development board
:::
:::{tab} HPM5301EVKLITE
Click [here](lab_hpm5301_evklite_board) to view the HPM5301EVKLITE development board
:::
:::{tab} HPM6800EVK
Click [here](lab_hpm6800_evk_board) to view the HPM6800EVK development board
:::
:::{tab} HPM6E00EVK
Click [here](lab_hpm6e00_evk_board) to view the HPM6E00EVK development board
:::

(lab_board_lcd_pin)=
(lab_board_app_i2c_pin)=
(lab_board_cam_i2c_pin)=
(lab_board_cap_i2c_pin)=
(lab_board_rgb_pin)=
(lab_board_app_spi_pin)=
(lab_board_drv_pwm_pin)=
(lab_board_drv_hrpwm_pin)=
(lab_board_motor_ctrl_pin)=
(lab_board_overiew)=
(lab_board_app_acmp_pin)=
(lab_board_app_gptmr_pin)=
(lab_board_app_audio_input)=
(lab_board_app_headphone)=
(lab_board_app_dao)=
(lab_board_app_i2s_pin)=
(lab_board_app_pla_pin)=
(lab_board_app_plb_pin)=
(lab_board_puart_pin)=
