/******************** (C) COPYRIGHT 2011 WCH ***********************************
* File Name          : HAL.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/08/28
* Description        : 硬件相关部分程序
*******************************************************************************/



/******************************************************************************/
/* 头文件包含 */
#include "stdio.h"
#include "bsp_sys.h"

#include "TYPE.H"										 /* 常变量定义头文件 */		
#include "CH378_HAL.H"										 /* 硬件引脚宏定义头文件 */

#if CHIP_USED == USE_CHIP_GD32 
#include "gd32f4xx.h"
#include "systick.h"
#else
#include "stm32f10x.h"
#endif

/*******************************************************************************
* Function Name  : Delay_uS
* Description    : 微秒级延时函数(基本准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_mDelayuS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_us(delay);
#else
	UINT8 i, j;

	for( i = delay; i != 0; i -- ) 
	{
		for( j = 8; j != 0; j -- )
		{
		}		
	}
#endif
}

/*******************************************************************************
* Function Name  : Delay_mS
* Description    : 毫秒级延时函数(基本准确)
* Input          : delay---延时值
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_mDelaymS( UINT8 delay )
{
#if CHIP_USED == USE_CHIP_GD32
	delay_ms(delay);
#else
	UINT8 i, j;

	for( i = delay; i != 0; i -- ) 
	{
		for( j = 200; j != 0; j -- )
		{
			mDelayuS( 5 );
		}		
	}
#endif
}

/*******************************************************************************
* Function Name  : mStopIfError
* Description    : 检查操作状态
*                  如果错误,则显示错误代码并停机,应该替换为实际的处理措施,
*                  例如显示错误信息,等待用户确认后重试等
* Input          : iError---待检测的状态值
* Output         : None
* Return         : None
*******************************************************************************/
void mStopIfError( UINT8 iError )
{
	if( iError == ERR_SUCCESS ) 
	{
		return;  												 /* 操作成功 */
	}
	printf( "Error: %02X\n", (UINT16)iError );  			 	 /* 显示错误 */
	while( 1 ) 
	{
	}
}
