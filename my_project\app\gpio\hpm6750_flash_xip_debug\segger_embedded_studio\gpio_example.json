{"target": {"name": "gpio_example", "sources": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/src/gpio.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains/segger/startup.s,E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/pinmux.c,E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/board.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains/reset.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains/trap.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/system.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/hpm_sysctl_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/hpm_l1c_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/hpm_clock_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/hpm_otp_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot/hpm_bootheader.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_uart_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_femc_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_sdp_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_lcdc_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_i2c_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_pmp_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_rng_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_gpio_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_spi_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_pdma_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_wdg_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_dma_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_gptmr_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_pwm_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_pllctl_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_usb_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_rtc_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_acmp_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_i2s_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_dao_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_pdm_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_vad_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_cam_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_can_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_jpeg_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_enet_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_sdxc_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_adc12_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_adc16_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_pcfg_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_ptpc_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_mchtmr_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/src/hpm_tamp_drv.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/hpm_sbrk.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/hpm_swap.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/hpm_ffssi.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/hpm_crc32.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/hpm_debug_console.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common/ff.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common/ffunicode.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/diskio.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc/hpm_sdmmc_disk.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_host.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_common.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_sd.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_emmc.c,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/hpm_sdmmc_port.c", "includes": "E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/.,E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/.,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/.,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/.,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/.,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/.,E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/.,E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include", "defines": "FLASH_XIP=1,HPMS<PERSON>_HAS_HPMSDK_MULTICORE=y,HPMSOC_HAS_HPMSDK_GPIO=y,HPMSOC_HAS_HPMSDK_PLIC=y,HPMSOC_HAS_HPMSDK_MCHTMR=y,HPMSOC_HAS_HPMSDK_PLICSW=y,HPMSOC_HAS_HPMSDK_GPIOM=y,HPMSOC_HAS_HPMSDK_ADC12=y,HPMSOC_HAS_HPMSDK_ADC16=y,HPMSOC_HAS_HPMSDK_ACMP=y,HPMSOC_HAS_HPMSDK_SPI=y,HPMSOC_HAS_HPMSDK_UART=y,HPMSOC_HAS_HPMSDK_CAN=y,HPMSOC_HAS_HPMSDK_WDG=y,HPMSOC_HAS_HPMSDK_MBX=y,HPMSOC_HAS_HPMSDK_PTPC=y,HPMSOC_HAS_HPMSDK_DMAMUX=y,HPMSOC_HAS_HPMSDK_DMA=y,HPMSOC_HAS_HPMSDK_RNG=y,HPMSOC_HAS_HPMSDK_KEYM=y,HPMSOC_HAS_HPMSDK_I2S=y,HPMSOC_HAS_HPMSDK_DAO=y,HPMSOC_HAS_HPMSDK_PDM=y,HPMSOC_HAS_HPMSDK_PWM=y,HPMSOC_HAS_HPMSDK_HALL=y,HPMSOC_HAS_HPMSDK_QEI=y,HPMSOC_HAS_HPMSDK_TRGM=y,HPMSOC_HAS_HPMSDK_SYNT=y,HPMSOC_HAS_HPMSDK_LCDC=y,HPMSOC_HAS_HPMSDK_CAM=y,HPMSOC_HAS_HPMSDK_PDMA=y,HPMSOC_HAS_HPMSDK_JPEG=y,HPMSOC_HAS_HPMSDK_ENET=y,HPMSOC_HAS_HPMSDK_GPTMR=y,HPMSOC_HAS_HPMSDK_USB=y,HPMSOC_HAS_HPMSDK_SDXC=y,HPMSOC_HAS_HPMSDK_CONCTL=y,HPMSOC_HAS_HPMSDK_I2C=y,HPMSOC_HAS_HPMSDK_SDP=y,HPMSOC_HAS_HPMSDK_FEMC=y,HPMSOC_HAS_HPMSDK_SYSCTL=y,HPMSOC_HAS_HPMSDK_IOC=y,HPMSOC_HAS_HPMSDK_OTP=y,HPMSOC_HAS_HPMSDK_PPOR=y,HPMSOC_HAS_HPMSDK_PCFG=y,HPMSOC_HAS_HPMSDK_PSEC=y,HPMSOC_HAS_HPMSDK_PMON=y,HPMSOC_HAS_HPMSDK_PGPR=y,HPMSOC_HAS_HPMSDK_VAD=y,HPMSOC_HAS_HPMSDK_PLLCTL=y,HPMSOC_HAS_HPMSDK_BPOR=y,HPMSOC_HAS_HPMSDK_BCFG=y,HPMSOC_HAS_HPMSDK_BUTN=y,HPMSOC_HAS_HPMSDK_BGPR=y,HPMSOC_HAS_HPMSDK_RTC=y,HPMSOC_HAS_HPMSDK_BSEC=y,HPMSOC_HAS_HPMSDK_BKEY=y,HPMSOC_HAS_HPMSDK_BMON=y,HPMSOC_HAS_HPMSDK_TAMP=y,HPMSOC_HAS_HPMSDK_MONO=y,HPMSOC_HAS_HPMSDK_PMP=y,SD_FATFS_ENABLE=1", "linker": "E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains/segger/flash_xip.icf", "link_symbols": "_flash_size=16M,_extram_size=32M", "sdk_base": "E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk", "board": "hpm6750", "board_dir": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750", "soc": "HPM6750", "register_definition": "E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/hpm_ses_reg.xml", "cpu_register_definition": "E:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/hpm_ses_riscv_cpu_regs.xml", "post_build_command": "", "heap_size": "0x4000", "stack_size": "0x4000", "cplusplus": "11", "gcc_opt_level": "", "target_device_name": "HPM6750xVMx", "toolchain_variant": "Standard", "cflags": "", "compiler_abi": "ilp32", "compiler_isa": "rv32imac", "compiler_arch_exts_csr": "1", "compiler_arch_exts_fencei": "1", "compiler_arch_exts_zba": "", "compiler_arch_exts_zbb": "", "compiler_arch_exts_zbc": "", "compiler_arch_exts_zbs": "", "ses_link_input": "", "enable_nds_dsp": "", "enable_cpp_exceptions": "", "library_io_type": "STD", "extra_ses_options": "", "app_name": "demo", "openocd": "E:/2014902/HPM6750/sdk_env_v1.6.0/tools/openocd/openocd.exe", "openocd_soc": "hpm6750-dual-core", "debug_probe": "cmsis_dap", "auto_start_gdb_server": "Yes", "gdb_server_port": "3333", "gdb_server_reset_command": "reset halt"}}