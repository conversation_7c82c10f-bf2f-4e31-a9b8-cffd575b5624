/*!
    \file  transplant.h
    \brief Protocol transplant header file
*/

#ifndef __TRANSPLANT_H
#define __TRANSPLANT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* Transplant status enumeration */
typedef enum {
    TRANSPLANT_STATUS_IDLE = 0,
    TRANSPLANT_STATUS_READY,
    TRANSPLANT_STATUS_OK,
    TRANSPLANT_STATUS_ERROR
} transplant_status_t;

/* Transplant configuration structure */
typedef struct {
    uint32_t baudrate;      /* Communication baudrate */
    uint8_t protocol_type;  /* Protocol type */
    bool enable_crc;        /* Enable CRC check */
    uint32_t timeout_ms;    /* Timeout in milliseconds */
} transplant_config_t;

/* Function prototypes */
transplant_status_t transplant_init(void);
transplant_status_t transplant_process(const uint8_t *data, uint32_t length);
transplant_status_t transplant_get_status(void);
transplant_status_t transplant_reset(void);
transplant_status_t transplant_config(const transplant_config_t *config);
transplant_status_t transplant_send(const uint8_t *data, uint32_t length);

#ifdef __cplusplus
}
#endif

#endif /* __TRANSPLANT_H */
