/*!
    \file  nav_math.c
    \brief Navigation math functions wrapper
    \note  This file provides wrapper for INAV math functions
*/

#include "nav_math.h"
#include "../INAV/FUNCTION.h"
#include <math.h>

/*!
    \brief      Vector cross product
    \param[in]  a: vector a
    \param[in]  b: vector b
    \param[out] c: result vector c = a × b
    \retval     none
*/
void nav_math_cross_product(const float a[3], const float b[3], float c[3])
{
    VEC va[3] = {a[0], a[1], a[2]};
    VEC vb[3] = {b[0], b[1], b[2]};
    VEC vc[3];
    
    Vec_Cross(va, vb, vc);
    
    c[0] = vc[0];
    c[1] = vc[1];
    c[2] = vc[2];
}

/*!
    \brief      Vector dot product
    \param[in]  a: vector a
    \param[in]  b: vector b
    \param[out] none
    \retval     dot product result
*/
float nav_math_dot_product(const float a[3], const float b[3])
{
    VEC va[3] = {a[0], a[1], a[2]};
    VEC vb[3] = {b[0], b[1], b[2]};
    SCAL result;
    
    Vec_Dot(va, vb, &result);
    
    return (float)result;
}

/*!
    \brief      Matrix multiplication
    \param[in]  a: matrix a (m×n)
    \param[in]  b: matrix b (n×p)
    \param[out] c: result matrix c (m×p)
    \param[in]  m: rows of matrix a
    \param[in]  n: columns of matrix a, rows of matrix b
    \param[in]  p: columns of matrix b
    \retval     none
*/
void nav_math_matrix_multiply(const float *a, const float *b, float *c, int m, int n, int p)
{
    Mat_Mul((MATR*)a, (MATR*)b, (MATR*)c, m, n, p);
}

/*!
    \brief      Matrix transpose
    \param[in]  a: input matrix (m×n)
    \param[out] b: output matrix (n×m)
    \param[in]  m: rows of input matrix
    \param[in]  n: columns of input matrix
    \retval     none
*/
void nav_math_matrix_transpose(const float *a, float *b, int m, int n)
{
    Mat_Tr((MATR*)a, (MATR*)b, m, n);
}

/*!
    \brief      Matrix inverse
    \param[in]  a: input matrix (n×n)
    \param[out] b: output matrix (n×n)
    \param[in]  n: matrix dimension
    \retval     nav_math_status_t
*/
nav_math_status_t nav_math_matrix_inverse(const float *a, float *b, int n)
{
    if (n > MAX_MAT_DIM) {
        return NAV_MATH_STATUS_ERROR;
    }
    
    Mat_Inv((MATR*)a, (MATR*)b, n);
    
    return NAV_MATH_STATUS_OK;
}

/*!
    \brief      Quaternion multiplication
    \param[in]  q1: quaternion 1
    \param[in]  q2: quaternion 2
    \param[out] q3: result quaternion q3 = q1 * q2
    \retval     none
*/
void nav_math_quaternion_multiply(const float q1[4], const float q2[4], float q3[4])
{
    QUAT qa[4] = {q1[0], q1[1], q1[2], q1[3]};
    QUAT qb[4] = {q2[0], q2[1], q2[2], q2[3]};
    QUAT qc[4];
    
    Qua_Mul(qa, qb, qc);
    
    q3[0] = qc[0];
    q3[1] = qc[1];
    q3[2] = qc[2];
    q3[3] = qc[3];
}

/*!
    \brief      Convert quaternion to rotation matrix
    \param[in]  q: quaternion [w, x, y, z]
    \param[out] R: rotation matrix (3×3)
    \retval     none
*/
void nav_math_quaternion_to_matrix(const float q[4], float R[9])
{
    QUAT quat[4] = {q[0], q[1], q[2], q[3]};
    MATR cnb[9];
    
    QToCnb(quat, cnb);
    
    for (int i = 0; i < 9; i++) {
        R[i] = cnb[i];
    }
}

/*!
    \brief      Convert rotation matrix to quaternion
    \param[in]  R: rotation matrix (3×3)
    \param[out] q: quaternion [w, x, y, z]
    \retval     none
*/
void nav_math_matrix_to_quaternion(const float R[9], float q[4])
{
    MATR cnb[9];
    QUAT quat[4];
    
    for (int i = 0; i < 9; i++) {
        cnb[i] = R[i];
    }
    
    CnbToQ(cnb, quat);
    
    q[0] = quat[0];
    q[1] = quat[1];
    q[2] = quat[2];
    q[3] = quat[3];
}

/*!
    \brief      Convert Euler angles to rotation matrix
    \param[in]  euler: Euler angles [roll, pitch, yaw] in radians
    \param[out] R: rotation matrix (3×3)
    \retval     none
*/
void nav_math_euler_to_matrix(const float euler[3], float R[9])
{
    ATTI atti[3] = {euler[2], euler[1], euler[0]}; // [yaw, pitch, roll]
    MATR cnb[9];
    
    AttiToCnb(atti, cnb);
    
    for (int i = 0; i < 9; i++) {
        R[i] = cnb[i];
    }
}

/*!
    \brief      Convert rotation matrix to Euler angles
    \param[in]  R: rotation matrix (3×3)
    \param[out] euler: Euler angles [roll, pitch, yaw] in radians
    \retval     none
*/
void nav_math_matrix_to_euler(const float R[9], float euler[3])
{
    MATR cnb[9];
    ATTI atti[3];
    
    for (int i = 0; i < 9; i++) {
        cnb[i] = R[i];
    }
    
    CnbToAtti(cnb, atti);
    
    euler[0] = atti[2]; // roll
    euler[1] = atti[1]; // pitch
    euler[2] = atti[0]; // yaw
}
