/**
  ******************************************************************************
  * @file    HAL_SYS.h
  * <AUTHOR>
  * @brief   <PERSON><PERSON> file for the HAL_SYS.c file.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) <PERSON>.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by <PERSON> pravite license
  * the "License"; You may not use this file except in compliance with
  * the License. 
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef ___BSP_SYS_H
#define ___BSP_SYS_H


#ifdef __cplusplus
 extern "C" {
#endif

#define USE_CHIP_STM32		1
#define USE_CHIP_GD32		2

#define CHIP_USED		USE_CHIP_GD32

/* Includes ------------------------------------------------------------------*/
#include "gd32f4xx.h"
#include "core_cm4.h"

/*************************************
*  System version information
*************************************/
#define SYSVersion_MAIN         (0x01)
#define SYSVersion_SUB1         (0x00)
#define SYSVersion_SUB2         (0x00)
#define SYSVersion_RC           (0x00)

#define SoftwareVersion         ( ( SYSVersion_MAIN <<24 ) | ( SYSVersion_SUB1 <<16 ) | ( SYSVersion_SUB2 <<8 ) | SYSVersion_RC )

#if CODE_USE_OS == CODE_USE_OS_NONE
    
#elif CODE_USE_OS == CODE_USE_OS_UCOS
#include "includes.h"
#elif CODE_USE_OS == CODE_USE_OS_FREERTOS
    #if USE_STM32CubeMX
        #include "cmsis_os.h"
    #else
        #include "FreeRTOS.h"
        #include "task.h"
        #include "timers.h"
        #include "queue.h"
        #include "semphr.h"
        #include "event_groups.h"
    #endif
#endif

/** @addtogroup ArthurXu
  * @{
  */

/** @defgroup 
  * @brief This file is the header file for HAL_SYS.c
  * @{
  */


/** @defgroup Exported_Defines
  * @{
  */
#ifdef BSP_SYS_Global
#define BSP_SYSExt
#else 
#define BSP_SYSExt extern
#endif 



/**
  * @}
  */


/** @defgroup Exported_TypesDefinitions
  * @{
  */
typedef int32_t  s32;
typedef int16_t s16;
typedef int8_t  s8;

typedef const int32_t sc32;  
typedef const int16_t sc16;  
typedef const int8_t sc8;  

typedef __IO int32_t  vs32;
typedef __IO int16_t  vs16;
typedef __IO int8_t   vs8;

typedef __I int32_t vsc32;  
typedef __I int16_t vsc16; 
typedef __I int8_t vsc8;   

typedef uint32_t  u32;
typedef uint16_t u16;
typedef uint8_t  u8;

typedef const uint32_t uc32;  
typedef const uint16_t uc16;  
typedef const uint8_t uc8; 

typedef __IO uint32_t  vu32;
typedef __IO uint16_t vu16;
typedef __IO uint8_t  vu8;

typedef __I uint32_t vuc32;  
typedef __I uint16_t vuc16; 
typedef __I uint8_t vuc8; 
/**
  * @}
  */



/** @defgroup Exported_Macros
  * @{
  */
#define JTAG_SWD_DISABLE   0X02
#define SWD_ENABLE         0X01
#define JTAG_SWD_ENABLE    0X00

#define Cache_Write_Through() (*(__IO uint32_t*)0XE000EF9C=1UL<<2) //Cache 透写

/**
  * @}
  */

/** @defgroup Exported_Variables
  * @{
  */
#if CODE_USE_OS == CODE_USE_OS_NONE
    
#elif CODE_USE_OS == CODE_USE_OS_UCOS
BSP_SYSExt OS_ERR err;
#elif CODE_USE_OS == CODE_USE_OS_FREERTOS

#endif

/**
  * @}
  */

/** @defgroup Exported_Functions
  * @{
  */

BSP_SYSExt void Cache_Enable(void);
BSP_SYSExt u8 IsICahceEnable(void);
BSP_SYSExt u8 IsDCahceEnable(void);
BSP_SYSExt u32 GetSoftwareVersion(void);
BSP_SYSExt void Sys_Soft_Reset(void);
BSP_SYSExt void Error_Handler(void);
BSP_SYSExt void assert_failed(uint8_t *file, uint32_t line);

#if defined(__clang__) //(for clang)
BSP_SYSExt void __attribute__((noinline)) WFI_SET(void);
BSP_SYSExt void __attribute__((noinline)) INTX_DISABLE(void);
BSP_SYSExt void __attribute__((noinline)) INTX_ENABLE(void);
BSP_SYSExt void __attribute__((noinline)) MSR_MSP(u32 addr);
#elif defined (__CC_ARM)    //(for ARMCC)
//Assemblor function
BSP_SYSExt void WFI_SET(void);		//WFI
BSP_SYSExt void INTX_DISABLE(void);//Disable all interrupt
BSP_SYSExt void INTX_ENABLE(void);	//Enable all interrupt
BSP_SYSExt void MSR_MSP(u32 addr);	//Set Heap address
#endif


#if CHIP_USED == USE_CHIP_STM32

void delay_us(u32 nus);
void delay_init();
void delay_ms(u16 nms);


#endif


/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif  /* ___BSP_SYS_H */
/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Arthur Xu *****END OF FILE****/


