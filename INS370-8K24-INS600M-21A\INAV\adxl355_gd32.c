/*!
    \file    adxl355_gd32.c
    \brief   ADXL355 sensor driver for GD32F4xx platform
    \note    Adapted from HPM6750 version for GD32F4xx compatibility
*/

#include "board.h"
#include "ins.h"
#include "bsp_uart.h"
#include "bsp_gpio.h"
#include "bsp_tim.h"
#include <string.h>
#include <stdio.h>

/* ADXL355 UART configuration for GD32F4xx */
#define ADXL355_UART                USART2
#define ADXL355_UART_CLK            RCU_USART2
#define ADXL355_UART_TX_PIN         GPIO_PIN_10
#define ADXL355_UART_RX_PIN         GPIO_PIN_11
#define ADXL355_UART_GPIO_PORT      GPIOB
#define ADXL355_UART_GPIO_CLK       RCU_GPIOB
#define ADXL355_UART_AF             GPIO_AF_7

/* ADXL355 protocol states */
typedef enum
{
    RCV_STATE_PID = 1,
    RCV_STATE_CMD = 2,
    RCV_STATE_LEN = 3,
    RCV_STATE_DAT = 4,
    RCV_STATE_CRC1 = 5,
    RCV_STATE_CRC2 = 6
} ADXL355_PRO_STATE;

/* ADXL355 data structure */
typedef union
{
    int32_t datai;
    uint8_t buf[4];
} ADXL355_UNION;

/* Global variables */
bool adxl355_is_running = false;
double adxl355_ax = 0.0;
double adxl355_ay = 0.0;
double adxl355_az = 0.0;

/* Private variables */
static uint8_t uart_rx_buf[15];
static uint8_t rx_data = 0;
static uint8_t rx_num = 0;
static uint16_t crc16_cal = 0;
static uint16_t crc16_rcv = 0;
static ADXL355_PRO_STATE rcv_state = RCV_STATE_PID;
static ADXL355_UNION temp_union;
static uint16_t adxl_data[12];

/* ADXL355 command */
static uint8_t ADXL355_CMD[8] = {0x00, 0x06, 0x00, 0xff, 0x00, 0x0a, 0x38, 0x2c};

/* Function prototypes */
static uint16_t adxl355_calculate_crc(uint8_t *data, uint16_t length);
static void adxl355_process_data(void);

/*!
    \brief      initialize ADXL355 UART
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ADXL355_UART_Init(void)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable(ADXL355_UART_GPIO_CLK);
    /* enable USART clock */
    rcu_periph_clock_enable(ADXL355_UART_CLK);

    /* configure USART Tx as alternate function push-pull */
    gpio_af_set(ADXL355_UART_GPIO_PORT, ADXL355_UART_AF, ADXL355_UART_TX_PIN);
    gpio_mode_set(ADXL355_UART_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, ADXL355_UART_TX_PIN);
    gpio_output_options_set(ADXL355_UART_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, ADXL355_UART_TX_PIN);

    /* configure USART Rx as alternate function push-pull */
    gpio_af_set(ADXL355_UART_GPIO_PORT, ADXL355_UART_AF, ADXL355_UART_RX_PIN);
    gpio_mode_set(ADXL355_UART_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, ADXL355_UART_RX_PIN);
    gpio_output_options_set(ADXL355_UART_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, ADXL355_UART_RX_PIN);

    /* USART configure */
    usart_deinit(ADXL355_UART);
    usart_word_length_set(ADXL355_UART, USART_WL_8BIT);
    usart_stop_bit_set(ADXL355_UART, USART_STB_1BIT);
    usart_parity_config(ADXL355_UART, USART_PM_NONE);
    usart_baudrate_set(ADXL355_UART, 115200U);
    usart_receive_config(ADXL355_UART, USART_RECEIVE_ENABLE);
    usart_transmit_config(ADXL355_UART, USART_TRANSMIT_ENABLE);
    usart_enable(ADXL355_UART);

    /* clear receive buffer */
    memset(uart_rx_buf, 0, sizeof(uart_rx_buf));
    rx_num = 0;
    rcv_state = RCV_STATE_PID;
}

/*!
    \brief      send data to ADXL355
    \param[in]  txbuf: pointer to transmit buffer
    \param[in]  size: data size
    \param[out] none
    \retval     none
*/
void adxl355_send_data(uint8_t *txbuf, int size)
{
    for(int i = 0; i < size; i++)
    {
        usart_data_transmit(ADXL355_UART, txbuf[i]);
        while(RESET == usart_flag_get(ADXL355_UART, USART_FLAG_TBE));
    }
}

/*!
    \brief      send command to ADXL355
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adxl355_send_command(void)
{
    adxl355_send_data(ADXL355_CMD, sizeof(ADXL355_CMD));
}

/*!
    \brief      receive data from ADXL355 (polling mode)
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adxl355_receive_polling(void)
{
    /* check if data is available */
    if(RESET != usart_flag_get(ADXL355_UART, USART_FLAG_RBNE))
    {
        rx_data = (uint8_t)usart_data_receive(ADXL355_UART);
        
        switch(rcv_state)
        {
            case RCV_STATE_PID:
                if(0x00 == rx_data)
                {
                    rx_num = 0;
                    memset(uart_rx_buf, 0, sizeof(uart_rx_buf));
                    uart_rx_buf[rx_num] = rx_data;
                    rcv_state = RCV_STATE_CMD;
                }
                else
                {
                    rx_num = 0;
                    memset(uart_rx_buf, 0, sizeof(uart_rx_buf));
                    rcv_state = RCV_STATE_PID;
                }
                break;

            case RCV_STATE_CMD:
                if(0x03 == rx_data)
                {
                    rx_num++;
                    uart_rx_buf[rx_num] = rx_data;
                    rcv_state = RCV_STATE_LEN;
                }
                else
                {
                    rx_num = 0;
                    memset(uart_rx_buf, 0, sizeof(uart_rx_buf));
                    rcv_state = RCV_STATE_PID;
                }
                break;

            case RCV_STATE_LEN:
                if(0x09 == rx_data)
                {
                    rx_num++;
                    uart_rx_buf[rx_num] = rx_data;
                    rcv_state = RCV_STATE_DAT;
                }
                else
                {
                    rx_num = 0;
                    memset(uart_rx_buf, 0, sizeof(uart_rx_buf));
                    rcv_state = RCV_STATE_PID;
                }
                break;

            case RCV_STATE_DAT:
                if(rx_num < 10)
                {
                    rx_num++;
                    uart_rx_buf[rx_num] = rx_data;
                    rcv_state = RCV_STATE_DAT;
                }
                else
                {
                    rx_num++;
                    uart_rx_buf[rx_num] = rx_data;
                    crc16_cal = adxl355_calculate_crc(uart_rx_buf, rx_num + 1);
                    rcv_state = RCV_STATE_CRC1;
                }
                break;

            case RCV_STATE_CRC1:
                rx_num++;
                uart_rx_buf[rx_num] = rx_data;
                rcv_state = RCV_STATE_CRC2;
                break;

            case RCV_STATE_CRC2:
                rx_num++;
                uart_rx_buf[rx_num] = rx_data;
                crc16_rcv = uart_rx_buf[rx_num] << 8 | uart_rx_buf[rx_num - 1];
                if(crc16_rcv == crc16_cal)
                {
                    adxl355_process_data();
                    adxl355_is_running = true;
                }
                rcv_state = RCV_STATE_PID;
                break;

            default:
                rx_num = 0;
                rcv_state = RCV_STATE_PID;
                break;
        }
    }
}

/*!
    \brief      process received ADXL355 data
    \param[in]  none
    \param[out] none
    \retval     none
*/
static void adxl355_process_data(void)
{
    uint32_t data3, data2, data1, datau;
    int32_t datai;

    /* process X-axis data */
    data3 = (uint32_t)uart_rx_buf[3];
    data2 = (uint32_t)uart_rx_buf[4];
    data1 = (uint32_t)uart_rx_buf[5];
    datau = data3 << 12 | data2 << 4 | data1 >> 4;
    if(datau & 0x80000)
        datai = datau | 0xfff00000;
    else
        datai = datau;
    
    adxl355_ax = datai / 64000.0;
    temp_union.datai = datai;
    adxl_data[0] = temp_union.buf[0];
    adxl_data[1] = temp_union.buf[1];

    /* process Y-axis data */
    data3 = (uint32_t)uart_rx_buf[6];
    data2 = (uint32_t)uart_rx_buf[7];
    data1 = (uint32_t)uart_rx_buf[8];
    datau = data3 << 12 | data2 << 4 | data1 >> 4;
    if(datau & 0x80000)
        datai = datau | 0xfff00000;
    else
        datai = datau;
    
    adxl355_ay = datai / 64000.0;
    temp_union.datai = datai;
    adxl_data[2] = temp_union.buf[0];
    adxl_data[3] = temp_union.buf[1];

    /* process Z-axis data */
    data3 = (uint32_t)uart_rx_buf[9];
    data2 = (uint32_t)uart_rx_buf[10];
    data1 = (uint32_t)uart_rx_buf[11];
    datau = data3 << 12 | data2 << 4 | data1 >> 4;
    if(datau & 0x80000)
        datai = datau | 0xfff00000;
    else
        datai = datau;
    
    adxl355_az = datai / 64000.0;
    temp_union.datai = datai;
    adxl_data[4] = temp_union.buf[0];
    adxl_data[5] = temp_union.buf[1];
}

/*!
    \brief      calculate CRC16 for ADXL355 data
    \param[in]  data: pointer to data buffer
    \param[in]  length: data length
    \param[out] none
    \retval     CRC16 value
*/
static uint16_t adxl355_calculate_crc(uint8_t *data, uint16_t length)
{
    uint16_t crc = 0xFFFF;
    uint16_t polynomial = 0xA001;

    for(uint16_t i = 0; i < length; ++i)
    {
        crc ^= data[i];

        for(uint8_t j = 0; j < 8; ++j)
        {
            if(crc & 0x0001)
            {
                crc >>= 1;
                crc ^= polynomial;
            }
            else
            {
                crc >>= 1;
            }
        }
    }

    return crc;
}

/*!
    \brief      get ADXL355 acceleration data
    \param[out] ax: X-axis acceleration
    \param[out] ay: Y-axis acceleration  
    \param[out] az: Z-axis acceleration
    \retval     true if data is valid, false otherwise
*/
bool adxl355_get_acceleration(double *ax, double *ay, double *az)
{
    if(adxl355_is_running)
    {
        *ax = adxl355_ax;
        *ay = adxl355_ay;
        *az = adxl355_az;
        return true;
    }
    return false;
}

/*!
    \brief      check if ADXL355 is running
    \param[in]  none
    \param[out] none
    \retval     true if running, false otherwise
*/
bool adxl355_is_active(void)
{
    return adxl355_is_running;
}
