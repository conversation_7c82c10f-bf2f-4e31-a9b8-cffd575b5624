<!DOCTYPE CrossStudio_Project_File>
<solution Name="gpio_example" target="20" version="2">
  <configuration
    Name="Common"
    c_preprocessor_definitions="FLASH_XIP=1;INIT_EXT_RAM_FOR_DATA=1;HPMSOC_HAS_HPMSDK_MULTICORE=y;HPMSOC_HAS_HPMSDK_GPIO=y;HPMSOC_HAS_HPMSDK_PLIC=y;HPMSOC_HAS_HPMSDK_MCHTMR=y;HPMSOC_HAS_HPMSDK_PLICSW=y;HPMSOC_HAS_HPMSDK_GPIOM=y;HPMSOC_HAS_HPMSDK_ADC12=y;HPMSOC_HAS_HPMSDK_ADC16=y;HPMSOC_HAS_HPMSDK_ACMP=y;HPMSOC_HAS_HPMSDK_SPI=y;HPMSOC_HAS_HPMSDK_UART=y;HPMSOC_HAS_HPMSDK_CAN=y;HPMSOC_HAS_HPMSDK_WDG=y;HPMSOC_HAS_HPMSDK_MBX=y;HPMSOC_HAS_HPMSDK_PTPC=y;HPMSOC_HAS_HPMSDK_DMAMUX=y;HPMSOC_HAS_HPMSDK_DMA=y;HPMSOC_HAS_HPMSDK_RNG=y;HPMSOC_HAS_HPMSDK_KEYM=y;HPMSOC_HAS_HPMSDK_I2S=y;HPMSOC_HAS_HPMSDK_DAO=y;HPMSOC_HAS_HPMSDK_PDM=y;HPMSOC_HAS_HPMSDK_PWM=y;HPMSOC_HAS_HPMSDK_HALL=y;HPMSOC_HAS_HPMSDK_QEI=y;HPMSOC_HAS_HPMSDK_TRGM=y;HPMSOC_HAS_HPMSDK_SYNT=y;HPMSOC_HAS_HPMSDK_LCDC=y;HPMSOC_HAS_HPMSDK_CAM=y;HPMSOC_HAS_HPMSDK_PDMA=y;HPMSOC_HAS_HPMSDK_JPEG=y;HPMSOC_HAS_HPMSDK_ENET=y;HPMSOC_HAS_HPMSDK_GPTMR=y;HPMSOC_HAS_HPMSDK_USB=y;HPMSOC_HAS_HPMSDK_SDXC=y;HPMSOC_HAS_HPMSDK_CONCTL=y;HPMSOC_HAS_HPMSDK_I2C=y;HPMSOC_HAS_HPMSDK_SDP=y;HPMSOC_HAS_HPMSDK_FEMC=y;HPMSOC_HAS_HPMSDK_SYSCTL=y;HPMSOC_HAS_HPMSDK_IOC=y;HPMSOC_HAS_HPMSDK_OTP=y;HPMSOC_HAS_HPMSDK_PPOR=y;HPMSOC_HAS_HPMSDK_PCFG=y;HPMSOC_HAS_HPMSDK_PSEC=y;HPMSOC_HAS_HPMSDK_PMON=y;HPMSOC_HAS_HPMSDK_PGPR=y;HPMSOC_HAS_HPMSDK_VAD=y;HPMSOC_HAS_HPMSDK_PLLCTL=y;HPMSOC_HAS_HPMSDK_BPOR=y;HPMSOC_HAS_HPMSDK_BCFG=y;HPMSOC_HAS_HPMSDK_BUTN=y;HPMSOC_HAS_HPMSDK_BGPR=y;HPMSOC_HAS_HPMSDK_RTC=y;HPMSOC_HAS_HPMSDK_BSEC=y;HPMSOC_HAS_HPMSDK_BKEY=y;HPMSOC_HAS_HPMSDK_BMON=y;HPMSOC_HAS_HPMSDK_TAMP=y;HPMSOC_HAS_HPMSDK_MONO=y;HPMSOC_HAS_HPMSDK_PMP=y;SD_FATFS_ENABLE=1;HPM_SDMMC_HOST_ENABLE_IRQ=1;BOARD_APP_SDCARD_HOST_USING_IRQ=1"
    debug_cpu_registers_file="..\..\..\..\..\hpm_sdk\soc\HPM6700\HPM6750\hpm_ses_riscv_cpu_regs.xml"
    debug_register_definition_file="..\..\..\..\..\hpm_sdk\soc\HPM6700\HPM6750\hpm_ses_reg.xml"
    debug_restrict_memory_access="No"
    gdb_server_write_timeout="300"
    link_symbol_definitions="_flash_size=16M;_extram_size=32M;" />
  <configuration
    Name="Debug"
    c_preprocessor_definitions="DEBUG"
    gcc_debugging_level="Level 3"
    gcc_optimization_level="None"
    gdb_server_allow_memory_access_during_execution="Yes"
    gdb_server_ignore_checksum_errors="No"
    gdb_server_register_access="General and Individual" />
  <configuration
    Name="Release"
    c_preprocessor_definitions="NDEBUG"
    gcc_debugging_level="None"
    gcc_omit_frame_pointer="Yes"
    gcc_optimization_level="Level 1" />
  <project Name="gpio_example - hpm6750">
    <configuration
      LIBRARY_IO_TYPE="STD"
      Name="Common"
      RISCV_TOOLCHAIN_VARIANT="Standard"
      arm_linker_heap_size="0x4000"
      arm_linker_no_warn_on_mismatch="Yes"
      arm_linker_stack_size="0x4000"
      arm_linker_variant="SEGGER"
      arm_rtl_variant="SEGGER"
      build_generic_options_file_name=""
      build_output_file_name="$(OutDir)/demo$(EXE)"
      c_additional_options=""
      c_user_include_directories="../../../../../hpm_sdk/arch;../../../../../hpm_sdk/arch/riscv/l1c;../../../../boards/hpm6750;../../../../../hpm_sdk/soc/HPM6700/HPM6750;../../../../../hpm_sdk/soc/HPM6700/ip;../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains;../../../../../hpm_sdk/soc/HPM6700/HPM6750/boot;../../../../../hpm_sdk/drivers/inc;../../../../../hpm_sdk/utils;../../../../../hpm_sdk/components/debug_console;../../../../../hpm_sdk/middleware/fatfs/src/common;../../../../../hpm_sdk/middleware/fatfs/src/portable;../../../../../hpm_sdk/middleware/fatfs/src/portable/sdxc;../../../../../hpm_sdk/middleware/hpm_sdmmc/lib;../../../../../hpm_sdk/middleware/hpm_sdmmc/lib;../../../../../hpm_sdk/middleware/hpm_sdmmc/port;../build_tmp/generated/include;../../src;../../src/bsp/inc;../../src/Common/inc;../../src/INAV;../../src/NAV;../../src/Protocol;../../src/RTT;../../src/Source/Edwoy;../../src/Source/inc"
      debug_target_connection="GDB Server"
      gcc_all_warnings_command_line_options="-Wall;-Wextra;-Wno-format"
      gcc_cplusplus_language_standard="c++11"
      gcc_enable_all_warnings="Yes"
      gdb_server_autostart_server="Yes"
      gdb_server_command_line="E:/2014902/HPM6750/sdk_env_v1.6.0/tools/openocd/openocd.exe -f $(ProjectDir)/../../../../../hpm_sdk/boards/openocd/probes/cmsis_dap.cfg -f $(ProjectDir)/../../../../../hpm_sdk/boards/openocd/soc/hpm6750-dual-core.cfg"
      gdb_server_port="3333"
      gdb_server_reset_command="reset halt"
      gdb_server_type="Custom"
      heap_size="0x4000"
      libcxx="Yes"
      link_linker_script_file="..\..\..\..\..\hpm_sdk\soc\HPM6700\HPM6750\toolchains\segger\flash_xip.icf"
      linker_additional_files="../../../../../hpm_sdk/middleware/hpm_math/nds_dsp/gcc/libdspd.a"
      linker_output_format="bin"
      linker_printf_fmt_level="int"
      linker_printf_fp_enabled="Double"
      linker_printf_wchar_enabled="No"
      linker_printf_width_precision_supported="Yes"
      linker_scanf_character_group_matching_enabled="No"
      linker_scanf_fmt_level="int"
      linker_scanf_fp_enabled="No"
      post_build_command="&quot;$(OBJDUMP)&quot; -S -d &quot;$(OutDir)/demo$(EXE)&quot; &gt; &quot;$(OutDir)/demo.asm&quot;"
      project_directory=""
      project_type="Executable"
      rv_abi="ilp32d"
      rv_arch_ext=""
      rv_arch_zicsr="Yes"
      rv_arch_zifencei="Yes"
      rv_architecture="rv32gc"
      rv_debug_extensions="None"
      rv_toolchain_prefix=""
      stack_size="0x4000"
      target_device_name="HPM6750xVMx" />
    <configuration
      Name="Debug"
      debug_target_connection="J-Link"
      gcc_optimization_level="None"
      heap_size="16384"
      link_linker_script_file="$(ProjectDir)/flash_xip.icf"
      linker_printf_fp_enabled="Double"
      rv_abi="ilp32d"
      rv_architecture="rv32gc" />
    <folder Name="application">
      <configuration Name="Debug" default_code_section=".fast" />
      <folder Name="bsp">
        <folder Name="src">
          <file file_name="../../src/bsp/src/bsp_fmc.c" />
          <file file_name="../../src/bsp/src/bsp_gpio.c" />
          <file file_name="../../src/bsp/src/bsp_tim.c" />
          <file file_name="../../src/bsp/src/Logger.c" />
        </folder>
      </folder>
      <folder Name="Common">
        <folder Name="src">
          <file file_name="../../src/Common/src/data_convert.c" />
        </folder>
      </folder>
      <folder Name="INAV">
        <file file_name="../../src/INAV/adxl355.c" />
        <file file_name="../../src/INAV/align.c" />
        <file file_name="../../src/INAV/AnnTempCompen.c" />
        <file file_name="../../src/INAV/dynamic_align.c" />
        <file file_name="../../src/INAV/kalman.c" />
        <file file_name="../../src/INAV/matvecmath.c" />
        <file file_name="../../src/INAV/navi.c" />
        <file file_name="../../src/INAV/private_math.c" />
        <file file_name="../../src/INAV/read_and_check_gnss_data.c" />
        <file file_name="../../src/INAV/readpaoche.c" />
      </folder>
      <folder Name="NAV">
        <file file_name="../../src/NAV/nav_cli.c" />
      </folder>
      <folder Name="Protocol">
        <file file_name="../../src/Protocol/computerFrameParse.c" />
        <file file_name="../../src/Protocol/frame_analysis.c" />
        <file file_name="../../src/Protocol/InsTestingEntry.c" />
        <file file_name="../../src/Protocol/protocol.c" />
      </folder>
      <folder Name="RTT">
        <file file_name="../../src/RTT/SEGGER_RTT.c" />
        <file file_name="../../src/RTT/SEGGER_RTT_printf.c" />
      </folder>
      <folder Name="Source">
        <folder Name="Edwoy">
          <file file_name="../../src/Source/Edwoy/app_tool.c" />
          <file file_name="../../src/Source/Edwoy/pjt_board.c" />
          <file file_name="../../src/Source/Edwoy/sensor_misc.c" />
        </folder>
        <folder Name="src">
          <file file_name="../../src/Source/src/api_ch392.c" />
          <file file_name="../../src/Source/src/can_data.c" />
          <file file_name="../../src/Source/src/clock.c" />
          <file file_name="../../src/Source/src/Data_shift.c" />
          <file file_name="../../src/Source/src/datado.c" />
          <file file_name="../../src/Source/src/FirmwareUpdateFile.c" />
          <file file_name="../../src/Source/src/fpgad.c" />
          <file file_name="../../src/Source/src/gd32f4xx_it.c" />
          <file file_name="../../src/Source/src/gdwatch.c" />
          <file file_name="../../src/Source/src/imu_data.c" />
          <file file_name="../../src/Source/src/INS_Data.c" />
          <file file_name="../../src/Source/src/INS_Init.c" />
          <file file_name="../../src/Source/src/INS_Output.c" />
          <file file_name="../../src/Source/src/INS_Sys.c" />
          <file file_name="../../src/Source/src/SetParaBao.c" />
          <file file_name="../../src/Source/src/systick.c" />
          <file file_name="../../src/Source/src/Time_Unify.c" />
        </folder>
      </folder>
      <folder Name="src">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/application/src" />
        <file file_name="../../src/flash.c" />
        <file file_name="../../src/main.c" />
        <file file_name="../../src/sd_fatfs.c" />
        <file file_name="../../src/sdram.c" />
        <file file_name="../../src/timer.c" />
        <file file_name="../../src/uart.c" />
        <file file_name="../../src/uart_dma.c" />
      </folder>
    </folder>
    <folder Name="arch">
      <folder Name="riscv">
        <folder Name="l1c">
          <file file_name="../../../../../hpm_sdk/arch/riscv/l1c/hpm_l1c_drv.c" />
        </folder>
      </folder>
    </folder>
    <folder Name="boards">
      <folder Name="hpm6750">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/boards/hpm6750" />
        <file file_name="..\..\..\..\boards\hpm6750\board.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/board.c$(OBJ)" />
        </file>
        <file file_name="..\..\..\..\boards\hpm6750\pinmux.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/pinmux.c$(OBJ)" />
        </file>
      </folder>
    </folder>
    <folder Name="components">
      <folder Name="debug_console">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/components/debug_console" />
        <file file_name="../../../../../hpm_sdk/components/debug_console/hpm_debug_console.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_debug_console.c$(OBJ)" />
        </file>
      </folder>
    </folder>
    <folder Name="drivers">
      <folder Name="src">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/drivers/src" />
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_acmp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_acmp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_adc12_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_adc12_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_adc16_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_adc16_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_cam_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_cam_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_can_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_can_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_dao_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_dao_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_dma_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_dma_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_enet_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_enet_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_femc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_femc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_gpio_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_gpio_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_gptmr_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_gptmr_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_i2c_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_i2c_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_i2s_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_i2s_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_jpeg_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_jpeg_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_lcdc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_lcdc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_mchtmr_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_mchtmr_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_pcfg_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pcfg_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_pdm_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pdm_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_pdma_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pdma_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_pllctl_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pllctl_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_pmp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pmp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_ptpc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_ptpc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_pwm_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pwm_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_rng_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_rng_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_rtc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_rtc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_sdp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_sdp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_sdxc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_sdxc_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_spi_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_spi_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_tamp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_tamp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_uart_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_uart_drv.c$(OBJ)" />
          <configuration Name="Debug" default_code_section=".fast" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_usb_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_usb_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_vad_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_vad_drv.c$(OBJ)" />
        </file>
        <file file_name="../../../../../hpm_sdk/drivers/src/hpm_wdg_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_wdg_drv.c$(OBJ)" />
        </file>
      </folder>
    </folder>
    <folder Name="middleware">
      <configuration Name="Debug" default_code_section=".fast" />
      <folder Name="fatfs">
        <folder Name="src">
          <folder Name="common">
            <configuration
              Name="Common"
              build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/middleware/fatfs/src/common" />
            <file file_name="../../../../../hpm_sdk/middleware/fatfs/src/common/ff.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/ff.c$(OBJ)" />
            </file>
            <file file_name="../../../../../hpm_sdk/middleware/fatfs/src/common/ffunicode.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/ffunicode.c$(OBJ)" />
            </file>
          </folder>
          <folder Name="portable">
            <configuration
              Name="Common"
              build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/middleware/fatfs/src/portable" />
            <folder Name="sdxc">
              <configuration
                Name="Common"
                build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/middleware/fatfs/src/portable/sdxc" />
              <file file_name="../../../../../hpm_sdk/middleware/fatfs/src/portable/sdxc/hpm_sdmmc_disk.c">
                <configuration
                  Name="Common"
                  build_object_file_name="$(IntDir)/hpm_sdmmc_disk.c$(OBJ)" />
                <configuration Name="Debug" default_const_section=".rodata" />
              </file>
            </folder>
            <file file_name="../../../../../hpm_sdk/middleware/fatfs/src/portable/diskio.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/diskio.c$(OBJ)" />
            </file>
            <file file_name="../../../../../hpm_sdk/middleware/fatfs/src/portable/ff_queue.c">
              <configuration Name="Debug" default_const_section=".rodata" />
            </file>
          </folder>
        </folder>
      </folder>
      <folder Name="hpm_sdmmc">
        <folder Name="lib">
          <configuration
            Name="Common"
            build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/middleware/hpm_sdmmc/lib" />
          <file file_name="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_common.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sdmmc_common.c$(OBJ)" />
          </file>
          <file file_name="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_emmc.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sdmmc_emmc.c$(OBJ)" />
          </file>
          <file file_name="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_host.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sdmmc_host.c$(OBJ)" />
          </file>
          <file file_name="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_osal.c" />
          <file file_name="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_sd.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sdmmc_sd.c$(OBJ)" />
          </file>
        </folder>
        <folder Name="port">
          <configuration
            Name="Common"
            build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/middleware/hpm_sdmmc/port" />
          <file file_name="../../../../../hpm_sdk/middleware/hpm_sdmmc/port/hpm_sdmmc_port.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sdmmc_port.c$(OBJ)" />
          </file>
        </folder>
      </folder>
    </folder>
    <folder Name="soc">
      <folder Name="HPM6700">
        <folder Name="HPM6750">
          <configuration
            Name="Common"
            build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM6700/HPM6750" />
          <folder Name="boot">
            <configuration
              Name="Common"
              build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM6700/HPM6750/boot" />
            <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/boot/hpm_bootheader.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/hpm_bootheader.c$(OBJ)" />
            </file>
          </folder>
          <folder Name="toolchains">
            <configuration
              Name="Common"
              build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM6700/HPM6750/toolchains" />
            <folder Name="segger">
              <configuration
                Name="Common"
                build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM6700/HPM6750/toolchains/segger" />
              <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/segger/startup.s">
                <configuration
                  Name="Common"
                  build_object_file_name="$(IntDir)/startup.s$(OBJ)" />
              </file>
            </folder>
            <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/reset.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/reset.c$(OBJ)" />
            </file>
            <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/trap.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/trap.c$(OBJ)" />
            </file>
          </folder>
          <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_clock_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_clock_drv.c$(OBJ)" />
          </file>
          <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_otp_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_otp_drv.c$(OBJ)" />
          </file>
          <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_sysctl_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sysctl_drv.c$(OBJ)" />
          </file>
          <file file_name="../../../../../hpm_sdk/soc/HPM6700/HPM6750/system.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/system.c$(OBJ)" />
          </file>
        </folder>
      </folder>
    </folder>
    <folder Name="utils">
      <configuration
        Name="Common"
        build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/utils" />
      <file file_name="../../../../../hpm_sdk/utils/hpm_crc32.c">
        <configuration
          Name="Common"
          build_object_file_name="$(IntDir)/hpm_crc32.c$(OBJ)" />
      </file>
      <file file_name="../../../../../hpm_sdk/utils/hpm_ffssi.c">
        <configuration
          Name="Common"
          build_object_file_name="$(IntDir)/hpm_ffssi.c$(OBJ)" />
      </file>
      <file file_name="../../../../../hpm_sdk/utils/hpm_swap.c">
        <configuration
          Name="Common"
          build_object_file_name="$(IntDir)/hpm_swap.c$(OBJ)" />
      </file>
    </folder>
  </project>
</solution>
