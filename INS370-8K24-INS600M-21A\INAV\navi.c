/*****************************************************文件说明******************************************************************************/
/*文件名称：NAVI.C                                                                                                                         */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                */
/*编写人：                                                                                                                             */
/*包含文件：const.h、typedefine.h、math.h、DATASTRUCT.h、EXTERNGLOBALDATA.h、FUNCTION.h、memory.h                                          */
/*测试用例：由GNSSlocusGen.m文件生成整套软件测试用例，单个模块测试用例暂缺                                                                  */
/*说明：本文件包含了程序中用到的惯性导航解算函数。（此文件为初始测试版本，文件中所定义函数仅供程序设计人员参考选用）                       */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include <math.h>
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include <string.h>
#include <EXTERNGLOBALDATA.h>
/*********************************************函数说明*******************************************************/
/*函数名称：SysVarDefaultSet                                                                                */
/*函数功能描述：系统变量默认设置函数                                                                         */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void SysVarDefaultSet(p_SysVar lp_SysVar)
{
    //系统变量默认设置值
    lp_SysVar -> WorkPhase = PHASE_STANDBY;              //系统工作阶段默认设置：参见CONST.h
    lp_SysVar -> Time = 0.0;                             //系统起始工作时间默认设置：参见CONST.h
    //系统状态变量初始化
    g_SysVar.isGNSSValid = NO;
    g_SysVar.isGNSS_Update = NO;
	  g_SysVar.isPPSStart = NO;
	  g_SysVar.isRapidAlign = NO;
	  g_SysVar.isFineAlign = NO;
    g_SysVar.isDampedOK = NO; 	
    g_InitBind.isBind = NO;
    g_InitBind.isHeadBind = NO;	
}

/*********************************************函数说明*******************************************************/
/*函数名称：BindDefaultSet_by_GNSS                                                                          */
/*函数功能描述：初始装订默认设置函数                                                                         */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针：lp_InitBind                                                                               */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void BindDefaultSet_by_GNSS(p_InitBind lp_InitBind,p_GNSSData lp_GNSSData)
{

    lp_InitBind -> r_InitLati = lp_GNSSData -> r_GNSSLati;    //初始纬度默认值设置
        
    lp_InitBind -> r_InitLogi = lp_GNSSData -> r_GNSSLogi;    //初始经度默认值设置
        
    lp_InitBind -> InitHeight = lp_GNSSData -> GNSSHeight;    //初始高度默认值设置
        
    lp_InitBind -> InitVn[0] = DEFAULT_VN;                    //北向速度默认值设置
        
    lp_InitBind -> InitVn[1] = DEFAULT_VU;                    //天向速度默认值设置
        
    lp_InitBind -> InitVn[2] = DEFAULT_VE;                    //东向速度默认值设置

    lp_InitBind -> isBind = YES;

    if(lp_GNSSData ->isHeadingEn == YES)
    {
        lp_InitBind -> r_InitHead = lp_GNSSData -> r_GPSHead;
        lp_InitBind -> isHeadBind = YES;
    }
}





/*********************************************函数说明*******************************************************/
/*函数名称：Sys_Init                                                                                        */
/*函数功能描述：系统初始化函数（封装函数）                                                                   */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void SysInit()
{  
    //先将程序中所有全局变量清零
    memset(&g_SysVar, 0, sizeof(SysVar));         //将g_SysVar结构体成员变量全部清零
    memset(&g_SelfTest, 0, sizeof(SelfTest));     
    memset(&g_InitBind, 0, sizeof(InitBind));     //将g_InitBind结构体成员变量全部清零
    memset(&g_Align, 0, sizeof(Align));           //将g_Align结构体成员变量全部清零
    memset(&g_Navi, 0, sizeof(Navi));             //将g_Navi结构体成员变量全部清零
    memset(&g_Kalman, 0, sizeof(Kalman));         //将g_Kalman结构体成员变量全部清零
    memset(&g_GNSSData_In_Use, 0, sizeof(GNSSData));     //将g_GNSSData结构体成员变量全部清零
    memset(&g_Compen, 0, sizeof(Compen));         //将g_Compen结构体成员变量全部清零
    memset(&g_InertialSysAlign, 0, sizeof(InertialSysAlign)); //将g_Compen结构体成员变量全部清零
    SysVarDefaultSet(&g_SysVar);                  //系统变量默认设置
    //ANNCompen_Init();
    
    g_Compen.AccLeverArm[0] = 0.0;
    g_Compen.AccLeverArm[4] = 0.0; 

    g_Compen.AccLeverArm[6] = 0.0;
    g_Compen.AccLeverArm[7] = 0.0;
    g_Compen.AccLeverArm[8] = 0;
	
	  g_SysVar.Arm_GNSSToINS_b[0] = -0.7624;
		g_SysVar.Arm_GNSSToINS_b[1] = 0.0438;
		g_SysVar.Arm_GNSSToINS_b[2] = 0.011;
    Kalman_Init(&g_Kalman);
		
		//初始对准方法默认设置为静基座自寻北
		g_SysVar.isRapidAlign = NO;
		g_SysVar.isFineAlign = NO;
}




/*****************************************************************************************************************************
// 函数名称： Sys_Fail_Work
// 功能描述： 
// 输入参数： 无
// 输出参数： 无
// 全局变量： 
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
//void Sys_Fail_Work(void)
//{
//    g_SysVar.WorkPhase = PHASE_FAIL;
//    g_SysVar.System_state1 = FAIL;
//    g_SysVar.INS_Data_valid = 0;
//}
/*****************************函数说明*************************************/
/*函数名称：Navi_Init                                                     */
/*函数功能描述：完成惯导结构体的初始化                                     */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：指针:初始装订结构体lp_InitBind、指针：初始对准结构体lp_Align     */
/*输出变量：指针:惯性导航结构体:lp_Navi                                    */
/*测试用例：暂缺                                                           */
/*备注1：请在惯导解算开始前调用该函数进行导航结构体的初始化                 */
/*备注2：该函数所有形参为指针变量，请使用地址传递方式调用该函数              */
/*返回值：无                                                              */
/**************************************************************************/
void Navi_Init(p_InitBind const lp_InitBind, p_InertialSysAlign const lp_InertialSysAlign, p_Navi lp_Navi)
{
    register IPARA i;

    memset(lp_Navi, 0, sizeof(Navi));   //将g_Navi结构体成员变量全部清零

    //惯导位置初始化
    lp_Navi -> r_Lati = lp_InitBind -> r_InitLati;  //纬度,单位：rad
    lp_Navi -> r_Logi = lp_InitBind -> r_InitLogi;  //经度，单位：rad
    lp_Navi -> Height = lp_InitBind -> InitHeight;  //高度

    //阻尼高度按默认设置
    //lp_Navi-> DampHeight = lp_InitBind -> InitHeight;

    //惯导速度初始化
    for (i = 0; i < 3; i++)
    {
        lp_Navi -> Vn[i] = lp_InitBind -> InitVn[i];   
    }

    //姿态矩阵初始化
    for (i = 0; i < 9; i++)
    {
        lp_Navi -> Cnb[i] = lp_InertialSysAlign -> AlignCnb[i];
    }

    //姿态角初始化
    CnbToAtti(lp_Navi -> Cnb, lp_Navi -> r_Atti);
    //姿态四元数初始化
    for (i = 0; i < 4; i++)
    {
        lp_Navi -> Q[i] = lp_InertialSysAlign -> AlignQ[i];
    }

    //测试代码，加入大的航向误差角
    //lp_Navi -> r_Atti[0] = lp_Navi -> r_Atti[0];
    // AttiToCnb(lp_Navi -> r_Atti,lp_Navi -> Cnb);
    // CnbToQ(lp_Navi -> Cnb,lp_Navi -> Q);
    //测试代码结束
//    //如航向已通过双天线GPS装订，则使用GPS装订的航向
//    if(lp_InitBind -> isHeadBind == YES)
//    {
//        lp_Navi -> r_Atti[0] = lp_InitBind -> r_InitHead;
//        AttiToCnb(lp_Navi -> r_Atti,lp_Navi -> Cnb);
//        CnbToQ(lp_Navi -> Cnb,lp_Navi -> Q);
//        lp_Navi -> isHeadBind = YES;
//    }
    ComputeDeg_Ex(lp_Navi);//计算角度表示的导航参数
    TransHeading0to360(lp_Navi);
}




/*****************************************************************************************************************************
// 函数名称： DynamicNavi_Init
// 功能描述： 
// 输入参数： 无
// 输出参数： 无
// 全局变量： 
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void DynamicNavi_Init(p_GNSSData lp_GNSSData, p_DynamicInertialSysAlign lp_DynamicInertialSysAlign, p_Navi lp_Navi)
{
    register IPARA i;

    //memset(lp_Navi, 0, sizeof(Navi));   //将g_Navi结构体成员变量全部清零

    //惯导位置初始化
    lp_Navi -> r_Lati = lp_GNSSData -> r_GNSSLati;  //纬度,单位：rad
    lp_Navi -> r_Logi = lp_GNSSData -> r_GNSSLogi;  //经度，单位：rad
    lp_Navi -> Height = lp_GNSSData -> GNSSHeight;  //高度

    //阻尼高度按默认设置
    //lp_Navi-> DampHeight = lp_GNSSData -> GNSSHeight;

    //惯导速度初始化
    for (i = 0; i < 3; i++)
    {
        lp_Navi -> Vn[i] = lp_GNSSData -> GNSSVn[i];   
    }

    //姿态矩阵初始化
    for (i = 0; i < 9; i++)
    {
        lp_Navi -> Cnb[i] = lp_DynamicInertialSysAlign -> AlignCnb[i];
    }

    //姿态角初始化
    CnbToAtti( lp_Navi -> Cnb, lp_Navi -> r_Atti );
    //姿态四元数初始化
    for (i = 0; i < 4; i++)
    {
        lp_Navi -> Q[i] = lp_DynamicInertialSysAlign -> AlignQ[i];
    }
    lp_Navi -> r_Atti[0] = lp_GNSSData -> r_GPSHead;
    AttiToCnb( lp_Navi -> r_Atti,lp_Navi -> Cnb );
    CnbToQ( lp_Navi -> Cnb,lp_Navi -> Q );
    lp_Navi -> isHeadBind = YES;
    ComputeDeg_Ex(lp_Navi);//计算角度表示的导航参数
    TransHeading0to360(lp_Navi);
}




/******************************************函数说明***********************************************/
/*函数名称：NaviCompute                                                                          */
/*函数功能描述：完成一次实时惯导解算                                                              */
/*版本号：  Ver 0.1                                                                              */
/*编写日期/时间：                                                                                */
/*编写人：                                                                                       */
/*输入变量：参数：陀螺补偿输出r_Gyro、参数：加速度计补偿输出Acc                                    */
/*输出变量：指针:惯性导航结构体:lp_Navi                                                           */
/*测试用例：暂缺                                                                                 */
/*备注1：该函数输入变量、输出变量均为地址传递                                                      */
/*备注2：该函数为惯导解算全流程的封装函数，用于实时惯导解算                                        */
/*备注3：不同类型陀螺和加速度计输出的物理意义或单位可能不同，请根据代码注释提示或实际情况修改代码    */
/*返回值：无                                                                                     */
/*************************************************************************************************/
void NaviCompute(DPARA const Gyro[3],DPARA const LastGyro[3],DPARA const Acc[3],DPARA const LastAcc[3],p_Navi lp_Navi) 
{
    register IPARA i;	  
    //输入陀螺、加表数值的写入和转换
    for (i = 0 ; i < 3; i++)
    {
        lp_Navi -> r_Wibb[0][i] = LastGyro[i];
        lp_Navi -> r_Wibb[1][i] = Gyro[i];
        lp_Navi -> Fibb[0][i] = LastAcc[i];
        lp_Navi -> Fibb[1][i] = Acc[i]; 
    }

    //惯导解算过程
    ComputeWien(lp_Navi -> r_Lati, lp_Navi -> r_Wien);//计算地球自转角速率在导航系下的分量

    ComputeRmRn(lp_Navi -> r_Lati,lp_Navi->Height,&lp_Navi->Rm,&lp_Navi->Rn,&lp_Navi->invRm,&lp_Navi->invRn);//计算地球子午圈半径、卯酉圈半径及其倒数

    ComputeWenn(lp_Navi -> r_Lati, lp_Navi -> Vn, lp_Navi->invRm,lp_Navi->invRn,lp_Navi -> r_Wenn);//计算载体相对于地球表面的转移角速率在导航系下的分量

    ComputeWnbb(lp_Navi -> r_Wibb, lp_Navi -> r_Wien, lp_Navi -> r_Wenn, lp_Navi -> Cnb, lp_Navi -> r_Wnbb);//计算载体相对于导航坐标系的旋转角速率在导航系下的分量

    ComputeDelSenbb(lp_Navi -> r_Wnbb,lp_Navi -> r_DelSenbb_1,lp_Navi -> r_DelSenbb_2,lp_Navi -> r_DelSenbb);

    ComputeQ(lp_Navi -> r_DelSenbb, lp_Navi -> Q);//计算姿态四元数

    QToCnb(lp_Navi -> Q, lp_Navi -> Cnb);//根据姿态四元数计算姿态矩阵

    CnbToAtti(lp_Navi -> Cnb, lp_Navi -> r_Atti);//根据姿态矩阵计算姿态角

    ComputeAttiRate(lp_Navi -> r_Wnbb, lp_Navi -> r_Atti, lp_Navi -> r_AttiRate);//计算姿态角速率

    ComputeG(lp_Navi -> r_Lati, lp_Navi -> Height, &lp_Navi -> Gn);//计算g

    ComputeVibn(lp_Navi);

    ComputeVn(lp_Navi);//计算导航坐标系下的载体速度

    ComputePos(lp_Navi -> LastVn,lp_Navi -> Vn, lp_Navi -> invRm, lp_Navi -> invRn, &lp_Navi -> r_Lati, &lp_Navi -> r_Logi, &lp_Navi -> Height,lp_Navi -> Sn);//计算载体位置（经度、纬度(单位：rad)、高度）

    ComputeDeg_Ex(lp_Navi);//计算角度表示的导航参数

    TransHeading0to360(lp_Navi);
		//判断拐弯
    lp_Navi -> d_HeadingRateBuffer[lp_Navi -> HeadingRate_Circle_Count] = lp_Navi -> d_AttiRate[0];
    lp_Navi -> HeadingRate_Circle_Count++;

    if(lp_Navi -> HeadingRate_Circle_Count == HEADINGRATE_BUFFER_SIZE)
    {
        lp_Navi -> HeadingRate_Circle_Count = 0;
    }
    lp_Navi -> d_HeadingRate_Mean = 0.0;//清0
    for(i = 0 ; i < HEADINGRATE_BUFFER_SIZE ; i++)
    {
        lp_Navi -> d_HeadingRate_Mean += lp_Navi -> d_HeadingRateBuffer[i];
    }

    lp_Navi -> d_HeadingRate_Mean /= HEADINGRATE_BUFFER_SIZE;

    if(fabs(lp_Navi -> d_HeadingRate_Mean) >= THRESHOLD_HEADING_RATE)
    {
        lp_Navi -> isHeadingchange = YES;
    }
    else
    {
        lp_Navi -> isHeadingchange = NO;
    }
    //将kalman滤波器估计的误差在每个导航周期分别修正
    if((lp_Navi -> Correct_Count > 0)&&(g_SysVar.WorkPhase != PHASE_INS_NAVI))
    {
        ErrCorrect_1_Navi_Time(&g_Navi, &g_Kalman);
    }    
    lp_Navi -> Navi_Count ++;//完成一次完整导航解算过程后，将导航计数+1
}





/*****************************函数说明*************************************/
/*函数名称：ComputeWien                                                   */
/*函数功能描述：计算地球自转角速率在导航系三轴下的分量                    */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：纬度: r_Lati                                                  */
/*输出变量：角速率: r_Wien                                                */
/*测试用例：暂缺                                                          */
/*备注1：该函数输入变量、输出变量均为地址传递                             */
/*备注2：该函数输入姿态四元数应为归一化四元数                             */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeWien(LATI r_Lati, ANGRATE r_Wien[3])
{

    r_Wien[0] = WIE * cos(r_Lati);
    r_Wien[1] = WIE * sin(r_Lati);
    r_Wien[2] = 0.0;
}





/******************************************************函数说明***********************************************************/
/*函数名称：ComputeRmRn                                                                                                  */
/*函数功能描述:计算当地子午圈半径、卯酉圈半径(含载体海拔高度)及其倒数                                                      */
/*版本号：  Ver 0.1                                                                                                      */
/*编写日期/时间：                                                                                                        */
/*编写人：                                                                                                               */
/*输入变量：纬度: r_Lati、高度：Height                                                                                   */
/*输出变量：指针：子午圈半径：lp_Rm、卯酉圈半径lp_Rn                                                                      */
/*输出变量：指针：子午圈半径倒数：lp_invRm、卯酉圈半径倒数lp_invRn                                                         */
/*测试用例：暂缺                                                                                                         */
/*备注1：该函数输入变量为值传递、输出变量为地址传递                                                                       */
/*备注2：计算当地子午圈半径、卯酉圈半径的倒数并存储到导航结构体中是为了减少整个解算过程中除法计算的次数、提高实时解算效率     */
/*返回值：无                                                                                                            */
/*************************************************************************************************************************/
void ComputeRmRn(LATI r_Lati, HEIGHT Height, LEN *lp_Rm, LEN *lp_Rn, DPARA *lp_invRm, DPARA *lp_invRn)
{
    DPARA SinLati = sin(r_Lati);
    DPARA SinLatiSqure = SinLati * SinLati;
    *lp_Rm = RE - (2 * RE * F) + (3 * RE * F * SinLatiSqure) + Height;
    *lp_Rn = RE + F * RE * SinLatiSqure + Height;
    *lp_invRm = 1 / *lp_Rm;
    *lp_invRn = 1 / *lp_Rn;
} 





/*****************************函数说明*************************************/
/*函数名称：ComputeWenn                                                   */
/*函数功能描述:计算载体相对于地球的转移角速率在导航坐标系下的三轴分量     */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：纬度: r_Lati、速度：Vn、参数：invRm、参数：invRn              */
/*输出变量：角速率: r_Wenn                                                */
/*测试用例：暂缺                                                          */
/*备注1：该函数输入变量为值传递、输出变量为地址传递                       */
/*备注2：invRm、invRn为当地子午圈半径、卯酉圈半径的倒数                   */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeWenn(LATI r_Lati, VEL const Vn[3], DPARA invRm, DPARA invRn, ANGRATE r_Wenn[3])
{
    r_Wenn[0] = Vn[2] * invRn;
    r_Wenn[1] = r_Wenn[0] * tan(r_Lati);
    r_Wenn[2] = -Vn[0] * invRm;
}




/*****************************函数说明*************************************/
/*函数名称：ComputeWnbb                                                   */
/*函数功能描述:计算载体坐标系相对于导航坐标系的旋转角速率的三轴分量       */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：角速率: r_Wibb、角速率:r_Wien、角速率：r_Wenn、姿态矩阵：Cnb  */
/*输出变量：角速率: r_Wnbb                                                */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeWnbb(ANGRATE r_Wibb[2][3], ANGRATE const r_Wien[3], ANGRATE const r_Wenn[3], MATR Cnb[9],ANGRATE r_Wnbb[2][3])
{
    register IPARA i = 0,j = 0;

    ANGRATE r_Winn[3] , r_Winb[3];

    for (i = 0; i < 3; i++)
    {
        r_Winn[i] = r_Wien[i] + r_Wenn[i];
    }

    Mat_Mul(Cnb, r_Winn, r_Winb, 3, 3, 1);     
    for(j = 0;j < 2; j++)
    {
        for (i = 0; i < 3; i++)
        {
            r_Wnbb[j][i] = r_Wibb[j][i] - r_Winb[i];
        }
    }
}




/*****************************************************************************************************************************
// 函数名称： ComputeDelSenbb
// 功能描述： 
// 输入参数： 无
// 输出参数： 无
// 全局变量： 
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void ComputeDelSenbb(ANGRATE r_Wnbb[2][3], DELANG r_DelSenbb_1[3], DELANG r_DelSenbb_2[3], DELANG r_DelSenbb[3])
{
    register IPARA i;
    DELANG r_DelSenbb_Compen[3] = { 0.0 };

    for (i = 0; i < 3; i++)
    {
        r_DelSenbb_1[i] = r_Wnbb[0][i] * TIME_NAVI;
    }
    for (i = 0; i < 3; i++)
    {
        r_DelSenbb_2[i] = r_Wnbb[1][i] * TIME_NAVI;
    }
    Vec_Cross(r_DelSenbb_1, r_DelSenbb_2, r_DelSenbb_Compen);

    for (i = 0; i < 3; i++)
    {
        r_DelSenbb[i] = r_DelSenbb_2[i] + 1.0 / 12.0 * r_DelSenbb_Compen[i];
    }

}





/*****************************函数说明*************************************/
/*函数名称：ComputeQ                                                      */
/*函数功能描述:更新计算载体姿态四元数                                     */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：角速率: r_Wnbb                                                */
/*输出变量：惯导姿态四元数: Q                                             */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeQ(DELANG r_DelSenbb[3], QUAT Q[4])
{
    register IPARA i;
    register DPARA C;
    register DPARA S;
    register DPARA InvQSquRoot = 0.0;
        
    QUAT Qc[4];
    QUAT LastQ[4];
    //DELANG r_DelSenbb_1[3];
    //DELANG r_DelSenbb_2[3];

    DELANG r_Delta;
            
    r_Delta = sqrt(r_DelSenbb[0] * r_DelSenbb[0] + r_DelSenbb[1] * r_DelSenbb[1] + r_DelSenbb[2] * r_DelSenbb[2]);

    C = cos(0.5 * r_Delta);

    S = (fabs(r_Delta) >= MIN_DATA) ? sin(0.5 * r_Delta) / r_Delta : 0.5;//除零保护
       
    Qc[0] = C;
    Qc[1] = S * r_DelSenbb[0];
    Qc[2] = S * r_DelSenbb[1];
    Qc[3] = S * r_DelSenbb[2];
    //更新四元数
    for (i = 0; i < 4; i++)
    {
        LastQ[i] = Q[i];
    }
    Qua_Mul(LastQ, Qc, Q);  
    //四元数归一化
    InvQSquRoot = 1.0 / sqrt(Q[0] * Q[0] + Q[1] * Q[1] + Q[2] * Q[2] + Q[3] * Q[3]);
    Q[0] = Q[0] * InvQSquRoot; 
    Q[1] = Q[1] * InvQSquRoot;
    Q[2] = Q[2] * InvQSquRoot;
    Q[3] = Q[3] * InvQSquRoot;
}





/*****************************函数说明*************************************/
/*函数名称：QToCnb                                                        */
/*函数功能描述：完成惯导姿态四元数到姿态矩阵的转换                        */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：惯导姿态四元数:Q                                              */
/*输出变量：惯导姿态矩阵:Cnb                                              */
/*测试用例：暂缺                                                          */
/*备注1：该函数输入变量、输出变量均为地址传递                             */
/*备注2：该函数输入姿态四元数应为归一化四元数                             */
/*返回值：无                                                              */
/**************************************************************************/
void QToCnb(QUAT const Q[4], MATR Cnb[9])
{

    Cnb[0] = (Q[0] * Q[0]) + (Q[1] * Q[1]) - (Q[2] * Q[2]) - (Q[3] * Q[3]);
    Cnb[1] = 2 * (Q[1] * Q[2] + Q[0] * Q[3]);
    Cnb[2] = 2 * (Q[1] * Q[3] - Q[0] * Q[2]);

    Cnb[3] = 2 * (Q[1] * Q[2] - Q[0] * Q[3]);
    Cnb[4] = (Q[0] * Q[0]) - (Q[1] * Q[1]) + (Q[2] * Q[2]) - (Q[3] * Q[3]);
    Cnb[5] = 2 * (Q[2] * Q[3] + Q[0] * Q[1]);

    Cnb[6] = 2 * (Q[1] * Q[3] + Q[0] * Q[2]);
    Cnb[7] = 2 * (Q[2] * Q[3] - Q[0] * Q[1]);
    Cnb[8] = (Q[0] * Q[0]) - (Q[1] * Q[1]) - (Q[2] * Q[2]) + (Q[3] * Q[3]);

}




/*****************************函数说明*************************************/
/*函数名称：CnbToQ                                                        */
/*函数功能描述：完成惯导姿态矩阵到姿态四元数的转换                        */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：惯导姿态矩阵:Cnb                                              */
/*输出变量：惯导姿态四元数:Q                                              */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void CnbToQ(MATR Cnb[9],QUAT Q[4])
{
    IPARA i;
    //检查Cnb中是否存在极小元素，并置为0，避免产生sqrt对极小绝对值的负数开方产生的溢出
    for(i = 0; i < 9; i++)
    {
        if(fabs(Cnb[i]) <= MIN_DATA)
        {
            Cnb[i] = 0.0;
        }
    }
    Q[0] = 0.5 * sqrt(1 + Cnb[0] + Cnb[4] + Cnb[8]);

    Q[1] = (Cnb[5] >= Cnb[7]) ? (0.5 * sqrt(1 + Cnb[0] - Cnb[4] - Cnb[8])) : (-0.5 * sqrt(1 + Cnb[0] - Cnb[4] - Cnb[8]));

    Q[2] = (Cnb[6] >= Cnb[2]) ? (0.5 * sqrt(1 - Cnb[0] + Cnb[4] - Cnb[8])) : (-0.5 * sqrt(1 - Cnb[0] + Cnb[4] - Cnb[8]));

    Q[3] = (Cnb[1] >= Cnb[3]) ? (0.5 * sqrt(1 - Cnb[0] - Cnb[4] + Cnb[8])) : (-0.5 * sqrt(1 - Cnb[0] - Cnb[4] + Cnb[8]));
}




/*****************************函数说明*************************************/
/*函数名称：AttiToCnb                                                     */
/*函数功能描述：完成惯导姿态角到姿态矩阵的转换                            */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：姿态角:r_Atti、数组元素按顺序分别为航向角、俯仰角、横滚角     */
/*输出变量：矩阵:惯导姿态矩阵Cnb                                          */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void AttiToCnb(ATTI const r_Atti[3], MATR Cnb[9])
{
    register DPARA SINFAI = sin(r_Atti[0]);
    register DPARA COSFAI = cos(r_Atti[0]);
    register DPARA SINSETA = sin(r_Atti[1]);
    register DPARA COSSETA = cos(r_Atti[1]);
    register DPARA SINGAMA = sin(r_Atti[2]);
    register DPARA COSGAMA = cos(r_Atti[2]);

    Cnb[0] = COSSETA * COSFAI;
    Cnb[1] = SINSETA;
    Cnb[2] = -SINFAI * COSSETA;
    Cnb[3] = SINGAMA * SINFAI - SINSETA * COSGAMA * COSFAI;
    Cnb[4] = COSGAMA * COSSETA;
    Cnb[5] = SINSETA * SINFAI * COSGAMA + SINGAMA * COSFAI;
    Cnb[6] = SINSETA * SINGAMA * COSFAI + COSGAMA * SINFAI;
    Cnb[7] = -SINGAMA * COSSETA;
    Cnb[8] = COSGAMA * COSFAI - SINGAMA * SINSETA * SINFAI; 
}




/*****************************函数说明*************************************/
/*函数名称：CnbToAtti                                                     */
/*函数功能描述：完成惯导姿态矩阵到姿态角的转换                            */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：矩阵:惯导姿态矩阵Cnb                                          */
/*输出变量：姿态角:r_Atti、数组元素按顺序分别为航向角、俯仰角、横滚角     */
/*测试用例：暂缺                                                          */
/*备注：该函数所有形参为指针变量，请使用地址传递方式调用该函数            */
/*返回值：无                                                              */
/**************************************************************************/
void CnbToAtti(MATR const Cnb[9], ATTI r_Atti[3])
{ 
    //航向角计算
    if(fabs(Cnb[0]) <= MIN_DATA)
    {
        r_Atti[0] = (Cnb[2] > 0) ?  - PIDIV2 : PIDIV2;
    }
    else
    {
        r_Atti[0] = -1 * atan(Cnb[2] / Cnb[0]);

        if(Cnb[0] < 0.0)
        {
            r_Atti[0] = ( Cnb[2] > 0 ) ? r_Atti[0] - PI : r_Atti[0] + PI;
        }
    }
    //俯仰角计算
    r_Atti[1] = asin(Cnb[1]);

    //横滚角计算
    if(fabs(Cnb[4]) <=  MIN_DATA)
    {
        r_Atti[2] = (Cnb[7] > 0) ? - PIDIV2 : PIDIV2;
    }
    else
    {
        r_Atti[2] = -1 * atan(Cnb[7] / Cnb[4]);
        if(Cnb[4] < 0)
        {
            r_Atti[2] = (Cnb[7] > 0) ? r_Atti[2] - PI : r_Atti[2] + PI;
        }
    }
}




/*****************************函数说明*************************************/
/*函数名称：ComputeAttiRate                                               */
/*函数功能描述：计算惯导姿态角速率                                        */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：角速率: r_Wnbb、姿态角：r_Atti                                */
/*输出变量：角速率: r_AttiRate                                            */
/*测试用例：暂缺                                                          */
/*备注：该函数所有形参为指针变量，请使用地址传递方式调用该函数            */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeAttiRate(ANGRATE r_Wnbb[2][3], ATTI const r_Atti[3], ANGRATE r_AttiRate[3])
{
    UINT8 i = 0;
    register DPARA SINGAMA = sin(r_Atti[2]);
    register DPARA COSGAMA = cos(r_Atti[2]);
    register DPARA COSSETA = cos(r_Atti[1]);
    register DPARA TANSETA = tan(r_Atti[1]);
    DPARA r_Wnbb_Mean[3];
    for(i = 0; i < 3;i++)
    {
        r_Wnbb_Mean[i] = r_Wnbb[1][i];//由于是单子样，不需要进行平均处理 2024-12-27
    }

    r_AttiRate[0] = (COSGAMA / COSSETA) * r_Wnbb_Mean[1] - (SINGAMA / COSSETA) * r_Wnbb_Mean[2];

    r_AttiRate[1] = SINGAMA * r_Wnbb_Mean[1] + COSGAMA * r_Wnbb_Mean[2];

    r_AttiRate[2] = r_Wnbb_Mean[0] - COSGAMA * TANSETA * r_Wnbb_Mean[1] + SINGAMA * TANSETA * r_Wnbb_Mean[2];
}





/*****************************函数说明*************************************/
/*函数名称：ComputeG                                                      */
/*函数功能描述：计算当地重力加速度G                                       */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：纬度: r_Lati、高度：Height                                    */
/*输出变量：加速度: lp_Gn                                                 */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量为值传递，输出变量为地址传递                        */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeG(LATI r_Lati, HEIGHT Height, ACCELER *lp_Gn)
{
    *lp_Gn = G0 * (1 + 0.0052884 * sin(r_Lati) * sin(r_Lati) - 0.0000059 * sin(2 * r_Lati) * sin(2 * r_Lati)) * (1 - 2 * Height * INVRE);	
}





/*****************************************************************************************************************************
// 函数名称： ComputeVibn
// 功能描述： 
// 输入参数： 无
// 输出参数： 无
// 全局变量： 
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void ComputeVibn(p_Navi lp_Navi)
{
    register IPARA i;

    VEL DeltaVb_1[3];
    VEL DeltaVb_2[3];
    VEL DeltaVb[3];
    VEL DeltaVRot[3];
    VEL DeltaVScul_1[3];
    VEL DeltaVScul_2[3];
    VEL DeltaVScul[3];
    VEL Vibb[3];

    ACCELER Fibb[3];

    for (i = 0; i < 3; i++)
    {
        Fibb[i] = lp_Navi ->Fibb[1][i];
        DeltaVb[i] = lp_Navi ->Fibb[1][i] * TIME_NAVI;
    }
    
    Vec_Cross(lp_Navi ->r_DelSenbb , DeltaVb, DeltaVRot);
    for (i = 0; i < 3; i++)
    {
        DeltaVRot[i] = 0.5 * DeltaVRot[i];	
    }  
    
    // 单子样下关闭划桨误差补偿功能
    //Vec_Cross(DeltaVb_1 , lp_Navi ->r_DelSenbb_2, DeltaVScul_1);
    //Vec_Cross(DeltaVb_2 , lp_Navi ->r_DelSenbb_1, DeltaVScul_2);
    //for (i = 0; i < 3; i++)
    //{
    //    DeltaVScul[i] = 2.0 / 3.0 *  (DeltaVScul_1[i] - DeltaVScul_2[i]);
    //}

    for (i = 0; i < 3; i++)
    {
        //Vibb[i] = DeltaVb[i] + DeltaVRot[i] + DeltaVScul[i];
        Vibb[i] = DeltaVb[i] + DeltaVRot[i];
    }

    Mat_Tr(lp_Navi -> Cnb, lp_Navi -> Cbn, 3, 3);
    //??Fibn
    Mat_Mul(lp_Navi -> Cbn,Vibb, lp_Navi -> Vibn, 3, 3, 1);

    Mat_Mul(lp_Navi -> Cbn, Fibb, lp_Navi -> Fibn, 3, 3, 1);
}





/*****************************函数说明*************************************/
/*函数名称：ComputeVn                                                     */
/*函数功能描述：计算载体在导航坐标系下的三轴速度分量（北天东）            */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：加速度: Fibb、角速度：r_Wien、角速度：r_Wenn、加速度：Gn      */
/*输入变量：姿态矩阵: Cnb、加速度：Fibn                                   */
/*输出变量：速度: LastVn、Vn                                              */
/*测试用例：暂缺                                                          */
/*备注：该函数中Gn为值传递、其余输入输出变量均为地址传递                  */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeVn(p_Navi lp_Navi)
{
    register IPARA i = 0;

    register DPARA temp1 = 0.0;

    register DPARA temp2 = 0.0;

    //	register DPARA temp3 = 0.0;

    VEL DeltaVn[3];

    //double del_Vuf=0;

    temp1 = 2 * lp_Navi -> r_Wien[1] + lp_Navi -> r_Wenn[1];
    temp2 = 2 * lp_Navi -> r_Wien[0] + lp_Navi -> r_Wenn[0];

    DeltaVn[0] = lp_Navi -> Vibn[0] + ( - temp1 * lp_Navi -> Vn[2] + lp_Navi -> r_Wenn[2] * lp_Navi -> Vn[1]) * TIME_NAVI; 

    //temp3 = lp_Navi->Height + K4 * lp_Navi->Vn[1] - lp_Navi->DampHeight;
    //lp_Navi -> K3_Integral = lp_Navi -> K3_Integral + K3 * temp3 * TIME_NAVI;
    //DeltaVn[1] = lp_Navi -> Vibn[1] + (temp2 * lp_Navi->Vn[2] - lp_Navi-> r_Wenn[2] * lp_Navi->Vn[0] - lp_Navi->Gn - K2 * temp3 - lp_Navi->K3_Integral) * TIME_NAVI;
    DeltaVn[1] = lp_Navi -> Vibn[1] + (temp2 * lp_Navi->Vn[2] - lp_Navi-> r_Wenn[2] * lp_Navi->Vn[0] - lp_Navi->Gn) * TIME_NAVI;

    DeltaVn[2] = lp_Navi -> Vibn[2] + (temp1 * lp_Navi -> Vn[0] - temp2 * lp_Navi -> Vn[1]) * TIME_NAVI;

    for (i = 0; i < 3; i++)
    {
        lp_Navi -> LastVn[i] = lp_Navi -> Vn[i];
        lp_Navi -> Vn[i] = lp_Navi -> LastVn[i] + DeltaVn[i];	
    }

		if(g_SysVar.WorkPhase == PHASE_INS_NAVI)
		{
			lp_Navi -> Vn[1] = 0.0;
    }
    if(fabs(lp_Navi -> Vn[0]) >= THRESHOLD_VN)
    {
        lp_Navi -> Vn[0] = 0.0;	
    }
    if(fabs(lp_Navi -> Vn[2]) >= THRESHOLD_VE)
    {
        lp_Navi -> Vn[2] = 0.0;	
    }
}




/*****************************************************************************************************************************
// 函数名称： VnDapmedByAir
// 功能描述： 
// 输入参数： 无
// 输出参数： 无
// 全局变量： 
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
//void VnDapmedByAir(p_Navi lp_Navi,p_ADCData lp_ADCData)
//{
//    if(g_SysVar.isVMC_Air_Update == YES)
//    {
//        if(lp_ADCData -> isAirHeightEn == YES)
//        {
//            lp_Navi -> DampHeight = lp_ADCData -> Air_Height;
//            /////////////////////////////////////
////            Zuni_Updata = YES;
//            /////////////////////////////////////
//        }
//        /*else if(lp_ADCData -> isVertical_V_En == YES)
//        {
//            lp_Navi -> DampHeight += lp_ADCData -> Vertical_V * TIME_NAVI;
//        }*/
//        g_SysVar.isVMC_Air_Update = NO;
//    }
//}



/*****************************************************************************************************************************
// 函数名称： VnDapmed_PureINS
// 功能描述： 
// 输入参数： 无
// 输出参数： 无
// 全局变量： 
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void VnDapmed_PureINS(p_Navi lp_Navi)
{
    lp_Navi -> Vn[1] = 0.0;
    lp_Navi -> K3_Integral = 0.0;
    lp_Navi -> DampedV[1] = 0.0;
}





/*****************************函数说明*************************************/
/*函数名称：ComputeVb                                                     */
/*函数功能描述：计算载体在载体坐标系下的三轴速度分量（前上右）            */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：速度: Vn、姿态矩阵：Cnb                                       */
/*输出变量：速度: Vb                                                      */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeVb(VEL const Vn[3], MATR const Cnb[9], VEL Vb[3])
{
    Vb[0] = Cnb[0] * Vn[0] + Cnb[1] * Vn[1] + Cnb[2] * Vn[2];
    Vb[1] = Cnb[3] * Vn[0] + Cnb[4] * Vn[1] + Cnb[5] * Vn[2];
    Vb[2] = Cnb[6] * Vn[0] + Cnb[7] * Vn[1] + Cnb[8] * Vn[2];
}





/*****************************函数说明*************************************/
/*函数名称：AttiToCpb                                                     */
/*函数功能描述：                                                          */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：速度: Vn、姿态矩阵：Cnb                                       */
/*输出变量：速度: Vb                                                      */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void AttiToCpb(ATTI const r_Atti[3], MATR Cpb[9])
{
    register DPARA SINFAI = sin(0.0);
    register DPARA COSFAI = cos(0.0);
    register DPARA SINSETA = sin(r_Atti[1]);
    register DPARA COSSETA = cos(r_Atti[1]);
    register DPARA SINGAMA = sin(r_Atti[2]);
    register DPARA COSGAMA = cos(r_Atti[2]);

    Cpb[0] = COSSETA * COSFAI;
    Cpb[1] = SINSETA;
    Cpb[2] = -SINFAI * COSSETA;
    Cpb[3] = SINGAMA * SINFAI - SINSETA * COSGAMA * COSFAI;
    Cpb[4] = COSGAMA * COSSETA;
    Cpb[5] = SINSETA * SINFAI * COSGAMA + SINGAMA * COSFAI;
    Cpb[6] = SINSETA * SINGAMA * COSFAI + COSGAMA * SINFAI;
    Cpb[7] = -SINGAMA * COSSETA;
    Cpb[8] = COSGAMA * COSFAI - SINGAMA * SINSETA * SINFAI;
}




/*****************************函数说明*************************************/
/*函数名称：ComputeVp                                                     */
/*函数功能描述：                                                          */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：速度: Vn、姿态矩阵：Cnb                                       */
/*输出变量：速度: Vb                                                      */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeVp(VEL const Vb[3], MATR const Cpb[9], VEL Vp[3])
{
    Vp[0] = Cpb[0] * Vb[0] + Cpb[3] * Vb[1] + Cpb[6] * Vb[2];
    Vp[1] = Cpb[1] * Vb[0] + Cpb[4] * Vb[1] + Cpb[7] * Vb[2];
    Vp[2] = Cpb[2] * Vb[0] + Cpb[5] * Vb[1] + Cpb[8] * Vb[2];
}





/*****************************函数说明*************************************/
/*函数名称：DampedVp                                                      */
/*函数功能描述：对纯惯导状态下的速度误差进阻尼                            */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：速度: Vn、姿态矩阵：Cnb                                       */
/*输出变量：速度: Vb                                                      */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void DampedVp(VEL Vp[3],VEL Vb[3],MATR const Cpb[9],MATR const Cnb[9],VEL Vn[3])
{
    //侧向速度清0
    Vp[2] = 0.0;
    //判断前向速度是否与航向方向一致
    if(Vp[0] < 0.0)
    {
        Vp[0] = 0.0;
    }
    Vb[0] = Cpb[0] * Vp[0] + Cpb[1] * Vp[1] + Cpb[2] * Vp[2];
    Vb[1] = Cpb[3] * Vp[0] + Cpb[4] * Vp[1] + Cpb[5] * Vp[2];
    Vb[2] = Cpb[6] * Vp[0] + Cpb[7] * Vp[1] + Cpb[8] * Vp[2];

    Vn[0] = Cnb[0] * Vb[0] + Cnb[3] * Vb[1] + Cnb[6] * Vb[2];
    Vn[1] = Cnb[1] * Vb[0] + Cnb[4] * Vb[1] + Cnb[7] * Vb[2];
    Vn[2] = Cnb[2] * Vb[0] + Cnb[5] * Vb[1] + Cnb[8] * Vb[2];
}




/*******************************函数说明*************************************/
/*函数名称：ComputePos                                                      */
/*函数功能描述：计算载体位置（纬度、经度、高度、导航系三轴下的位移分量）    */
/*版本号：  Ver 0.1                                                         */
/*编写日期/时间：                                                           */
/*编写人：                                                                  */
/*输入变量：速度: LastVn、速度：Vn、参数：invRm、参数：invRn                */
/*输出变量：纬度lp_Lati、经度：lp_Logi、高度：lp_Height、位移Sn（北天东）   */
/*测试用例：暂缺                                                            */
/*备注：该函数中参数invRm、invRn为值传递、其余输入变量、输出变量均为地址传递*/
/*返回值：无                                                                */
/****************************************************************************/
void ComputePos(VEL const LastVn[3],VEL const Vn[3], DPARA invRm, DPARA invRn, LATI *lp_Lati, LOGI *lp_Logi, HEIGHT *lp_Height,LEN Sn[3])
{
    register IPARA i;
    LEN DeltaSn[3];

    for (i = 0; i < 3; i++)
    {
        //DeltaSn[i] = (0.5 * (Vn[i] + LastVn[i]) - g_Navi.DampedV[i]) * TIME_NAVI;
        DeltaSn[i] = 0.5 * (Vn[i] + LastVn[i])* TIME_NAVI;
        Sn[i] = Sn[i] + DeltaSn[i];
    }

    *lp_Lati = *lp_Lati + DeltaSn[0] * invRm;

    *lp_Logi = *lp_Logi + DeltaSn[2] * invRn / cos(*lp_Lati);

    //东西经正负180度切换的判断
    if(*lp_Logi > PI)
    {
        *lp_Logi = *lp_Logi - PIMUL2; //PIMUL2表示2π
    }

    if(*lp_Logi < -PI)
    {
        *lp_Logi = *lp_Logi + PIMUL2; //PIMUL2表示2π
    }

    *lp_Height = *lp_Height + DeltaSn[1];
		
		//*lp_Height = 0.0;

    /*if((g_SysVar.WorkMode == WORK_MODE_PURE_INS)||(g_SysVar.WorkMode == WORK_MODE_FAIL))
    { 
        *lp_Height=*lp_Height;
        *lp_Height=0;
    }
    else if((g_SysVar.WorkMode == WORK_MODE_INS_AIR) && (Zuni_Updata == YES))
    {
        *lp_Height = *lp_Height + (g_ADCData.Air_Height- *lp_Height) * 1.0;//原来0.7
    }
    else 
    {
        if(Zuni_Updata == YES) 
        {
            if(g_Kalman.isHeightRecord == YES)
            {
                *lp_Height = *lp_Height + (g_GNSSData_In_Use.GNSSHeight - g_Kalman.InsHeight) * 1.0;//原来0.7
                g_Kalman.isHeightRecord = NO;
            }
            else
            {
                *lp_Height = *lp_Height + (g_GNSSData_In_Use.GNSSHeight - *lp_Height) * 1.0;//原来0.7
            }
        }
    }*/

    /*if(*lp_Height > 12000.0)
        *lp_Height = 12000.0;
    if(*lp_Height < -500.0)
        *lp_Height = -500.0;*/

}





/**********************************函数说明****************************************/
/*函数名称：ComputeTrackAtti                                                      */
/*函数功能描述：根据输入的GPS速度信息计算航迹姿态角                               */
/*版本号：  Ver 0.1                                                               */
/*编写日期/时间：                                                                */
/*编写人：                                                                      */
/*输入、输出变量：指针：导航结构体lp_Navi                                         */
/*测试用例：暂缺                                                                  */
/*备注：无                                                                        */
/*返回值：无                                                                      */
/**********************************************************************************/
/*void ComputeTrackAtti(VEL GPSVn[3], ATTI *r_TrackAtti)
{
    //除0保护
    if(fabs(GPSVn[0]) <= MIN_DATA)
    {
        if(GPSVn[2] > 0.0)
        {
            *r_TrackAtti = -0.5 * PI;
        }
        else
        {
            *r_TrackAtti = 0.5 * PI;
        }
        return;
    }
        
    *r_TrackAtti = -1 * atan(GPSVn[2] / GPSVn[0]);

    if(GPSVn[0] < 0.0)
    {
        if(*r_TrackAtti > 0.0)
        {
                *r_TrackAtti -= PI;
        }
        else
        {
                *r_TrackAtti += PI;
        }
    }
}*/




/**********************************函数说明****************************************/
/*函数名称：ComputeDeg_Ex                                                         */
/*函数功能描述：计算对外输出惯导数据（圆度表示的经纬度、姿态角、姿态角速率）      */
/*版本号：  Ver 0.1                                                               */
/*编写日期/时间：                                       */
/*编写人：                                                                    */
/*输入、输出变量：指针：导航结构体lp_Navi                                         */
/*测试用例：暂缺                                                                  */
/*备注：该函数为计算惯导对外输出数据的封装函数、可在需要向上位机输出惯导数据时调用*/
/*返回值：无                                                                      */
/**********************************************************************************/
void ComputeDeg_Ex(p_Navi lp_Navi) 
{



    lp_Navi -> d_Lati = lp_Navi -> r_Lati * R2D;
    lp_Navi -> d_Logi = lp_Navi -> r_Logi * R2D;
        

    lp_Navi -> d_Atti[0] = lp_Navi -> r_Atti[0] * R2D;
    lp_Navi -> d_Atti[1] = lp_Navi -> r_Atti[1] * R2D;
    lp_Navi -> d_Atti[2] = lp_Navi -> r_Atti[2] * R2D;


   // lp_Navi -> d_AttiRate[0] = lp_Navi -> r_AttiRate[0] * R2D;
  //  lp_Navi -> d_AttiRate[1] = lp_Navi -> r_AttiRate[1] * R2D;
   // lp_Navi -> d_AttiRate[2] = lp_Navi -> r_AttiRate[2] * R2D;
}




/**********************************函数说明****************************************/
/*函数名称：TransHeadng0to360                                                     */
/*函数功能描述：计算对外输出惯导数据（圆度表示的经纬度、姿态角、姿态角速率）      */
/*版本号：  Ver 0.1                                                               */
/*编写日期/时间：                                                                 */
/*编写人：                                                                        */
/*输入、输出变量：指针：导航结构体lp_Navi                                         */
/*测试用例：暂缺                                                                  */
/*备注：该函数为计算惯导对外输出数据的封装函数、可在需要向上位机输出惯导数据时调用*/
/*返回值：无                                                                      */
/**********************************************************************************/
void TransHeading0to360(p_Navi lp_Navi)
{
    if(lp_Navi -> d_Atti[0] > 0.0)
    {
        lp_Navi -> d_Atti[0] = 360 - lp_Navi -> d_Atti[0];
    }
    else
    {
        lp_Navi -> d_Atti[0] = - lp_Navi -> d_Atti[0];
    }
    //航向角速率也切换为顺时针方向为正
    //lp_Navi -> d_AttiRate[0] *= -1.0;//此处不用切换，协议要求绕基线轴逆时针旋转为正
}




/*********************************************函数说明*******************************************************/
/*函数名称ComputeLeverArmVn                                                                               */
/*函数功能描述:根据输入杆臂长度和陀螺补偿数据计算出导航系（n系下的分量）下的杆臂速度                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：与GPS组合修正杆臂速度时r_Webb最好为平均值，而非瞬时值                                              */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeLeverArmVn(MATR Cnb[9],DPARA r_Webb[3],VEC VnLeverArm[3],VEL Vn_r[3])
{
    MATR Cbn[9];
    VEL Vb_r[3];
    //计算出杆臂速度Vn_r
    Vec_Cross(r_Webb, VnLeverArm, Vb_r);
    Mat_Tr(Cnb, Cbn, 3, 3);
    Mat_Mul(Cbn, Vb_r, Vn_r, 3, 3, 1);
}




/*********************************************函数说明*******************************************************/
/*函数名称ComputeLeverArmSn                                                                               */
/*函数功能描述:根据输入杆臂长度和陀螺补偿数据计算出导航系（n系下的分量）下的杆臂位移                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：与GPS组合修正杆臂速度时r_Webb最好为平均值，而非瞬时值                                              */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeLeverArmSn(MATR Cnb[9],LEN Sb_r[3],LEN Sn_r[3])
{
    MATR Cbn[9];
    //计算出杆臂位移Sn_r	
    Mat_Tr(Cnb, Cbn, 3, 3);
    Mat_Mul(Cbn, Sb_r, Sn_r, 3, 3, 1);
}




/*****************************函数说明*************************************/
/*函数名称：ComputeWindSpeedAndHeading                                    */
/*函数功能描述:计算风向与风速                                             */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：                                                              */
/*输出变量：                                                              */
/*测试用例：暂缺                                                          */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
/*void ComputeWindSpeedAndHeading(p_ADCData lp_ADCData,p_Navi lp_Navi)
{
    VEL Air_Vn[3];
    VEL Air_Vb[3];
    VEL Wind_Vn[3]; 
    MATR Cbn[9];
    ATTI r_WindHeading;
    ATTI d_WindHeading;
    //Air_Vn[0] =  lp_ADCData -> AirVn * cos(lp_Navi -> r_Atti[1]) * cos(lp_Navi -> r_Atti[0]);
    //Air_Vn[1] =  lp_ADCData -> AirVn * sin(lp_Navi -> r_Atti[1]);
    //Air_Vn[2] = -lp_ADCData -> AirVn * cos(lp_Navi -> r_Atti[1]) * sin(lp_Navi -> r_Atti[0]);
    Air_Vb[0] = lp_ADCData -> AirVb;
    Air_Vb[1] = 0.0;
    Air_Vb[2] = 0.0;
    Mat_Tr(lp_Navi -> Cnb, Cbn, 3, 3);
    Mat_Mul(Cbn, Air_Vb, Air_Vn, 3, 3, 1);
    Wind_Vn[0] = Air_Vn[0] - lp_Navi -> Vn[0];
    Wind_Vn[1] = Air_Vn[1] - lp_Navi -> Vn[1];
    Wind_Vn[2] = Air_Vn[2] - lp_Navi -> Vn[2];
    lp_ADCData -> WindVn = sqrt(Wind_Vn[0] * Wind_Vn[0] + Wind_Vn[2] * Wind_Vn[2]);
    ComputeTrackAtti(Wind_Vn, &r_WindHeading);
    d_WindHeading = r_WindHeading * R2D;
    if(d_WindHeading > 0.0)
    {
        d_WindHeading = 360 - d_WindHeading;
    }
    else
    {
        d_WindHeading = - d_WindHeading;
    }
    lp_ADCData -> WindHeading = d_WindHeading;

}*/





/*****************************函数说明*************************************/
/*函数名称：ComputeHeave                                                  */
/*函数功能描述：计算升沉                                                  */
/*版本号：  Ver 0.1                                                       */
/*编写日期/时间：                                                         */
/*编写人：                                                                */
/*输入变量：                                                              */
/*输出变量：                                                              */
/*测试用例：暂缺                                                          */
/*备注1：                                                                 */
/*备注2：                                                                 */
/*返回值：无                                                              */
/**************************************************************************/
/*void ComputeHeave(p_Heave lp_Heave)
{
    //lp_Heave -> Last_Raw_Vu = lp_Heave -> Raw_Vu;
    lp_Heave -> Last_Filtered_Vu = lp_Heave -> Filtered_Vu;
    lp_Heave -> Raw_Vu += lp_Heave -> Acc_Vertical * TIME_HEAVE;
    IIR_Data_Input_buffer(lp_Heave -> InputBuffer,lp_Heave -> Raw_Vu);
    lp_Heave -> Filtered_Vu = IIR_Filter(lp_Heave -> InputBuffer,lp_Heave -> OutputBuffer,lp_Heave -> IIR_a,lp_Heave -> IIR_b);
    IIR_Data_Output_buffer(lp_Heave -> OutputBuffer,lp_Heave -> Filtered_Vu);

    lp_Heave -> Heave += 0.5 * (lp_Heave -> Last_Filtered_Vu + lp_Heave -> Filtered_Vu) * TIME_HEAVE;
    //lp_Heave -> Raw_Vu = lp_Heave -> Filtered_Vu;
}*/



