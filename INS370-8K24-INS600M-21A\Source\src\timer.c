//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：timer.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.18
//---------------------------------------------------------


#include "main.h"
#include "timer.h"
#include "gd32f4xx_it.h"

static void timer_config(void)
{
    uint32_t gptmr_freq;
    gptmr_channel_config_t config;

    gptmr_channel_get_default_config(APP_BOARD_GPTMR, &config);

    gptmr_freq = clock_get_frequency(APP_BOARD_GPTMR_CLOCK);
    config.reload = gptmr_freq / 1000 * APP_TICK_MS;
    gptmr_channel_config(APP_BOARD_GPTMR, APP_BOARD_GPTMR_CH, &config, false);
    gptmr_start_counter(APP_BOARD_GPTMR, APP_BOARD_GPTMR_CH);

    gptmr_enable_irq(APP_BOARD_GPTMR, GPTMR_CH_RLD_IRQ_MASK(APP_BOARD_GPTMR_CH));
    intc_m_enable_irq_with_priority(APP_BOARD_GPTMR_IRQ, 1);
}


void tick_ms_isr(void)
{
    if (gptmr_check_status(APP_BOARD_GPTMR, GPTMR_CH_RLD_STAT_MASK(APP_BOARD_GPTMR_CH))) {
        gptmr_clear_status(APP_BOARD_GPTMR, GPTMR_CH_RLD_STAT_MASK(APP_BOARD_GPTMR_CH));
        //Led_Control();
        TIMER2_IRQHandler();//1ms定时中断调用
    }
}
SDK_DECLARE_EXT_ISR_M(APP_BOARD_GPTMR_IRQ, tick_ms_isr);


uint32_t current_reload;
//设置PWM频率
static void set_pwm_waveform_edge_aligned_frequency(uint32_t freq)
{
    gptmr_channel_config_t config;
    uint32_t gptmr_freq;

    gptmr_channel_get_default_config(APP_BOARD_PWM, &config);
    gptmr_freq = clock_get_frequency(APP_BOARD_PWM_CLOCK);
    current_reload = gptmr_freq / freq;
    config.reload = current_reload;
    config.cmp_initial_polarity_high = false;
    gptmr_stop_counter(APP_BOARD_PWM, APP_BOARD_PWM_CH);
    gptmr_channel_config(APP_BOARD_PWM, APP_BOARD_PWM_CH, &config, false);
    gptmr_channel_reset_count(APP_BOARD_PWM, APP_BOARD_PWM_CH);
    gptmr_start_counter(APP_BOARD_PWM, APP_BOARD_PWM_CH);
}

//设置占空比
static void set_pwm_waveform_edge_aligned_duty(uint8_t duty)
{
    uint32_t cmp;
    if (duty > 100) {
        duty = 100;
    }
    cmp = ((current_reload * duty) / 100) + 1;
    gptmr_update_cmp(APP_BOARD_PWM, APP_BOARD_PWM_CH, 0, cmp);
}

void Pwm_Init(void)
{
    init_gptmr_pins(APP_BOARD_PWM);
    set_pwm_waveform_edge_aligned_frequency(17);//频率配置为17Hz，周期约为60ms
    set_pwm_waveform_edge_aligned_duty(50);//50%占空比
}


void timer_Init(void)
{
    init_gptmr_pins(APP_BOARD_GPTMR);//捕获引脚配置
    timer_config();
}

