/********************************************************************************************
    \file    by gd32f470i_eval.h
    \brief   definitions for GD32F470_EVAL's leds, keys and COM ports hardware resources
    
    \version 2024-3.12, V1.0 demo for GD32F4xx
**********************************************************************************************/


#ifndef __PJT_BOARD_H__
#define __PJT_BOARD_H__

#ifdef __cplusplus
 extern "C" {
#endif

#include "pjt_glb_head.h"

#include "systick.h"
#include "app_tool.h"
#include "main.h"

extern uint8_t  g_r8bit;   
extern uint8_t  g_sdbuf[4];
extern uint16_t g_r16bit;
extern uint32_t g_retry , g_tmo ;

///===Track-Datas==========================================================================================================================
//--update 2024.6.27---------------------------------------------------------------------------------
extern uint32_t g_fpga_syn_cnt , g_alg_entr_cnt ;
//--end update 2024.6.6------------------------------------------------------------------------------
///===End Track-Datas===================================================================================================End Track-Datas====

///===Tx-FPGA_RawDatas=====================================================================================================================
//--update 2024.6.6---------------------------------------------------------------------------------
#define	UPPER_InsUart_Hd0x66AA  		4	   //mcuFPGA
extern int	gins912outputmode  ;				 // ins422/ins232 
//--end update 2024.6.6-----------------------------------------------------------------------------
///===End Tx-FPGA_RawDatas=========================================================================================End Tx-FPGA_RawDatas====

///===FFDev_SDCard_Spi=====================================================================================================================
/*****************************************************
 \brief
 \param
 \return void
 \update
    2024.3.25,
*****************************************************/
void pjt_ffdev_sdc_spi_init(void);

///===End FFDev_SDCard_Spi=========================================================================================End FFDev_SDCard_Spi====


///===Board_UartPorts======================================================================================================================
//--update 2024.3.14 14:45-
//#define IAP_INFO_SIZE            128  //iap infomation size
#define INS_422_RxBuffSize         64
											 
//int8_t iap_info[IAP_INFO_SIZE] = {0};											 
extern __IO  uint16_t g_ins422_rx_precnt , g_ins422_rx_curcnt ;
extern __IO  bool     g_ins422_rxtkn     ;     // rx-token: be True if received a data
extern __IO  bool     g_ins422_rxtkn_ovr ; //rx-token: be True if received a amount of datas over
extern uint8_t         g_ins422_rxbuff[INS_422_RxBuffSize ] ; // uart , USART_REC_LEN.

#define G_DBCOM_SZ   256
//static s8_t  _r_dbcom_txbuff[G_DBCOM_SZ] ;

/**********************************************************************
  *\brief reset ins422`s rx datas
  *\param uart, uart peripheral
  *\param fmt, 
  *\return void
  *\notice
       -1-, #include "stdarg.h"	 	
  *\update
  *   -1.0-,(verified)2024.3.12
  *********************************************************************/
void app_ins422rx_rst(void);

/**********************************************************************
  *\brief INS422_ARM-->UART4 app_ins422_printf
  *\param uart, uart peripheral
  *\param fmt, 
  *\return void
  *\notice
       -1-, #include "stdarg.h"	 	
  *\update
  *   -2.0-,(verified)2024.3.12
         _r_dbcom_txbuffptr->rxbuf
  *   -1.0-,(verified) 2022.7.6 created
  *********************************************************************/
void app_ins422_printf(const char* fmt,...)  ;

/**********************************************************************
  *\brief uart
  *\param uart, uart peripheral
  *\param fmt, 
  *\return void
  *\notice
       -1-, #include "stdarg.h"	 	
  *\update
  *   -2.0-,(verified)2024.3.12
         _r_dbcom_txbuffptr->rxbuf
  *   -1.0-,(verified) 2022.7.6 created
  *********************************************************************/
void app_free_printf(uint32_t huart,const char* fmt,...)  ;
///===End Board_UartPorts===========================================================================================End Board_UartPorts====

///===Board-LEDs===========================================================================================================================
// exported types 
typedef enum 
{
    LED_sdARM      = 0,
    LED_WorkStatus = 1,	
    LED_WheelSpeed = 2,
} led_typedef_enum;

// eval board low layer led 
#define  LEDs_QTY                    3U

#define LED_sdARM_GPIO_PORT					GPIO_DO_GPIOA
#define LED_sdARM_GPIO_PIN					10

#define LED_WheelSpeed_GPIO_PORT				GPIO_DO_GPIOA
#define LED_WheelSpeed_GPIO_PIN					24

#define LED_WorkStatus_GPIO_PORT				GPIO_DO_GPIOA
#define LED_WorkStatus_GPIO_PIN                                 23

  
/* function declarations */
/* configures led GPIO */
void pjt_led_init(led_typedef_enum lednum);
/* turn on selected led */
void pjt_led_on(led_typedef_enum lednum);
/* turn off selected led */
void pjt_led_off(led_typedef_enum lednum);
/* toggle the selected led */
void pjt_led_togg(led_typedef_enum lednum);

/***********************************************************************
    \brief      write Led to the specified GPIO pin
    \param[in]  gpio_periph: GPIO port
                only one parameter can be selected which is shown as below:
      \arg        GPIOx(x = A,B,C,D,E,F,G,H,I)
    \param[in]  pin: GPIO pin
                one or more parameters can be selected which are shown as below:
      \arg        GPIO_PIN_x(x=0..15), GPIO_PIN_ALL
    \param[in]  bit_value: SET or RESET
      \arg        RESET: clear the port pin
      \arg        SET: set the port pin
    \param[out] none
    \retval     none
************************************************************************/
//void pjt_led_output(led_typedef_enum lednum, bit_status bit_value);
///===End Board-LEDs=====================================================================================================End Board-LEDs====

#ifdef __cplusplus
}
#endif

#endif /* __PJT_BOARD_H__ */
