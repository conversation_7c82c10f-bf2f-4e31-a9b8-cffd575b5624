/*!
    \file  nav_magnet.h
    \brief Navigation magnetometer interface header file
*/

#ifndef __NAV_MAGNET_H
#define __NAV_MAGNET_H

#include <stdint.h>
#include <stdbool.h>

typedef enum {
    NAV_MAGNET_STATUS_OK = 0,
    NAV_MAGNET_STATUS_ERROR
} nav_magnet_status_t;

typedef struct {
    float mag[3];
    uint32_t timestamp;
    bool valid;
} nav_magnet_data_t;

typedef struct {
    float bias[3];
    float scale[3];
    float misalign[9];
} nav_magnet_calib_t;

nav_magnet_status_t nav_magnet_init(void);
nav_magnet_status_t nav_magnet_calibrate(nav_magnet_calib_t *calib);
nav_magnet_status_t nav_magnet_update(const nav_magnet_data_t *mag_data);

#endif /* __NAV_MAGNET_H */
