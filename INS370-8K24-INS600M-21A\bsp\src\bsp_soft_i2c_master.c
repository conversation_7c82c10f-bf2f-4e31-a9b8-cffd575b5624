#include "bsp_soft_i2c_master.h"

void Soft_I2C_SDA_Output(BSP_SOFT_I2C_Type* soft_i2c);
void Soft_I2C_SDA_Input(BSP_SOFT_I2C_Type* soft_i2c);

void Soft_I2C_Master_Init(BSP_SOFT_I2C_Type* soft_i2c)
{
	if((soft_i2c->scl.gpiox == GPIOA)||(soft_i2c->sda.gpiox == GPIOA))
		rcu_periph_clock_enable(RCU_GPIOA);
	else if((soft_i2c->scl.gpiox == GPIOB)||(soft_i2c->sda.gpiox == GPIOB))
		rcu_periph_clock_enable(RCU_GPIOB);
	else if((soft_i2c->scl.gpiox == GPIOC)||(soft_i2c->sda.gpiox == GPIOC))
		rcu_periph_clock_enable(RCU_GPIOC);
	else if((soft_i2c->scl.gpiox == GPIOD)||(soft_i2c->sda.gpiox == GPIOD))
		rcu_periph_clock_enable(RCU_GPIOD);
	else if((soft_i2c->scl.gpiox == GPIOE)||(soft_i2c->sda.gpiox == GPIOE))
		rcu_periph_clock_enable(RCU_GPIOE);
	else if((soft_i2c->scl.gpiox == GPIOF)||(soft_i2c->sda.gpiox == GPIOF))
		rcu_periph_clock_enable(RCU_GPIOF);
	else if((soft_i2c->scl.gpiox == GPIOG)||(soft_i2c->sda.gpiox == GPIOG))
		rcu_periph_clock_enable(RCU_GPIOG);
	else if((soft_i2c->scl.gpiox == GPIOH)||(soft_i2c->sda.gpiox == GPIOH))
		rcu_periph_clock_enable(RCU_GPIOH);

	gpio_mode_set(soft_i2c->scl.gpiox,GPIO_MODE_OUTPUT,GPIO_PUPD_PULLUP,soft_i2c->scl.pin);
	gpio_output_options_set(soft_i2c->scl.gpiox, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ,soft_i2c->scl.pin);
	SCL_H(soft_i2c);

	gpio_mode_set(soft_i2c->sda.gpiox,GPIO_MODE_OUTPUT,GPIO_PUPD_PULLUP,soft_i2c->sda.pin);
	gpio_output_options_set(soft_i2c->sda.gpiox, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ,soft_i2c->sda.pin);
	SDA_O_H(soft_i2c);
}

//产生IIC起始信号
void Soft_I2C_Start(BSP_SOFT_I2C_Type* soft_i2c)
{
	Soft_I2C_SDA_Output(soft_i2c);     //sda线输出
	SDA_O_H(soft_i2c);
	SCL_H(soft_i2c);
	delay_us(5);
	SDA_O_L(soft_i2c);//START:when CLK is high,DATA change form high to low 
	delay_us(5);
	SCL_L(soft_i2c);//钳住I2C总线，准备发送或接收数据 
}

//产生IIC停止信号
void Soft_I2C_Stop(BSP_SOFT_I2C_Type* soft_i2c)
{
	Soft_I2C_SDA_Output(soft_i2c);//sda线输出
	SCL_L(soft_i2c);
	SDA_O_L(soft_i2c);//STOP:when CLK is high DATA change form low to high
 	delay_us(4);
	SCL_H(soft_i2c); 
	SDA_O_H(soft_i2c);
	delay_us(4);
//	SCL_L(soft_i2c);
//	delay_us(4);
//	SCL_H(soft_i2c);
//	SDA_O_H(soft_i2c);//发送I2C总线结束信号
}
//等待应答信号到来
//返回值：1，接收应答失败
//        0，接收应答成功
u8 Soft_I2C_Wait_Ack(BSP_SOFT_I2C_Type* soft_i2c)
{
	u8 ucErrTime=0;
	
//	SDA_O_H(soft_i2c);
	delay_us(1);
	Soft_I2C_SDA_Input(soft_i2c);      //SDA设置为输入  
	SCL_H(soft_i2c);delay_us(1);
	while(SDA_I(soft_i2c))
	{
		ucErrTime++;
		if(ucErrTime>250)
		{
			Soft_I2C_Stop(soft_i2c);
			return 1;
		}
	}
	SCL_L(soft_i2c);//时钟输出0
	return 0;  
} 
//产生ACK应答
void Soft_I2C_Ack(BSP_SOFT_I2C_Type* soft_i2c)
{
	SCL_L(soft_i2c);
	Soft_I2C_SDA_Output(soft_i2c);
	SDA_O_L(soft_i2c);
	delay_us(2);
	SCL_H(soft_i2c);
	delay_us(2);
	SCL_L(soft_i2c);
}
//不产生ACK应答
void Soft_I2C_NAck(BSP_SOFT_I2C_Type* soft_i2c)
{
	SCL_L(soft_i2c);
	Soft_I2C_SDA_Output(soft_i2c);
	SDA_O_H(soft_i2c);
	delay_us(2);
	SCL_H(soft_i2c);
	delay_us(2);
	SCL_L(soft_i2c);
}
//IIC发送一个字节
//返回从机有无应答
//1，有应答
//0，无应答
void Soft_I2C_Send_Byte(BSP_SOFT_I2C_Type* soft_i2c,u8 txd)
{
	u8 t;   
	Soft_I2C_SDA_Output(soft_i2c);
	SCL_L(soft_i2c);//拉低时钟开始数据传输
	for(t=0;t<8;t++)
	{
		//SOFT_I2C_SDA=(txd&0x80)>>7;
		if((txd&0x80)>>7)
			SDA_O_H(soft_i2c);
		else
			SDA_O_L(soft_i2c);
		txd<<=1;
		delay_us(1);   //对TEA5767这三个延时都是必须的
		SCL_H(soft_i2c);
		delay_us(4); 
		SCL_L(soft_i2c);
		delay_us(2);
	}
}
//读1个字节，ack=1时，发送ACK，ack=0，发送nACK   
u8 Soft_I2C_Read_Byte(BSP_SOFT_I2C_Type* soft_i2c,unsigned char ack)
{
	unsigned char i,receive=0;
	Soft_I2C_SDA_Input(soft_i2c);//SDA设置为输入
	for(i=0;i<8;i++ )
	{
		SCL_L(soft_i2c); 
		delay_us(3);
		SCL_H(soft_i2c);
		receive = receive<<1;
		if(SDA_I(soft_i2c))receive++;   
		delay_us(2); 
	}
	if (!ack)
		Soft_I2C_NAck(soft_i2c);//发送nACK
	else
		Soft_I2C_Ack(soft_i2c); //发送ACK   
	return receive;
}


void Soft_I2C_SDA_Output(BSP_SOFT_I2C_Type* soft_i2c)
{

	if((soft_i2c->scl.gpiox == GPIOA)||(soft_i2c->sda.gpiox == GPIOA))
		rcu_periph_clock_enable(RCU_GPIOA);
	else if((soft_i2c->scl.gpiox == GPIOB)||(soft_i2c->sda.gpiox == GPIOB))
		rcu_periph_clock_enable(RCU_GPIOB);
	else if((soft_i2c->scl.gpiox == GPIOC)||(soft_i2c->sda.gpiox == GPIOC))
		rcu_periph_clock_enable(RCU_GPIOC);
	else if((soft_i2c->scl.gpiox == GPIOD)||(soft_i2c->sda.gpiox == GPIOD))
		rcu_periph_clock_enable(RCU_GPIOD);
	else if((soft_i2c->scl.gpiox == GPIOE)||(soft_i2c->sda.gpiox == GPIOE))
		rcu_periph_clock_enable(RCU_GPIOE);
	else if((soft_i2c->scl.gpiox == GPIOF)||(soft_i2c->sda.gpiox == GPIOF))
		rcu_periph_clock_enable(RCU_GPIOF);
	else if((soft_i2c->scl.gpiox == GPIOG)||(soft_i2c->sda.gpiox == GPIOG))
		rcu_periph_clock_enable(RCU_GPIOG);

	gpio_mode_set(soft_i2c->sda.gpiox,GPIO_MODE_OUTPUT,GPIO_PUPD_PULLUP,soft_i2c->sda.pin);
	gpio_output_options_set(soft_i2c->sda.gpiox, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ,soft_i2c->sda.pin);
}

void Soft_I2C_SDA_Input(BSP_SOFT_I2C_Type* soft_i2c)
{
	if((soft_i2c->scl.gpiox == GPIOA)||(soft_i2c->sda.gpiox == GPIOA))
		rcu_periph_clock_enable(RCU_GPIOA);
	else if((soft_i2c->scl.gpiox == GPIOB)||(soft_i2c->sda.gpiox == GPIOB))
		rcu_periph_clock_enable(RCU_GPIOB);
	else if((soft_i2c->scl.gpiox == GPIOC)||(soft_i2c->sda.gpiox == GPIOC))
		rcu_periph_clock_enable(RCU_GPIOC);
	else if((soft_i2c->scl.gpiox == GPIOD)||(soft_i2c->sda.gpiox == GPIOD))
		rcu_periph_clock_enable(RCU_GPIOD);
	else if((soft_i2c->scl.gpiox == GPIOE)||(soft_i2c->sda.gpiox == GPIOE))
		rcu_periph_clock_enable(RCU_GPIOE);
	else if((soft_i2c->scl.gpiox == GPIOF)||(soft_i2c->sda.gpiox == GPIOF))
		rcu_periph_clock_enable(RCU_GPIOF);
	else if((soft_i2c->scl.gpiox == GPIOG)||(soft_i2c->sda.gpiox == GPIOG))
		rcu_periph_clock_enable(RCU_GPIOG);

	gpio_mode_set(soft_i2c->sda.gpiox,GPIO_MODE_INPUT,GPIO_PUPD_NONE,soft_i2c->sda.pin);
}
