#include "appmain.h"
#include "logger.h"
#include "math.h"
#include "Time_Unify.h"
#include "INS_Data.h"

char g_charBuf[LOG_BUF_SIZE] = {0};
char g_headCharBuf[LOG_BUF_SIZE] = {0};
extern int g_gpsWeek;							//GPS		
extern double g_gpsSecond;						//GPS		

//****************************************************************************//
//* 	:	generateCSVLogFileName
//* 		:	char* fileName, 		
//* 	:	int,  1 fail   0 success
//****************************************************************************//
//* 	:	
//****************************************************************************//
int generateCSVLogFileName(char* fileName)
{
	char tName[LOG_BUF_SIZE] = {0};
	char tYear[5] = {0};
	char tMon[3] = {0};
	char tDay[3] = {0};
	sow2Date(&g_gpsWeek,&g_gpsSecond,tYear,tMon,tDay);
	memset(tName,0,LOG_BUF_SIZE);
	sprintf(tName,"\\TestLog\\%s_%s_%s_TestLog.csv",tYear,tMon,tDay);
	strcpy(fileName,tName);
	return 0;
}

//****************************************************************************//
//* 		:	writeCSVLog
//* 		:	unsigned char* filename,	
//* 		:	LogBufTypeDef* pLog		
//* 		:	int,	1 fail		0 success
//****************************************************************************//
//* 	:	
//****************************************************************************//
int writeCSVLog(unsigned char* filename, LogBufTypeDef* pLog)
{
/*	uint8_t err;
	int8_t status;
	uint8_t* pbuf;
	
	uint16_t wroteLen = 0;
	uint16_t wroteSize = 0;		//
	uint16_t size = 0;
	
	err = CH378GetDiskStatus();

	if((filename == NULL)||(pLog == NULL)||(pLog->nSize == 0)||(pLog->pbuf == NULL))
		return 1;
	CH378SetFileName(filename);
	err = CH378FileOpen(filename);
	switch(err)
	{
		case ERR_NO_NAME:				// 
		case ERR_MISS_DIR:				// 
		case ERR_MISS_FILE:				// 
		{
			CH378FileCreate(filename);
			CH378_mDelaymS(50);
//			CH378FileClose(1);
//			CH378FileClose(1);
//			CH378FileClose(1);
//			CH378FileClose(1);
//			s = CH378FileClose(1);
			status = 1;
		}
			break;
		case ERR_FOUND_NAME:			// 
			
		{
			CH378ByteLocate(0xFFFFFFFF);		//
			status = 0;
		}
			break;
		case ERR_USB_UNKNOWN:			// 
		case ERR_BUF_OVER:				// 
		case ERR_LONG_NAME:				// 
		case ERR_LARGE_SECTOR:			// ,512
		case ERR_TYPE_ERROR:			// ,FAT12/FAT16/BigDOS/FAT32
		case ERR_BPB_ERROR:				// ,
		case ERR_DISK_FULL:				//  
		case ERR_FDT_OVER:				// , 512
		{
			printf("USB Disk error, Error code:%x",err);
			status = -1;
		}
			break;
		case ERR_FILE_CLOSE:						// 
		{
			status = -1;
		}
			break;
		default:
			status = -1;
			printf("USB Disk error, Error code:%x",err);
			break;
	}
	if(status == 0)									//
	{
		pbuf = (uint8_t *)pLog->pbuf;
		size = pLog->nSize;
		while(wroteLen != pLog->nSize)
		{
			pbuf = (uint8_t *)pLog->pbuf+wroteSize;
			size = size-wroteSize;
			CH378ByteWrite((uint8_t *)pbuf,size,&wroteSize);
			wroteLen = wroteLen + wroteSize;
		}
		if(wroteLen == pLog->nSize)					//
			return 0;
	}
	else if(status == 1)							//
	{
		err = CH378FileOpen(filename);
		switch(err)
		{
			case ERR_FOUND_NAME:					// 
			{
				CH378ByteLocate(0xFFFFFFFF);		//
				if( 0 == writeCSVFileHead())
					status = 0;
			}
				break;
			default:
				status = -1;
				break;
		}
		if( status == 0)
			return 0;
		else
			return 1;
	}
	return 1;*/
}

int writeCSVFileHead(void)
{
	/*uint16_t wroteLen = 0,wroteSize = 0;
	uint16_t len,size;
	char* pBf;
	memset(g_headCharBuf, 0, LOG_BUF_SIZE);
	strcpy(g_headCharBuf,"Date,Time,Roll,Pitch,Yaw,GyroX,GyroY,GyroZ,AccX,AccY,AccZ,Lontitude,Latitude,LonStd,LatStd,Altitude,AltitudeStd,Speed_North,Speed_East,Speed_Earth,State,StarNum,Temp,ResolveState\r\n");
	len = strlen(g_headCharBuf);
	size = len;
	pBf = g_headCharBuf;
	while(wroteLen != strlen(g_headCharBuf))
	{
		size = size-wroteSize;
		pBf = pBf+wroteSize;
		CH378ByteWrite((uint8_t *)pBf,size,&wroteSize);
		wroteLen = wroteLen + wroteSize;
		if(wroteLen>len)
			return 1;
	}
	if(wroteLen == len)					//
		return 0;
	else
		return 1;*/
}

//****************************************************************************//
//* 	:	synthesisLogBuf
//* 		:	uint8_t* pBuf, 			
//* 		:	uint32_t nSize, 		
//* 		:	uint16_t type, 			
//* 		:	LogBufTypeDef* pLog		
//* 	:	int,  1 fail   0 success
//****************************************************************************//
//* 	:	, 
//****************************************************************************//
int synthesisLogBuf(uint8_t* pBuf, uint32_t nSize, uint16_t type, LogBufTypeDef* pLog)
{
	gtime_t curTime;
	float angle[3];
	float gyro[3];
	float acc[3];
	float lat;
	float lon;
	float height;
	float speed_north;
	float speed_east;
	float speed_earth;
	uint8_t state;
	double time;
	uint32_t week;
	float temperature;
	float LonStd;				// , : 
	float LatStd;				// , : 
	float AltitudeStd;			// , : m
	uint8_t StarNum;			//  
	uint16_t ResolveState;
	float speed_nor_std;		//
	float speed_est_std;		//
	float speed_eth_std;		//
	float rollStd;				//
	float pitchStd;				//
	float yawStd;				//
	uint16_t wheelState;		//
	uint8_t GPSSpeedState;		//GPS
	
	char timeStr[25] = {0};
	memset(pLog->pbuf, 0, LOG_BUF_SIZE);
	
	if((pBuf == NULL)||(pLog == NULL))
		return 1;
	else
	{
		pLog->pbuf = g_charBuf;
		curTime = gpst2time(week,time);			//GPS
		curTime = gpst2utc(curTime);			//GPSUTC
		parseFPGABuff(angle,gyro,acc,&lat,&lon,&height,&speed_north,&speed_east,&speed_earth,\
					&state,(float*)&time,&week,&LonStd,&LatStd,&AltitudeStd,&StarNum,&speed_nor_std,&speed_est_std,&speed_eth_std,\
					&rollStd,&pitchStd,&yawStd,&temperature,&wheelState,&GPSSpeedState,&ResolveState);
		switch(type)
		{
			case Log_Type_0:
				memset(timeStr,0,25);
				time2str(curTime,timeStr,3);	//UTC
				sprintf(pLog->pbuf,"%s,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f,%.8f,%.8f,%.8f,%.8f,%.1f,%.8f,%.1f,%.1f,%.1f,%d,%d,%.1f,%d\r\n",\
					timeStr,angle[0],angle[1],angle[2],gyro[0],gyro[1],gyro[2],acc[0],acc[1],acc[2],\
					lat,lon,LatStd,LonStd,height,AltitudeStd,\
					speed_north,speed_east,speed_earth,state,StarNum,temperature,ResolveState);
				break;
			default:
				break;
		}
	}
	pLog->nSize = strlen(g_charBuf);
	return 0;
}

int parseFPGABuff(float* vAngle, float* vGyro, float* vAcc, float* lat, float* lon,float* height, \
					float* speed_nor,float* speed_est,float* speed_earth, uint8_t* state, float* time, uint32_t* week,\
						float*LonStd, float* LatStd, float* AltitudeStd, uint8_t* StarNum, \
					float* vn_std,float* ve_std, float* vd_std,float* rollstd,float* pitchstd,float* yawstd,float* temper,\
						uint16_t* wheelState,uint8_t* GPSSpeedState,uint16_t* ResolveState)
{
	if ( fpga_data_read_flag == 1)
	{
		fpga_data_read_flag = 0;
		if((hINSFPGAData.data_stream.header[0]== 0xBD)&&(hINSFPGAData.data_stream.header[1] == 0xDB)&&(hINSFPGAData.data_stream.header[2] == 0x0B))
		{
			//
			vAngle[0] = hINSFPGAData.data_stream.roll*360.0f/32768.0f;
			vAngle[1] = hINSFPGAData.data_stream.pitch*360.0f/32768.0f;
			vAngle[2] = hINSFPGAData.data_stream.azimuth*360.0f/32768.0f;
			
			vGyro[0] = hINSFPGAData.data_stream.gyroX*300.0f/32768.0f;
			vGyro[1] = hINSFPGAData.data_stream.gyroY*300.0f/32768.0f;
			vGyro[2] = hINSFPGAData.data_stream.gyroZ*300.0f/32768.0f;
			
			vAcc[0] = hINSFPGAData.data_stream.accelX*12.0f/32768.0f;
			vAcc[1] = hINSFPGAData.data_stream.accelY*12.0f/32768.0f;
			vAcc[2] = hINSFPGAData.data_stream.accelZ*12.0f/32768.0f;
			
			*lat = hINSFPGAData.data_stream.latitude*1.0E-7;
			*lon = hINSFPGAData.data_stream.longitude*1.0E-7;
			*height = hINSFPGAData.data_stream.altitude*1.0E-3;
			
			*speed_nor = hINSFPGAData.data_stream.speed_N*1.0E2 / 32768.0f;
			*speed_est = hINSFPGAData.data_stream.speed_E*1.0E2 / 32768.0f;
			*speed_earth = hINSFPGAData.data_stream.speed_G*1.0E2 / 32768.0f;
			*state = hINSFPGAData.data_stream.status;
			*time = hINSFPGAData.data_stream.poll_frame.gps_time*0.25f;
			*week = hINSFPGAData.data_stream.gps_week;
			
			switch(hINSFPGAData.data_stream.poll_frame.type)
			{
				case 0:					//
					*LatStd = exp(hINSFPGAData.data_stream.poll_frame.data1 / 100.0f);
					*LonStd = exp(hINSFPGAData.data_stream.poll_frame.data2 / 100.0f);
					*AltitudeStd = exp(hINSFPGAData.data_stream.poll_frame.data3 / 100.0f);
					break;
				case 1:					//
					*vn_std = exp(hINSFPGAData.data_stream.poll_frame.data1 / 100.0f);
					*ve_std = exp(hINSFPGAData.data_stream.poll_frame.data2 / 100.0f);
					*vd_std = exp(hINSFPGAData.data_stream.poll_frame.data3 / 100.0f);
					break;
				case 2:					//
					*rollstd = exp(hINSFPGAData.data_stream.poll_frame.data1 / 100.0f);
					*pitchstd = exp(hINSFPGAData.data_stream.poll_frame.data2 / 100.0f);
					*yawstd = exp(hINSFPGAData.data_stream.poll_frame.data3 / 100.0f);
					break;
				case 22:				//
					*temper = hINSFPGAData.data_stream.poll_frame.data1*200.0f / 32768.0f;
					break;
				case 32:				//GPS
					*ResolveState = hINSFPGAData.data_stream.poll_frame.data1;
					*StarNum = hINSFPGAData.data_stream.poll_frame.data2;
					*GPSSpeedState = hINSFPGAData.data_stream.poll_frame.data3;
					break;
				case 33:				//
					*wheelState = hINSFPGAData.data_stream.poll_frame.data2;;
					break;
				default:
					break;
			}
		}
		else if((hINSFPGAData.data_stream.header[0]== 0xBD)&&(hINSFPGAData.data_stream.header[1] == 0xDB)&&(hINSFPGAData.data_stream.header[2] == 0x0A))
		{
			//IMU
			
		}
		else if((hINSFPGAData.data_stream.header[0]== 0xBD)&&(hINSFPGAData.data_stream.header[1] == 0xDB)&&(hINSFPGAData.data_stream.header[2] == 0x0C))
		{
			//
			
		}
		else
			return 1;
	}
	return 0;
}

void SDFile2USB(void)
{
	
}

uint8_t  fileBuf[ 64 ];												 /*  */

/*******************************************************************************
* Function Name  : CopyAndConvertFile
* Description    : ,
*                  RAM,
* Input          : SrcFileName---,
*				   TarFileName---,
* Output         : None
* Return         : 
*******************************************************************************/
uint8_t CopyAndConvertFile( uint8_t *SrcFileName, uint8_t *TarFileName ) 
{  
#if 0
	uint8_t  s;
	uint16_t ThisLen, cnt;
	uint32_t FileSize, ByteCount;

	FileSize = 0;
	ByteCount = 0;
	do 
	{
		/*  */
		printf( "OpenSrc\n" );  								 /* , */
		s = CH378FileOpen( SrcFileName );  						 
		if( s != ERR_SUCCESS ) 
		{
			return( s );
		}	
		if( ByteCount == 0 ) 
		{  
			/*  */
			FileSize = CH378GetFileSize( );  					 /*  */
			printf( "SrcFileSz=%ld\n", FileSize );
		}
		else 
		{  
			/*  */
			s = CH378ByteLocate( ByteCount );  					 /*  */
			if( s != ERR_SUCCESS ) 
			{
				return( s );
			}
		}

		/*  */
		printf( "Read\n" );
		s = CH378ByteRead( fileBuf, sizeof( fileBuf ), &ThisLen );  	 /* ,,ThisLen */
		if( s != ERR_SUCCESS ) 
		{
			return( s );
		}
#if  0
		s = CH378FileClose( FALSE );  							 /* , */
		if( s != ERR_SUCCESS ) 
		{
			return( s );
		}
#endif

		/*  */
		for( cnt = 0; cnt < ThisLen; cnt ++ ) 
		{  
			s = fileBuf[ cnt ];
			if( s >= 'a' && s <= 'z' ) 
			{
				fileBuf[ cnt ] = s - ( 'a' - 'A' );
			}
		}

		/*  */
		if( ByteCount == 0 ) 
		{  
			/* , */
			printf( "CreateTar\n" );
			s = CH378FileCreate( TarFileName );  				 /* , */
			if( s != ERR_SUCCESS ) 
			{
				return( s );
			}
		}
		else 
		{  
			/* , */
			printf( "OpenTar\n" );
			s = CH378FileOpen( TarFileName );  					  /*  */
			if( s != ERR_SUCCESS ) 
			{
				return( s );
			}
			s = CH378ByteLocate( ByteCount );  					  /*  */
			if( s != ERR_SUCCESS ) 
			{
				return( s );
			}
		}

		/*  */
		printf( "Write\n" );
		s = CH378ByteWrite( fileBuf, ThisLen, NULL );  				  /* ,,ThisLen */
		if( s != ERR_SUCCESS ) 
		{
			return( s );
		}

		/*  */
		printf( "CloseTar\n" );
		s = CH378FileClose( TRUE );  							  /* , */
		if( s != ERR_SUCCESS ) 
		{
			return( s );
		}
		ByteCount += ThisLen;
		if( ThisLen < sizeof( fileBuf ) ) 
		{  
			/* , */
			if( ByteCount != FileSize ) 
			{
				printf( "Error On SourceFile Reading!!!" );
			}
			break;
		}
	}while( ByteCount < FileSize );
	return( ERR_SUCCESS );
#endif
}
