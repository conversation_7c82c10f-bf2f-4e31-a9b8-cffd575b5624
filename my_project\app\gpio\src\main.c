//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：mian.c
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.15
//---------------------------------------------------------

#include "main.h"
#include "uart.h"
#include "timer.h"
#include "flash.h"
#include "sdram.h"
#include "gd32f4xx_it.h"
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"
#include "bsp_gpio.h"
#include "deviceconfig.h"
#include "hpm_ppor_drv.h"

uint8_t uart4_sendImuFlag = 0;
uint8_t Original_Data = 0; // 0:正常版本，2:原始数据输出

static volatile uint64_t gpio_isr_rel_time; /* mark the real time from mchtimer */
static volatile uint64_t gpio_isr_pre_time; /* mark the last time marked on gpio_isr */
static volatile uint32_t level_value;       /* mark the level */
static volatile uint32_t pre_level_value;   /* mark the level before enter the isr */
static uint32_t debounce_threshold;         /* debounce threshold */

extern uint8_t fpga_syn;
extern uint32_t fpga_syn_count;
extern uint32_t fpga_loop_count;
uint8_t g_StartUpdateFirm = 0; // 开始升级标志 1:开始升级 0:结束升级
extern void ff_handle_poll(void);

void isr_gpio(void)
{
    // gpio_isr_rel_time = mchtmr_get_count(HPM_MCHTMR);
    // level_value = gpio_read_pin(BOARD_APP_GPIO_CTRL, BOARD_APP_GPIO_INDEX, BOARD_APP_GPIO_PIN);
    // if ((gpio_isr_rel_time - gpio_isr_pre_time > debounce_threshold) && (level_value == 0) && (pre_level_value == 1))
    // if (level_value == 1)
    {
        // gpio_isr_pre_time = gpio_isr_rel_time;
        EXTI3_IRQHandler(); // 检测FPGA的外部中断
    }

    gpio_clear_pin_interrupt_flag(BOARD_APP_GPIO_CTRL, BOARD_APP_GPIO_INDEX,
                                  BOARD_APP_GPIO_PIN);
}
SDK_DECLARE_EXT_ISR_M(BOARD_APP_GPIO_IRQ, isr_gpio)

void init_gpio(void)
{
    uint32_t pad_ctl = IOC_PAD_PAD_CTL_PE_SET(1) | IOC_PAD_PAD_CTL_PS_SET(1);

    HPM_IOC->PAD[IOC_PAD_PB09].FUNC_CTL = IOC_PB09_FUNC_CTL_GPIO_B_09;
    HPM_IOC->PAD[IOC_PAD_PB09].PAD_CTL = pad_ctl;

    // 用于调试
    //HPM_PIOC->PAD[IOC_PAD_PY07].FUNC_CTL = PIOC_PY07_FUNC_CTL_SOC_PY_07;
    //HPM_IOC->PAD[IOC_PAD_PY07].FUNC_CTL = IOC_PY07_FUNC_CTL_GPIO_Y_07;
    //gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOY, 7, gpiom_soc_gpio0);
    //gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOY, 7);
    //gpio_write_pin(HPM_GPIO0, GPIO_DO_GPIOY, 7, 0);
}

void test_gpio_input_interrupt(void)
{
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOB, 9, gpiom_soc_gpio0);
    gpio_set_pin_input(HPM_GPIO0, GPIO_OE_GPIOB, 9);
    gpio_enable_pin_interrupt(HPM_GPIO0, GPIO_IE_GPIOB, 9);
    gpio_config_pin_interrupt(HPM_GPIO0, GPIO_DI_GPIOB, 9, gpio_interrupt_trigger_edge_rising);
    intc_m_enable_irq_with_priority(BOARD_APP_GPIO_IRQ, 1);
}

void Exti_Init(void)
{
    init_gpio();
    printf("gpio example\n");

    test_gpio_input_interrupt();
}

void Led_Control(void)
{
    gpio_toggle_pin(HPM_GPIO0, LED_SRAM_IO_PORT, LED_SRAM_IO_PIN);
}


uint32_t get_boot_reason(void)
{
    printf("boot reason:0x%08x!\r\n", HPM_PPOR->RESET_FLAG);
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_brownout) != 0U)
        printf("boot reason:ppor_reset_brownout\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_debug) != 0U)
        printf("boot reason:ppor_reset_debug\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_wdog0) != 0U)
        printf("boot reason:ppor_reset_wdog0\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_wdog1) != 0U)
        printf("boot reason:ppor_reset_wdog1\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_wdog2) != 0U)
        printf("boot reason:ppor_reset_wdog2\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_wdog3) != 0U)
        printf("boot reason:ppor_reset_wdog3\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_pmic_wdog) != 0U)
        printf("boot reason:ppor_reset_pmic_wdog\r\n");
    if ((HPM_PPOR->RESET_FLAG & ppor_reset_software) != 0U)
        printf("boot reason:ppor_reset_software\r\n");
    return HPM_PPOR->RESET_FLAG;
}

int main(void)
{
    uint32_t uiLedCnt = 0;

    board_init();

    get_boot_reason();

    SysInit(); // Init inertial navigation device...

    ReadParaFromFlash();

    DeviceInit(); // 系统初始化完成后，进入循环前的准备
    delay_ms(1000);

    char txbuf[50] = {0};
    sprintf(txbuf, "\r\n Into INS 370 Init!\n");
    uart4sendmsg(txbuf, strlen(txbuf));

    while (1)
    {
        if (fpga_syn == 1) // 每一帧FPGA数据产生，处理
        {
            fpga_syn = 0;
            SdFileReadOperate();     // 读取SD卡数据
            get_fpgadata();          // 1、获取当前帧FPGA数据，及相关
            AlgorithmDo();           // 2、对获取的数据进行算法处理
            INS912_Output(&gnavout); // 4、算法处理完成的数据，进行打包、发送处理
            uiLedCnt++;
            if (uiLedCnt >= 20)
            {
                uiLedCnt = 0;
                Led_Control();
            }
        }
#ifdef DEVICE_ACC_TYPE_ADLX355
        l355_uart_recv_polling();
#endif
        loopDoOther(); // 循环中，处理其它事宜
        sduart_recv_polling();
        analysisRxdata(); // 用于参数设置和升级
        SdFileWriteOperate(); // 写数据到SD卡
        ff_handle_poll();
    }

    return 0;
}
