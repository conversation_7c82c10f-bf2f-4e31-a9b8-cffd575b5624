# 编译问题解决检查清单

## 🎯 当前状态

### 已出现的文件错误：
- ❌ `adxl355.c` - 包含HPM6750特定头文件
- ❌ `sins2.c` - 大量兼容性问题
- ⚠️ `ahrs.c` - 应该正常，可能是包含路径问题

## 📋 必须完成的操作

### ✅ 已创建的文件：
- [x] `RTE_Components.h` - 在项目根目录
- [x] `board.h` - 在项目根目录
- [x] `adxl355_gd32.c` - GD32适配版本（可选）

### 🔧 必须配置的包含路径：
在Keil项目选项 → C/C++ → Include Paths中添加：

```
.                    ← 项目根目录 (RTE_Components.h, board.h)
../INAV              ← INAV算法头文件
../Source/Edwoy      ← app_tool.h位置
../Source/inc        ← Source头文件
../Protocol          ← Protocol头文件
../Common/inc        ← Common头文件
../bsp/inc           ← BSP头文件
../Library/CMSIS     ← CMSIS库
../Library/GD32F4xx_standard_peripheral/Include ← GD32标准库
../NAV               ← NAV头文件
../RTT               ← RTT头文件
```

### 🗑️ 必须移除的有问题文件：
在Keil项目中移除以下文件：

- [ ] `INAV/adxl355.c` - 包含HPM6750特定代码
- [ ] `INAV/sins2.c` - 大量兼容性问题

### 🔄 编译测试步骤：
1. [ ] Clean项目：`Project -> Clean Targets`
2. [ ] Rebuild项目：`Project -> Rebuild all target files`
3. [ ] 检查编译输出

## 📊 预期结果

### 成功标志：
- ✅ 编译输出中看到INAV文件被编译：
  ```
  compiling navi.c...
  compiling kalman.c...
  compiling align.c...
  compiling ahrs.c...
  compiling sins.c...
  compiling ins.c...
  ```

- ✅ 没有"找不到头文件"错误
- ✅ 错误数量大幅减少（目标：<10个错误）

### 不应该看到的错误：
- ❌ `cannot open source input file "RTE_Components.h"`
- ❌ `cannot open source input file "board.h"`
- ❌ `cannot open source input file "app_tool.h"`
- ❌ `Browse information of one or more files is not available`

## 🔍 故障排除

### 如果ahrs.c仍有问题：

#### 检查1：验证头文件存在
```
✅ INS370-8K24-INS600M-21A/INAV/ins.h
✅ INS370-8K24-INS600M-21A/INAV/CONST.h
```

#### 检查2：验证包含路径
确保 `../INAV` 在包含路径中

#### 检查3：检查文件编码
ahrs.c文件可能有编码问题，可以尝试：
1. 用记事本打开ahrs.c
2. 另存为UTF-8编码
3. 重新编译

### 如果仍有其他文件错误：

#### 策略1：逐个移除有问题的文件
如果出现其他INAV文件的类似错误，可以暂时移除：
- 检查文件是否包含HPM6750特定头文件
- 如果是，暂时移除该文件
- 保留核心算法文件：`navi.c`, `kalman.c`, `align.c`, `sins.c`, `ins.c`

#### 策略2：检查文件依赖
某些文件可能依赖于已移除的文件，需要：
1. 检查编译错误信息
2. 找到依赖关系
3. 修复或移除依赖

## 🎯 核心目标

### 最小可工作配置：
保留以下核心INAV文件即可实现完整导航功能：
- ✅ `navi.c` - 导航核心算法
- ✅ `kalman.c` - Kalman滤波
- ✅ `align.c` - 对准算法
- ✅ `sins.c` - 主要SINS算法
- ✅ `ins.c` - INS接口
- ✅ `ahrs.c` - 姿态算法（如果能编译）
- ✅ `compen.c` - 补偿算法
- ✅ `matvecmath.c` - 数学运算

### 可选文件：
以下文件可以根据需要添加：
- `fuseGnssVelPosHeight.c` - GNSS融合
- `fuseTwoAntHeading.c` - 双天线融合
- `fuseBodyOdomVel.c` - 里程计融合
- `dynamic_align.c` - 动态对准
- `private_math.c` - 私有数学函数

## 📝 操作记录

请在完成每个步骤后打勾：

### 包含路径配置：
- [ ] 添加 `.` 到包含路径
- [ ] 添加 `../INAV` 到包含路径
- [ ] 添加 `../Source/Edwoy` 到包含路径
- [ ] 添加其他必要路径

### 文件移除：
- [ ] 移除 `adxl355.c`
- [ ] 移除 `sins2.c`
- [ ] 移除其他有问题的文件（如果有）

### 编译测试：
- [ ] Clean项目
- [ ] Rebuild项目
- [ ] 检查错误数量
- [ ] 记录剩余错误类型

完成这些步骤后，编译错误应该大幅减少。如果仍有问题，请提供具体的错误信息以便进一步诊断。
