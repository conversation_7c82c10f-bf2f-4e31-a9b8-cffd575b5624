#include "bsp_i2c.h"
#include "bmp280.h"

volatile char i2c_transfer_done = 0;

void bsp_i2c_init(void)
{
	/* enable GPIOB clock */
	rcu_periph_clock_enable(RCU_GPIOH);
	/* enable I2C1 clock */
	rcu_periph_clock_enable(RCU_I2C1);
	
	
	/* connect PH4 to I2C1_SCL */
	gpio_af_set(GPIOH, GPIO_AF_4, GPIO_PIN_4);
	/* connect PH5 to I2C1_SDA */
	gpio_af_set(GPIOH, GPIO_AF_4, GPIO_PIN_5);

	/* configure I2C1 GPIO */
	gpio_mode_set(GPIOH, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_4);
	gpio_output_options_set(GPIOH, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_4);
	gpio_mode_set(GPIOH, GPIO_MODE_AF, <PERSON><PERSON>_PUPD_PULLUP, GPIO_PIN_5);
	gpio_output_options_set(GPIOH, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_5);
	
	/* configure I2C clock */
	i2c_clock_config(I2C1, 400000, I2C_DTCY_2);
	/* configure I2C address */
	i2c_mode_addr_config(I2C1, I2C_I2CMODE_ENABLE, I2C_ADDFORMAT_7BITS, I2C1_OWN_ADDRESS7);
	/* enable I2C1 */
	i2c_enable(I2C1);
	/* enable acknowledge */
	i2c_ack_config(I2C1, I2C_ACK_ENABLE);
}

void i2c_bus_reset(void)
{
	i2c_deinit(I2C1);
	/* configure SDA/SCL for GPIO */
	GPIO_BC(GPIOH) |= GPIO_PIN_4;
	GPIO_BC(GPIOH) |= GPIO_PIN_5;
	gpio_output_options_set(GPIOH, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_4);
	gpio_output_options_set(GPIOH, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_5);
	__NOP();
	__NOP();
	__NOP();
	__NOP();
	__NOP();
	GPIO_BOP(GPIOH) |= GPIO_PIN_4;
	__NOP();
	__NOP();
	__NOP();
	__NOP();
	__NOP();
	GPIO_BOP(GPIOH) |= GPIO_PIN_5;
	/* connect I2C_SCL_PIN to I2C_SCL */
	/* connect I2C_SDA_PIN to I2C_SDA */
	gpio_output_options_set(GPIOH, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_4);
	gpio_output_options_set(GPIOH, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_5);
	/* configure the I2C1 interface */
	bsp_i2c_init();
}

int8_t i2c_write(uint8_t dev_addr, uint8_t reg_addr, uint8_t *pTxBuf,  uint8_t nBytes)
{
	uint16_t txBuf = 0;
	txBuf = dev_addr<<1;
	i2c_start_on_bus(I2C1);
	i2c_master_addressing(I2C1,dev_addr,I2C_TRANSMITTER);
	//////////wait ACK
	while(!i2c_flag_get(I2C1, I2C_FLAG_ADDSEND));
	i2c_flag_clear(I2C1, I2C_FLAG_ADDSEND);
	i2c_data_transmit(I2C1,reg_addr);
	//////////wait ACK
	
}

int8_t i2c_read(uint8_t dev_addr, uint8_t reg_addr, uint8_t *pRxBuf,uint16_t nBytes)
{
	
}

int8_t i2c_write_timeout(uint8_t dev_addr, uint8_t reg_addr, uint8_t *pTxBuf,  uint8_t nBytes)
{
	uint8_t state = I2C_START;
	uint16_t timeout = 0;
	uint8_t i2c_timeout_flag = 0;
	int err;
	while(!(i2c_timeout_flag)) {
		switch(state) {
		case I2C_START:
			/* i2c master sends start signal only when the bus is idle */
			while(i2c_flag_get(I2C1, I2C_FLAG_I2CBSY) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				i2c_start_on_bus(I2C1);
				timeout = 0;
				state = I2C_SEND_ADDRESS;
			} else {
				i2c_bus_reset();
				timeout = 0;
				state = I2C_START;
			}
			break;
		case I2C_SEND_ADDRESS:
			/* i2c master sends START signal successfully */
			while((!i2c_flag_get(I2C1, I2C_FLAG_SBSEND)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				i2c_master_addressing(I2C1, dev_addr, I2C_TRANSMITTER);
				timeout = 0;
				state = I2C_CLEAR_ADDRESS_FLAG;
			} else {
				timeout = 0;
				state = I2C_START;
			}
			break;
		case I2C_CLEAR_ADDRESS_FLAG:
			/* address flag set means i2c slave sends ACK */
			while((!i2c_flag_get(I2C1, I2C_FLAG_ADDSEND)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				i2c_flag_clear(I2C1, I2C_FLAG_ADDSEND);
				timeout = 0;
				state = I2C_TRANSMIT_DATA;
			} else {
				timeout = 0;
				state = I2C_START;
			}
			break;
		case I2C_TRANSMIT_DATA:
			/* wait until the transmit data buffer is empty */
			while((!i2c_flag_get(I2C1, I2C_FLAG_TBE)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				/* send the EEPROM's internal address to write to : only one byte address */
				
				
				
				timeout = 0;
			} else {
				timeout = 0;
				state = I2C_START;
			}
			/* wait until BTC bit is set */
			while((!i2c_flag_get(I2C1, I2C_FLAG_BTC)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				timeout = 0;
			} else {
				timeout = 0;
				state = I2C_START;
			}
			while(nBytes--) {
				i2c_data_transmit(I2C1, *pTxBuf);
				/* point to the next byte to be written */
				pTxBuf++;
				/* wait until BTC bit is set */
				while((!i2c_flag_get(I2C1, I2C_FLAG_BTC)) && (timeout < I2C_TIME_OUT)) {
					timeout++;
				}
				if(timeout < I2C_TIME_OUT) {
					timeout = 0;
				} else {
					timeout = 0;
					state = I2C_START;
				}
			}
			timeout = 0;
			state = I2C_STOP;
			break;
		case I2C_STOP:
			/* send a stop condition to I2C bus */
			i2c_stop_on_bus(I2C1);
			/* i2c master sends STOP signal successfully */
			while((I2C_CTL0(I2C1) & I2C_CTL0_STOP) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				timeout = 0;
				state = 1;
				i2c_timeout_flag = 1;
			} else {
				timeout = 0;
				state = I2C_START;
			}
			break;
		default:
			state = I2C_START;
			i2c_timeout_flag = 1;
			timeout = 0;
			break;
		}
	}
	if (err == 0)
		return 0;
	else
		return -1;
}

int8_t i2c_read_timeout(uint8_t dev_addr, uint8_t reg_addr, uint8_t *pRxBuf,uint16_t nBytes)
{
	int err;
	uint8_t state = I2C_START;
	uint8_t read_cycle = 0;
	uint16_t timeout = 0;
	uint8_t i2c_timeout_flag = 0;

	while(!(i2c_timeout_flag)) {
		switch(state) {
		case I2C_START:
			if(RESET == read_cycle) {
				/* i2c master sends start signal only when the bus is idle */
				while(i2c_flag_get(I2C1, I2C_FLAG_I2CBSY) && (timeout < I2C_TIME_OUT)) {
					timeout++;
				}
				if(timeout < I2C_TIME_OUT) {
					/* whether to send ACK or not for the next byte */
					if(2 == nBytes) {
						i2c_ackpos_config(I2C1, I2C_ACKPOS_NEXT);
					}
				} else {
					i2c_bus_reset();
					timeout = 0;
					state = I2C_START;
				}
			}
			/* send the start signal */
			i2c_start_on_bus(I2C1);
			timeout = 0;
			state = I2C_SEND_ADDRESS;
			break;
		case I2C_SEND_ADDRESS:
			/* i2c master sends START signal successfully */
			while((!i2c_flag_get(I2C1, I2C_FLAG_SBSEND)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				if(RESET == read_cycle) {
					i2c_master_addressing(I2C1, dev_addr, I2C_TRANSMITTER);
					state = I2C_CLEAR_ADDRESS_FLAG;
				} else {
					i2c_master_addressing(I2C1, dev_addr, I2C_RECEIVER);
					if(nBytes < 3) {
						/* disable acknowledge */
						i2c_ack_config(I2C1, I2C_ACK_DISABLE);
					}
					state = I2C_CLEAR_ADDRESS_FLAG;
				}
				timeout = 0;
			} else {
				timeout = 0;
				state = I2C_START;
				read_cycle = 0;
			}
			break;
		case I2C_CLEAR_ADDRESS_FLAG:
			/* address flag set means i2c slave sends ACK */
			while((!i2c_flag_get(I2C1, I2C_FLAG_ADDSEND)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				i2c_flag_clear(I2C1, I2C_FLAG_ADDSEND);
				if((SET == read_cycle) && (1 == nBytes)) {
					/* send a stop condition to I2C bus */
					i2c_stop_on_bus(I2C1);
				}
				timeout = 0;
				state = I2C_TRANSMIT_ADDR;
			} else {
				timeout = 0;
				state = I2C_START;
				read_cycle = 0;
			}
			break;
		case I2C_TRANSMIT_ADDR:
			/* wait until the transmit data buffer is empty */
			while((! i2c_flag_get(I2C1, I2C_FLAG_TBE)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				/* send the EEPROM's internal address to write to : only one byte address */
				i2c_data_transmit(I2C1, reg_addr);
				timeout = 0;
//			} else {
//				timeout = 0;
				state = I2C_RESTART;
				read_cycle = 0;
			}
			break;
		case I2C_RESTART:
			/* wait until the transmit data buffer is empty */
			while((! i2c_flag_get(I2C1, I2C_FLAG_TBE)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				/* send the EEPROM's internal address to write to : only one byte address */
				i2c_start_on_bus(I2C1);
				timeout = 0;
//			} else {
//				timeout = 0;
				state = I2C_RECEIVE_ADDA;
				read_cycle = 0;
			}
			break;
		case I2C_RECEIVE_ADDA:
			/* i2c master sends START signal successfully */
			while((!i2c_flag_get(I2C1, I2C_FLAG_SBSEND)) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				if(RESET == read_cycle) {
					i2c_master_addressing(I2C1, dev_addr+1, I2C_TRANSMITTER);
					i2c_ack_config(I2C1, I2C_ACK_ENABLE);
					state = I2C_RECEIVE_DATA;
				} else {
					i2c_master_addressing(I2C1, dev_addr+1, I2C_RECEIVER);
					if(nBytes < 3) {
						/* disable acknowledge */
						i2c_ack_config(I2C1, I2C_ACK_DISABLE);
					}
					state = I2C_RECEIVE_DATA;
				}
				timeout = 0;
			} else {
				timeout = 0;
				state = I2C_START;
				read_cycle = 0;
			}
			break;
		case I2C_RECEIVE_DATA:
			while(nBytes) {
				timeout++;
				if(3 == nBytes) {
					/* wait until BTC bit is set */
					while(!i2c_flag_get(I2C1, I2C_FLAG_BTC));
					/* disable acknowledge */
					i2c_ack_config(I2C1, I2C_ACK_DISABLE);
				}
				if(2 == nBytes) {
					/* wait until BTC bit is set */
					while(!i2c_flag_get(I2C1, I2C_FLAG_BTC));
					/* send a stop condition to I2C bus */
					i2c_stop_on_bus(I2C1);
				}
				/* wait until RBNE bit is set */
				if(i2c_flag_get(I2C1, I2C_FLAG_RBNE)) {
					/* read a byte from the EEPROM */
					*pRxBuf = i2c_data_receive(I2C1);
					i2c_ack_config(I2C1, I2C_ACK_ENABLE);
					/* point to the next location where the byte read will be saved */
					pRxBuf++;
					/* decrement the read bytes counter */
					nBytes--;
					timeout = 0;
				}
				if(timeout > I2C_TIME_OUT) {
					timeout = 0;
					state = I2C_START;
					read_cycle = 0;
				}
			}
			timeout = 0;
			state = I2C_STOP;
			break;
		case I2C_TRANSMIT_DATA:
			if(RESET == read_cycle) {
				/* wait until the transmit data buffer is empty */
				while((! i2c_flag_get(I2C1, I2C_FLAG_TBE)) && (timeout < I2C_TIME_OUT)) {
					timeout++;
				}
				if(timeout < I2C_TIME_OUT) {
					/* send the EEPROM's internal address to write to : only one byte address */
					i2c_data_transmit(I2C1, reg_addr);
					timeout = 0;
				} else {
					timeout = 0;
					state = I2C_START;
					read_cycle = 0;
				}
				/* wait until BTC bit is set */
				while((!i2c_flag_get(I2C1, I2C_FLAG_BTC)) && (timeout < I2C_TIME_OUT)) {
					timeout++;
				}
				if(timeout < I2C_TIME_OUT) {
					timeout = 0;
					state = I2C_START;
					read_cycle++;
				} else {
					timeout = 0;
					state = I2C_START;
					read_cycle = 0;
				}
			} else {
				while(nBytes) {
					timeout++;
					if(3 == nBytes) {
						/* wait until BTC bit is set */
						while(!i2c_flag_get(I2C1, I2C_FLAG_BTC));
						/* disable acknowledge */
						i2c_ack_config(I2C1, I2C_ACK_DISABLE);
					}
					if(2 == nBytes) {
						/* wait until BTC bit is set */
						while(!i2c_flag_get(I2C1, I2C_FLAG_BTC));
						/* send a stop condition to I2C bus */
						i2c_stop_on_bus(I2C1);
					}
					/* wait until RBNE bit is set */
					if(i2c_flag_get(I2C1, I2C_FLAG_RBNE)) {
						/* read a byte from the EEPROM */
						*pRxBuf = i2c_data_receive(I2C1);
						/* point to the next location where the byte read will be saved */
						pRxBuf++;
						/* decrement the read bytes counter */
						nBytes--;
						timeout = 0;
					}
					if(timeout > I2C_TIME_OUT) {
						timeout = 0;
						state = I2C_START;
						read_cycle = 0;
					}
				}
				timeout = 0;
				state = I2C_STOP;
			}
			break;
		case I2C_STOP:
			/* i2c master sends STOP signal successfully */
			while((I2C_CTL0(I2C1) & I2C_CTL0_STOP) && (timeout < I2C_TIME_OUT)) {
				timeout++;
			}
			if(timeout < I2C_TIME_OUT) {
				timeout = 0;
				state = 1;
				i2c_timeout_flag = 1;
				err = 0;
			} else {
				err = 1;
				timeout = 0;
				state = I2C_START;
				read_cycle = 0;
			}
			break;
		default:
			err = 1;
			state = I2C_START;
			read_cycle = 0;
			i2c_timeout_flag = 1;
			timeout = 0;
			break;
		}
	}
	
	if (err == 0)
		return 0;
	else
		return -1;
}

