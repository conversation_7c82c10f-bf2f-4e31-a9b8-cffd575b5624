# 航向角掉电保存修复测试程序 Makefile

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -O0
INCLUDES = -I../NAV -I../SetPara -I.

# 源文件
SOURCES = main_test_azimuth_fix.c \
          test_azimuth_fix.c \
          ../SetPara/SetParaBao.c \
          ../NAV/nav_sins.c \
          ../NAV/nav_math.c

DEBUG_SOURCES = debug_azimuth_issue.c \
                ../SetPara/SetParaBao.c \
                ../NAV/nav_sins.c \
                ../NAV/nav_math.c

PROTECTION_SOURCES = test_azimuth_protection.c \
                     ../SetPara/SetParaBao.c \
                     ../NAV/nav_sins.c \
                     ../NAV/nav_math.c

# 目标文件
OBJECTS = $(SOURCES:.c=.o)
DEBUG_OBJECTS = $(DEBUG_SOURCES:.c=.o)
PROTECTION_OBJECTS = $(PROTECTION_SOURCES:.c=.o)

# 可执行文件
TARGET = test_azimuth_fix
DEBUG_TARGET = debug_azimuth_issue
PROTECTION_TARGET = test_azimuth_protection

# 默认目标
all: $(TARGET) $(DEBUG_TARGET) $(PROTECTION_TARGET)

# 链接
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET) -lm

$(DEBUG_TARGET): $(DEBUG_OBJECTS)
	$(CC) $(DEBUG_OBJECTS) -o $(DEBUG_TARGET) -lm

$(PROTECTION_TARGET): $(PROTECTION_OBJECTS)
	$(CC) $(PROTECTION_OBJECTS) -o $(PROTECTION_TARGET) -lm

# 编译
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f $(OBJECTS) $(DEBUG_OBJECTS) $(PROTECTION_OBJECTS) $(TARGET) $(DEBUG_TARGET) $(PROTECTION_TARGET)

# 运行测试
test: $(TARGET)
	./$(TARGET)

# 运行调试程序
debug: $(DEBUG_TARGET)
	./$(DEBUG_TARGET)

# 运行保护功能测试
protection: $(PROTECTION_TARGET)
	./$(PROTECTION_TARGET)

.PHONY: all clean test debug protection
