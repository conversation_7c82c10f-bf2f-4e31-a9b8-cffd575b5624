/*!
    \file  nav_sins.c
    \brief Strapdown Inertial Navigation System wrapper
*/

#include "nav_sins.h"
#include "../INAV/FUNCTION.h"
#include "../INAV/GLOBALDATA.h"

nav_sins_status_t nav_sins_init(void)
{
    SysInit();
    return NAV_SINS_STATUS_OK;
}

nav_sins_status_t nav_sins_update(const nav_imu_data_t *imu_data, nav_sins_result_t *result)
{
    if (imu_data == NULL || result == NULL) {
        return NAV_SINS_STATUS_ERROR;
    }
    
    static DPARA last_gyro[3] = {0};
    static DPARA last_acc[3] = {0};
    
    DPARA gyro[3] = {imu_data->gyro[0], imu_data->gyro[1], imu_data->gyro[2]};
    DPARA acc[3] = {imu_data->acc[0], imu_data->acc[1], imu_data->acc[2]};
    
    NaviCompute(gyro, last_gyro, acc, last_acc, &g_Navi);
    
    result->position[0] = g_Navi.d_Lati;
    result->position[1] = g_Navi.d_Logi;
    result->position[2] = g_Navi.Height;
    
    result->velocity[0] = g_Navi.Vn[0];
    result->velocity[1] = g_Navi.Vn[1];
    result->velocity[2] = g_Navi.Vn[2];
    
    result->attitude[0] = g_Navi.d_Atti[0];
    result->attitude[1] = g_Navi.d_Atti[1];
    result->attitude[2] = g_Navi.d_Atti[2];
    
    for (int i = 0; i < 3; i++) {
        last_gyro[i] = gyro[i];
        last_acc[i] = acc[i];
    }
    
    return NAV_SINS_STATUS_OK;
}
