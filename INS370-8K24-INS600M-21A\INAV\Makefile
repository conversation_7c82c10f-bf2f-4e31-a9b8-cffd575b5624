# INAV算法测试编译Makefile
# 用于编译和测试移植的INAV算法

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -O0 -DTEST_MODE
INCLUDES = -I. -I../Common/inc -I../Source/inc -I../Protocol

# INAV算法源文件
INAV_SOURCES = navi.c \
               kalman.c \
               align.c \
               matvecmath.c \
               inav_main.c

# 测试源文件
TEST_SOURCES = test_inav.c

# 所有源文件
SOURCES = $(INAV_SOURCES) $(TEST_SOURCES)

# 目标文件
OBJECTS = $(SOURCES:.c=.o)

# 可执行文件
TARGET = test_inav

# 默认目标
all: $(TARGET)

# 链接
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET) -lm

# 编译
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f $(OBJECTS) $(TARGET)

# 运行测试
test: $(TARGET)
	./$(TARGET)

# 检查语法
check:
	$(CC) $(CFLAGS) $(INCLUDES) -fsyntax-only $(INAV_SOURCES)

# 显示帮助
help:
	@echo "可用目标:"
	@echo "  all      - 编译所有文件"
	@echo "  test     - 编译并运行测试"
	@echo "  check    - 检查语法"
	@echo "  clean    - 清理生成的文件"
	@echo "  help     - 显示此帮助信息"

.PHONY: all clean test check help
