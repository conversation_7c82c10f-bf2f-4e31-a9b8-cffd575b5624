//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：sd_fatfs.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2025.03.14
//---------------------------------------------------------
#include "appmain.h"
#include "uart.h"
#include <stdlib.h>
#include <stdio.h>

//#define SD_STR_DEF         //SD卡已字符串形式保存时，打开这个宏

FIL s_fileBB00;
FIL s_fileCOM3;
FATFS s_sd_disk;
BYTE work[FF_MAX_SS];

DIR s_dir;
FRESULT fatfs_result;
const TCH<PERSON> driver_num_buf[3] = { DEV_SD + '0', ':', '/' };
uint8_t hello_str[SDRX_MAXCOUNT] = {0};

char *Com3FileName = "Com3Bin.txt";//COM3二进制数据保存文件
char *BB00FileName = "BB00Data.txt";//BB00原始数据保存文件

//#define DEBUG_TEST
#ifdef DEBUG_TEST
char test_str[] = "Hello, this is SD card FATFS demo\n";
#endif

//挂载SD卡文件系统
static FRESULT sd_mount_fs(void)
{
    FRESULT fresult = f_mount(&s_sd_disk, driver_num_buf, 1);
    if (fresult == FR_OK) {
        printf("SD card has been mounted successfully\n");
    } else {
        printf("Failed to mount SD card, cause: %s\n", show_error_string(fresult));
    }

    fresult = f_chdrive(driver_num_buf);
    return fresult;
}

//SD卡格式化
static FRESULT sd_mkfs(void)
{
    printf("Formatting the SD card, depending on the SD card capacity, the formatting process may take a long time\n");
    FRESULT fresult = f_mkfs(driver_num_buf, NULL, work, sizeof(work));
    if (fresult != FR_OK) {
        printf("Making File system failed, cause: %s\n", show_error_string(fresult));
    } else {
        printf("Making file system is successful\n");
    }

    return fresult;
}

const char *show_error_string(FRESULT fresult)
{
    const char *result_str;

    switch (fresult) {
    case FR_OK:
        result_str = "succeeded";
        break;
    case FR_DISK_ERR:
        result_str = "A hard error occurred in the low level disk I/O level";
        break;
    case FR_INT_ERR:
        result_str = "Assertion failed";
        break;
    case FR_NOT_READY:
        result_str = "The physical drive cannot work";
        break;
    case FR_NO_FILE:
        result_str = "Could not find the file";
        break;
    case FR_NO_PATH:
        result_str = "Could not find the path";
        break;
    case FR_INVALID_NAME:
        result_str = "Tha path name format is invalid";
        break;
    case FR_DENIED:
        result_str = "Access denied due to prohibited access or directory full";
        break;
    case FR_EXIST:
        result_str = "Access denied due to prohibited access";
        break;
    case FR_INVALID_OBJECT:
        result_str = "The file/directory object is invalid";
        break;
    case FR_WRITE_PROTECTED:
        result_str = "The physical drive is write protected";
        break;
    case FR_INVALID_DRIVE:
        result_str = "The logical driver number is invalid";
        break;
    case FR_NOT_ENABLED:
        result_str = "The volume has no work area";
        break;
    case FR_NO_FILESYSTEM:
        result_str = "There is no valid FAT volume";
        break;
    case FR_MKFS_ABORTED:
        result_str = "THe f_mkfs() aborted due to any problem";
        break;
    case FR_TIMEOUT:
        result_str = "Could not get a grant to access the volume within defined period";
        break;
    case FR_LOCKED:
        result_str = "The operation is rejected according to the file sharing policy";
        break;
    case FR_NOT_ENOUGH_CORE:
        result_str = "LFN working buffer could not be allocated";
        break;
    case FR_TOO_MANY_OPEN_FILES:
        result_str = "Number of open files > FF_FS_LOCK";
        break;
    case FR_INVALID_PARAMETER:
        result_str = "Given parameter is invalid";
        break;
    default:
        result_str = "Unknown error";
        break;
    }
    return result_str;
}

void  Fatfs_Init(void)
{
    bool need_init_filesystem = true;

    /* Before doing FATFS operation, ensure the SD card is present */
    DSTATUS dstatus = disk_status(DEV_SD);
    if (dstatus == STA_NODISK)
    {
      printf("No disk in the SD slot, please insert an SD card...\n");
      do 
      {
        dstatus = disk_status(DEV_SD);
      } while (dstatus == STA_NODISK);

      //board_delay_ms(100);
      printf("Detected SD card, re-initialize the filesystem...\n");
      need_init_filesystem = true;
    }

    dstatus = disk_initialize(DEV_SD);//SD卡
    if (dstatus != RES_OK) 
    {
      printf("Failed to initialize SD disk\n");
    }
    if (need_init_filesystem) 
    {
      fatfs_result = sd_mount_fs();
      if (fatfs_result == FR_NO_FILESYSTEM) 
      {
          printf("There is no File system available, making file system...\n");
          fatfs_result = sd_mkfs();
          if (fatfs_result != FR_OK) 
          {
            printf("Failed to make filesystem, cause:%s\n", show_error_string(fatfs_result));
          } else 
          {
            need_init_filesystem = false;
          }
      }
    }
}

//COM3二进制数据写入文件到SD卡
void WriteCOM3FileToSd(void)
{
    uint16_t i=0;
    UINT byte_written;
    uint8_t str[SDRX_MAXCOUNT*2]={0};
#ifdef SD_STR_DEF
    for(i=0;i<1000;i++)
      sprintf(&str[2*i], "%02x", hello_str[i]);
    FRESULT fresult = f_write(&s_fileCOM3, str, 2000, &byte_written);
#else
    //FRESULT fresult = f_write(&s_fileCOM3, hello_str, 1000, &byte_written);
    FRESULT fresult = f_async_write(&s_fileCOM3, hello_str, 1000, &byte_written);
#endif

    if (fresult != FR_OK) 
    {
        printf("COM3 %s\n", show_error_string(fresult));
    } 
    else 
    {
        //printf("Write COM3 file operation is successfully\n");
    }
}

uint64_t start_ticks, end_ticks;
uint32_t test_count = 0;
uint32_t read_count = 0;
float read_ms;
//BB00原始数据写入文件到SD卡
void WriteBB00FileToSd(void)
{
    uint16_t i=0;
    UINT byte_written=0;
    uint8_t strBB00[SDRX_BB00_MAXCOUNT*2]={0};
    FRESULT fresult=0;
#ifdef SD_STR_DEF
    for(i=0;i<sizeof(BB00SdData);i++)
      sprintf(&strBB00[2*i], "%02x", BB00SdData[i]);
    fresult = f_write(&s_fileBB00, strBB00, sizeof(strBB00), &byte_written);
#else
    start_ticks = mchtmr_get_count(HPM_MCHTMR);
    //fresult = f_write(&s_fileBB00, BB00SdData, sizeof(BB00SdData), &byte_written);
#ifndef DEBUG_TEST
    fresult = f_async_write(&s_fileBB00, BB00SdData, sizeof(BB00SdData), &byte_written);
#else
    fresult = f_async_write(&s_fileBB00, test_str, sizeof(test_str), &byte_written);
#endif
    end_ticks = mchtmr_get_count(HPM_MCHTMR);
#endif
    test_count++;
    read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
    //printf("write tick:%lld\r\n", end_ticks - start_ticks);
    //printf("t:%f\r\n", read_ms);
    if(read_ms >= 5.0f)
    {
        printf("-----timeout:%f!\r\n", read_ms);
    }

    if (fresult != FR_OK) 
    {
        printf("Write BB00 file failed, 8use: %s\n", show_error_string(fresult));
    } 
    else 
    {
        //printf("Write BB00 file operation is successfully\n");
    }
}

//从SD卡打开COM3二进制文件，用于写属性
static FRESULT WriteCom3FileOpenFromSd(void)
{
    FRESULT fresult = f_open(&s_fileCOM3, Com3FileName, FA_WRITE | FA_CREATE_ALWAYS);
    if (fresult != FR_OK)
    {
        printf("Open Write Com3 file failed, cause: %s\n", show_error_string(fresult));
    } 
    else 
    {
        printf("Open Write Com3 file successfully, status=%d\n", fresult);
        f_lseek(&s_fileCOM3, 0);
    }

    return fresult;
}

//从SD卡打开BB00原始数据文件，用于写属性
static FRESULT WriteBB00FileOpenFromSd(void)
{
    FRESULT fresult = f_open(&s_fileBB00, BB00FileName, FA_WRITE |FA_CREATE_ALWAYS);
    if (fresult != FR_OK)
    {
        printf("Open Write BB00 file failed, cause: %s\n", show_error_string(fresult));
    } 
    else 
    {
        printf("Open Write BB00 file successfully, status=%d\n", fresult);
        f_lseek(&s_fileBB00, 0);
    }

    return fresult;
}

//从SD卡打开COM3二进制文件，用于读属性
static FRESULT ReadCom3FileOpenFromSd(void)
{
    FRESULT fresult = f_open(&s_fileCOM3, Com3FileName, FA_READ);
    if (fresult != FR_OK) 
    {
        printf("Open Read Com3 file failed, cause: %s\n", show_error_string(fresult));
    } 
    else 
    {
        printf("Open Read Com3 file successfully\n");
        f_lseek(&s_fileCOM3, 0);
    }

    return fresult;
}

//从SD卡打开BB00原始数据文件，用于读属性
static FRESULT ReadBB00FileOpenFromSd(void)
{
    FRESULT fresult = f_open(&s_fileBB00, BB00FileName, FA_READ);
    if (fresult != FR_OK) 
    {
        printf("Open Read BB00 file failed, cause: %s\n", show_error_string(fresult));
    } 
    else 
    {
        printf("Open Read BB00 file successfully\n");
        f_lseek(&s_fileBB00, 0);
    }

    return fresult;
}

//从SD卡打开数据文件，用于读属性
void ReadFileOpenFromSd(uint8_t FlieType)
{
    if(FlieType == 0x01)
    {
        ReadCom3FileOpenFromSd();
    }
    else if(FlieType == 0x02)
    {
        ReadBB00FileOpenFromSd();
    }
    else if(FlieType == 0x03)
    {
        ReadCom3FileOpenFromSd();
        ReadBB00FileOpenFromSd();
    }
}

//从SD卡打开数据文件，用于写属性
void WriteFileOpenFromSd(uint8_t FlieType)
{
    f_mount(NULL, driver_num_buf, 0);
    FRESULT fresult = f_mount(&s_sd_disk, driver_num_buf, 1);
    if (fresult == FR_OK) {
        printf("SD card has been mounted successfully\n");
    } else {
        printf("Failed to mount SD card, cause: %s\n", show_error_string(fresult));
    }
    fresult = f_chdrive(driver_num_buf);
    if(FlieType == 0x01)
    {
        WriteCom3FileOpenFromSd();
    }
    else if(FlieType == 0x02)
    {
        WriteBB00FileOpenFromSd();
    }
    else if(FlieType == 0x03)
    {
        WriteCom3FileOpenFromSd();
        WriteBB00FileOpenFromSd();
    }
}


//写入文件到SD卡
void WriteFileToSd(uint8_t FlieType)
{
    if(FlieType == 0x01)
    {
        if(g_Com3WriteSdFlag==1)
        {
            g_Com3WriteSdFlag=0;
            WriteCOM3FileToSd();
        }   
    }
    else if(FlieType == 0x02)
    {
        if(g_BB00WriteSdFlag==1)
        {
            g_BB00WriteSdFlag=0;
            WriteBB00FileToSd();
        } 
    }
    else if(FlieType == 0x03)
    {
        if(g_Com3WriteSdFlag==1)
        {
            g_Com3WriteSdFlag=0;
            WriteCOM3FileToSd();
        }
        if(g_BB00WriteSdFlag==1)
        {
            g_BB00WriteSdFlag=0;
            WriteBB00FileToSd();
        }
    }
}

//从SD卡读取文件
void ReadFileToSd(uint8_t FlieType)
{
    //FpgadataPreDoSend_t gBB00dataSend;	
    UINT byte_readen,byte_readen2;
    uint16_t dataLength = 0,dataLength2 = 0;
    static uint16_t COM3Flag=0,BB00Flag=0;
    static uint32_t Cnt=0;
    TCHAR buffer[512]={0};
    TCHAR hexData[256]={0};

    TCHAR buffer2[SDRX_BB00_MAXCOUNT*2]={0};
    TCHAR hexData2[SDRX_BB00_MAXCOUNT]={0};

    if(FlieType == 0x01)
    {
#ifdef SD_STR_DEF
        FRESULT fresult = f_read(&s_fileCOM3, buffer, sizeof(buffer), &byte_readen);
        
        ////将字符串转换为
        for (uint16_t i = 0; i < byte_readen; i++) 
        {
            if (buffer[i] == ' ') continue; // 忽略空格
            
            if (dataLength % 2 == 0) 
            {
                hexData[dataLength/2] |= ((buffer[i] >= 'A' ? (buffer[i] - 'A' + 10) : (buffer[i] - '0')) << 4)&0xf0;
            } 
            else 
            {
                hexData[dataLength/2] |= ((buffer[i] >= 'A' ? (buffer[i] - 'A' + 10) : (buffer[i] - '0')) <<0)&0x0f;
            }
            dataLength++;
        }
        if(byte_readen !=0)
        { 
            //printf("fresult = %d,byte_readen = %d,Cnt=%d \n",fresult,byte_readen,Cnt++);
            COM3Flag=1;
            uart4sendmsg((char*)hexData,byte_readen/2);

            //for(int i=0;i<byte_readen/2;i++)
            //  printf("0x%x\r",hexData[i]);
            //printf("1111111 \n");
        }
        else if((byte_readen ==0)&&(COM3Flag==1))
        {
            //printf("fresult = %d,byte_readen = %d,Cnt=%d \n",fresult,byte_readen,Cnt++);
            COM3Flag=0;
            memset(hexData,0x0F,64);
            uart4sendmsg((char*)hexData,64);

            //for(int i=0;i<64;i++)
            //  printf("0x%x\r",hexData[i]);

        }
#else  
        start_ticks = mchtmr_get_count(HPM_MCHTMR);
        FRESULT fresult = f_read(&s_fileCOM3, hexData, sizeof(hexData), &byte_readen);
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        if(read_ms >= 5.0f)
        {
            printf("-----read1 timeout:%f!\r\n", read_ms);
        }
        if(byte_readen !=0)
        { 
            COM3Flag=1;
            uart4sendmsg((char*)hexData,byte_readen);
        }
        else if((byte_readen ==0)&&(COM3Flag==1))
        {
            printf("read over:%lld,%lld\r\n", read_count, test_count);
            COM3Flag=0;
            memset(hexData,0x0F,64);
            uart4sendmsg((char*)hexData,64);
        }
#endif    
    }
    else if(FlieType == 0x02)
    {
#ifdef SD_STR_DEF
        start_ticks = mchtmr_get_count(HPM_MCHTMR);
        FRESULT fresult2 = f_read(&s_fileBB00, buffer2, sizeof(buffer2), &byte_readen2);
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        if(read_ms >= 5.0f)
        {
            printf("-----read2 timeout:%f!\r\n", read_ms);
        }


        ////将字符串转换为
        for (uint16_t i = 0; i < byte_readen2; i++) 
        {
            if (buffer2[i] == ' ') continue; // 忽略空格
            
            if (dataLength2 % 2 == 0) 
            {
                hexData2[dataLength2/2] |= ((buffer2[i] >= 'A' ? (buffer2[i] - 'A' + 10) : (buffer2[i] - '0')) << 4)&0xf0;
            } 
            else 
            {
                hexData2[dataLength2/2] |= ((buffer2[i] >= 'A' ? (buffer2[i] - 'A' + 10) : (buffer2[i] - '0')) <<0)&0x0f;
            }
            dataLength2++;
        }

        //memcpy(&gBB00dataSend, hexData2,byte_readen2/2);
        if(byte_readen2 !=0)	
        {
            BB00Flag=1;
            
            uart4sendmsg((char*)&hexData2[0],byte_readen2/2);
        } 
        else if((byte_readen2 ==0)&&(BB00Flag==1))
        {
            BB00Flag=0;
            
            memset(hexData2,0x0F,64);
            uart4sendmsg((char*)&hexData2[0],64);
        }   
#else

        start_ticks = mchtmr_get_count(HPM_MCHTMR);
#ifndef DEBUG_TEST
        FRESULT fresult2 = f_read(&s_fileBB00, hexData2, sizeof(hexData2), &byte_readen2);
#else
        FRESULT fresult2 = f_read(&s_fileBB00, hexData2, sizeof(test_str), &byte_readen2);
#endif
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        read_count++;
#ifdef DEBUG_TEST
        if(byte_readen2 > 0 && memcmp((void*)test_str, hexData2, sizeof(test_str)) != 0)
        {
            printf("bad! no match:%d\r\n", read_count);
        }
        //printf("r:%d\r\n", byte_readen2);
#endif
        if(read_ms >= 5.0f)
        {
            printf("-----read3 timeout:%f!\r\n", read_ms);
        }
        //memcpy(&gSdDataSave, hexData2,  sizeof(hexData2));
        if(byte_readen2 !=0)	
        {
            BB00Flag=1;
            //SDTo422_00BB_send(&gnavout);
            uart4sendmsg((char*)&hexData2[0],byte_readen2);
        } 
        else if((byte_readen2 ==0)&&(BB00Flag==1))
        {
            printf("read over:%d,%d\r\n", read_count, test_count);
            BB00Flag=0;
            memset(hexData2,0x0F,64);
            uart4sendmsg((char*)&hexData2[0],64);
        }
#endif
    }
    else if(FlieType == 0x03)
    {
#ifdef SD_STR_DEF
        start_ticks = mchtmr_get_count(HPM_MCHTMR);
        FRESULT fresult2 = f_read(&s_fileCOM3, buffer, sizeof(buffer), &byte_readen);
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        if(read_ms >= 5.0f)
        {
            printf("-----read4 timeout:%f!\r\n", read_ms);
        }

        ////将字符串转换为
        for (uint16_t i = 0; i < byte_readen; i++) 
        {
            if (buffer[i] == ' ') continue; // 忽略空格
            
            if (dataLength % 2 == 0) 
            {
                hexData[dataLength/2] |= ((buffer[i] >= 'A' ? (buffer[i] - 'A' + 10) : (buffer[i] - '0')) << 4)&0xf0;
            } 
            else 
            {
                hexData[dataLength/2] |= ((buffer[i] >= 'A' ? (buffer[i] - 'A' + 10) : (buffer[i] - '0')) <<0)&0x0f;
            }
            dataLength++;
        }
        if(byte_readen !=0)
        { 
            COM3Flag=1;
            uart4sendmsg((char*)hexData,byte_readen/2);
        }
        else if((byte_readen ==0)&&(COM3Flag==1))
        {
            COM3Flag=0;
            memset(hexData,0x0F,64);
            uart4sendmsg((char*)hexData,64);
        }
            
        start_ticks = mchtmr_get_count(HPM_MCHTMR);
        FRESULT fresult2 = f_read(&s_fileBB00, buffer2, sizeof(buffer2), &byte_readen2);
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        if(read_ms >= 5.0f)
        {
            printf("-----read5 timeout:%f!\r\n", read_ms);
        }

        
        ////将字符串转换为
        for (uint16_t i = 0; i < byte_readen2; i++) 
        {
            if (buffer2[i] == ' ') continue; // 忽略空格
            
            if (dataLength2 % 2 == 0) 
            {
                hexData2[dataLength2/2] |= ((buffer2[i] >= 'A' ? (buffer2[i] - 'A' + 10) : (buffer2[i] - '0')) << 4)&0xf0;
            } 
            else 
            {
                hexData2[dataLength2/2] |= ((buffer2[i] >= 'A' ? (buffer2[i] - 'A' + 10) : (buffer2[i] - '0')) <<0)&0x0f;
            }
            dataLength2++;
        }
        if(byte_readen2 !=0)	
        {
            BB00Flag=1;
            uart4sendmsg((char*)&hexData2[0],byte_readen2/2);
        } 
        else if((byte_readen2 ==0)&&(BB00Flag==1))
        {
            BB00Flag=0;
            memset(hexData2,0x0F,64);
            uart4sendmsg((char*)&hexData2[0],64);
        }
#else
        start_ticks = mchtmr_get_count(HPM_MCHTMR);
        FRESULT fresult = f_read(&s_fileCOM3, hexData, sizeof(hexData), &byte_readen);
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        if(read_ms >= 5.0f)
        {
            printf("-----read6 timeout:%f!\r\n", read_ms);
        }
        
        uart4sendmsg((char*)&hexData[0],byte_readen); 

        start_ticks = mchtmr_get_count(HPM_MCHTMR);
        FRESULT fresult2 = f_read(&s_fileBB00, hexData2, sizeof(hexData2), &byte_readen2);
        end_ticks = mchtmr_get_count(HPM_MCHTMR);
        read_ms = (1000.0f)*(end_ticks -start_ticks)/clock_get_frequency(clock_mchtmr0);
        if(read_ms >= 5.0f)
        {
            printf("-----read7 timeout:%f!\r\n", read_ms);
        }
        
        uart4sendmsg((char*)&hexData2[0],byte_readen2);
#endif
    }
}

//从SD卡读写完毕后，关闭文件
void CloseFileToSd(uint8_t FlieType)
{
    if(FlieType == 0x01)
    {
      f_close(&s_fileCOM3);
      printf("Close COM3 file successfully\n");
    }
    else if(FlieType == 0x02)
    {
      f_close(&s_fileBB00);
      printf("Close BB00 file successfully\n");
    }
    else if(FlieType == 0x03)
    {
      f_close(&s_fileCOM3);
      f_close(&s_fileBB00);
      printf("Close COM3 AND BB00 file successfully\n");
    }  
}

//从SD卡删除指定文件
void DeleteFileFromSd(uint8_t FlieType)
{
    if(FlieType == 0x01)
    {
      f_unlink(Com3FileName);
      printf("Delete COM3 file successfully\n");
    }
    else if(FlieType == 0x02)
    {
      f_unlink(BB00FileName);
      printf("Delete BB00 file successfully\n");
    }
    else if(FlieType == 0x03)
    {
      f_unlink(Com3FileName);
      f_unlink(BB00FileName);
      printf("Delete COM3 AND BB00 file successfully\n");
    }
    
}

//格式化sd卡
void FormatSD(void)
{
    sd_mkfs();
}

//SD卡文件系统操作类型处理
void SdFileOperateTypeSet(uint8_t OperateType,uint8_t FlieType)
{
    if(OperateType == 0x01)
    {
        CloseFileToSd(FlieType);
        WriteFileOpenFromSd(FlieType);//打开写文件
    }
    else if(OperateType == 0x02)
    {
        CloseFileToSd(FlieType);//关闭写文件
    }
    else if(OperateType == 0x03)
    {
        CloseFileToSd(FlieType);//关闭写文件
        ReadFileOpenFromSd(FlieType);//打开读文件
    }
    else if(OperateType == 0x04)
    {
        CloseFileToSd(FlieType);//关闭读文件
    }
    else if(OperateType == 0x05)//清除数据
    {
        CloseFileToSd(FlieType);
        //FormatSD();//格式化SD卡
        DeleteFileFromSd(FlieType);//删除文件
    }

}

//SD卡文件系统写操作
void SdFileWriteOperate(void)
{
    //if(((g_Com3WriteSdFlag==1)||(g_BB00WriteSdFlag==1))&&(SetSdOperateType==0x01))
    //if((g_BB00WriteSdFlag==1)&&(SetSdOperateType==0x01))
    if(SetSdOperateType==0x01)
    {
        //g_Com3WriteSdFlag=0;  
        //g_BB00WriteSdFlag=0;

        WriteFileToSd(SetSdFlieType);
    } 
}

//SD卡文件系统读操作
void SdFileReadOperate(void)
{
    //输出频率设置
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    if(tCnt >= freq)
    {
        tCnt = 0;
        if(SetSdOperateType==0x03)
        {
            ReadFileToSd(SetSdFlieType);
        }
    }
}



//SD卡文件测试程序
void SdFileTest(void)
{
    WriteBB00FileOpenFromSd();

    WriteBB00FileToSd();

    f_close(&s_fileBB00);
}