#ifndef __FRAME_ANALYSIS_H__
#define __FRAME_ANALYSIS_H__

#include "protocol.h"
#include "INS_Data.h"
#include "insdef.h"

//#include "algorithm.h"
#include "tlhtype.h"

#undef COMMON_EXT
#ifdef  __GOL_FRAME_ANALYSIS_C__
    #define COMMON_EXT
#else
    #define COMMON_EXT extern
#endif

//#define	RS422_PROTOCOL_OLD

#define RS422_FRAME_HEADER_L                  0xBD
#define RS422_FRAME_HEADER_M                  0xDB
#define RS422_FRAME_HEADER_H                  0x1B//0x0B

#ifdef	RS422_PROTOCOL_OLD
#define	RS422_FRAME_LENGTH	63
#else
#define	RS422_FRAME_LENGTH	(100)	//65
#endif

typedef struct imu_data
{
    uint8_t syn_low;
    uint8_t syn_high;
    uint8_t accelX_l;
    uint8_t accelX_h;
    uint8_t accelY_l;
    uint8_t accelY_h;
    uint8_t accelZ_l;
    uint8_t accelZ_h;
    uint8_t gyroX_l;
    uint8_t gyroX_h;
    uint8_t gyroY_l;
    uint8_t gyroY_h;
    uint8_t gyroZ_l;
    uint8_t gyroZ_h;
    uint8_t roll_l;
    uint8_t roll_h;
    uint8_t pitch_l;
    uint8_t pitch_h;
    uint8_t	azimuth_l;
    uint8_t	azimuth_h;
    uint8_t	sensor_temp_l;
    uint8_t	sensor_temp_h;
    uint8_t crc;
} IMU_DATA_TypeDef;

//typedef enum poll_data_type
//{
//    locating_info_prec = 0,
//    speed_info_prec = 1,
//    pos_info_prec = 2,
//    dev_inter_temp = 22,
//    gps_status     = 32,
//    rotate_status = 33,
//    gnss_baseline = 34,
//    gnss_rtkStatus = 35,
//    para_adj = 36,
//    calib_rate = 37
//} POLL_DATA_TypeDef;

#pragma pack(1)

typedef  struct poll_data_F
{
    uint16_t	data1;
    uint16_t 	data2;
    uint16_t	data3;
} POLL_DATA_Fx, *pPOLL_DATA_F;

typedef union
{
    struct
    {
        uint8_t pos: 1;
        uint8_t spd: 1;
        uint8_t posture: 1;
        uint8_t courseAngle: 1;
        uint8_t hold: 4;
    } statusBits;
    uint8_t dev_status;
} DEV_StatusTypedef;

typedef  struct {
    uint8_t 			header[3];	//0xbd,0xdb,0x0b
    short 				roll;		//横滚角
    short 				pitch;		//俯仰角
    short				azimuth;	//方位角
    long 				gyroX;		//陀螺x轴
    long 				gyroY;		//陀螺y轴
#ifdef	RS422_PROTOCOL_OLD
	short				gyroZ;		//陀螺z轴
#else
	long				gyroZ;		//陀螺z轴
#endif    
    long 				accelX;		//加表x轴
    long 				accelY;		//加表y轴
    long				accelZ;		//加表z轴
    long				latitude;	//纬度
    long				longitude;	//经度
    long				altitude;	//高度
    short				ve;			//东向速度
    short				vn;			//北向速度
    short				vu;			//天向速度
    uint8_t				status;		//bit0:位置 bit1:速度 bit2:姿态 bit3:航向角
    //uint32_t 			Nav_Status;
    uint8_t				reserved[6];
    POLL_DATA_Fx		poll_frame;
    uint32_t			gps_time;
    uint8_t				type;
    uint8_t				xor_verify1;
    uint32_t			gps_week;
    uint8_t				xor_verify2;
} DATA_STREAM;


typedef union rs422_frame_define
{
    DATA_STREAM data_stream;
    uint8_t fpga_cache[RS422_FRAME_LENGTH];
} RS422_FRAME_DEFx, *pRS422_FRAME_DEF;


typedef  struct
{
    uint16_t 	counter;
    double		imuTimestamp;
    float		timestamp;
    float 		accelX;
    float 		accelY;
    float 		accelZ;
    float 		gyroX;
    float 		gyroY;
    float 		gyroZ;
    float		sensor_temp;
    uint8_t		gps_valid;
    uint16_t 	gpsWeek;
    uint32_t 	gpsSec;
    uint8_t		starNum;
    uint8_t		rtkStatus;
    float		lon;
    float		lat;
    float		alt;
    float		vn;		//bill
    float		ve;		//bill
    float		vu;
    float		heading;
	float		roll;		//bill
    float 		pitch;
	float		azimuth;	//bill
	
    uint8_t		ins_gnssflag_pos;
    uint8_t		ins_numsv;
    uint8_t		ins_gnssflag_heading;
    uint8_t		ins_self_check;
	
    uint8_t		ins_car_status;
    uint16_t	ins_gnss_week;
    uint8_t		ins_status;
	
	
    float		lon_std;
    float		lat_std;
    float		alt_std;
    float		hdgstddev;
    float		ptchstddev;
    float		hdop;
    float		trackTrue;
    volatile 	uint8_t pps_en;
    volatile 	uint8_t pps_cnt;
    uint16_t 	s_ppsDelay;
    //IFOG_PARSE_DATA_TypeDef iFogData;
    //CanDataTypeDef			canInfo;
} navi_test_t;

typedef  struct
{
    uint8_t 			header[3];			//0xbd,0xdb,0x0b
    uint8_t 			status;				//状态字
    uint8_t 			expiredStatus;		//主机过期状态
    uint8_t				calibStatus;		//标定状态
    uint8_t 			gnssStatus;			//gnss状态
    uint8_t 			imuStatus;			//imu状态
    uint8_t				diffStatus;			//差分状态
    uint8_t 			odoStatus;			//odo状态
    uint8_t				xor;				//3~9字节异或和
    uint8_t				end;				//结束符

} MODULE_STA_Typedef;


#pragma pack()

typedef enum poll_data_type
{
    locating_info_prec = 0,
    speed_info_prec = 1,
    pos_info_prec = 2,
    dev_inter_temp = 22,
    gps_status     = 32,
    rotate_status = 33,
    gnss_baseline = 34,
    gnss_rtkStatus = 35,
    para_adj = 36,
    calib_rate = 37
} POLL_DATA_TypeDef;

//COMMON_EXT IMU_PARSE_DATA_TypeDef imuParseData;
//COMMON_EXT IFOG_PARSE_DATA_TypeDef iFogParseData;
COMMON_EXT IMU_DATA_TypeDef imu_info;
COMMON_EXT MODULE_STA_Typedef moduleStatus;

extern	RS422_FRAME_DEFx	grs422_frame;
extern	navi_test_t			grs422_frameD;
extern	GPSDataTypeDef		ggpsorgdata;


//写数据到DRAM
void frame_writeDram(void);
void frame_pack_and_send(void* pnav, void *gps);
void frame_init(void);
uint8_t frame_fill_imu(uint8_t* pData, uint16_t dataLen);
uint8_t frame_fill_ifog(uint8_t* pData, uint16_t dataLen);
void frame_form(void);
void frame_iPMV_pack_and_send(void* pnav, void *gps);
uint8_t xor_check(uint8_t *buf, uint16_t len);



#endif

