/*!
    \file  systick.c
    \brief the systick configuration file
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#include "systick.h"
#include "board.h"


void delay_1ms(uint32_t count)
{
	board_delay_ms(count);
}


void delay_init(uint8_t SYSCLK)
{
 
}

//nus
//nus:us.	
//nus:0~204522252(2^32/fac_us@fac_us=168)
void delay_us(uint32_t nus)
{
	board_delay_us(nus);
}

//nms
//nms:ms
//nms:0~65535
void delay_ms(uint32_t nms)
{
	board_delay_ms(nms);
}

//nms,
//nms:ms
void delay_xms(uint32_t nms)
{
	uint32_t i;
	for(i=0;i<nms;i++) 
		board_delay_ms(1);
}
