# Keil项目配置更新详细指南

## 🎯 目标
更新Keil项目配置，移除不存在的文件引用，添加INAV算法文件。

## 📋 准备工作

### 1. 解决许可证问题（如果需要）
- 确保Keil MDK有有效许可证
- 或使用Keil MDK Community Edition（免费版）

### 2. 备份项目文件
```
复制 INS370-8K24-INS600M-21A/Project/INS.uvprojx 
到   INS370-8K24-INS600M-21A/Project/INS.uvprojx.backup
```

## 🔧 详细操作步骤

### 步骤1：打开Keil项目

1. **启动Keil MDK**
   - 双击桌面上的Keil图标
   - 或从开始菜单启动

2. **打开项目**
   - 菜单：`File -> Open Project...`
   - 浏览到：`INS370-8K24-INS600M-21A/Project/`
   - 选择：`INS.uvprojx`
   - 点击：`打开`

3. **查看项目结构**
   - 在左侧Project窗口中展开项目树
   - 应该看到：INS_4000项目和各个组

### 步骤2：移除不存在的文件引用

#### 在NAV组中移除文件：

**操作方法**：
1. 展开 `NAV` 组
2. 找到以下文件（可能显示为红色，表示文件不存在）
3. 右键点击文件名
4. 选择 `Remove File from Group`

**需要移除的文件**：
```
❌ nav.c
❌ nav_app.c
❌ nav_kf.c
❌ nav_math.c
❌ nav_ods.c
❌ nav_sins.c
❌ navlog.c
❌ nav_gnss.c
❌ nav_imu.c
❌ nav_magnet.c
❌ nav_mahony.c
❌ nav_uwb.c
```

**保留的文件**：
```
✅ nav_cli.c  (保留)
```

#### 在Protocol组中移除文件：

**需要移除的文件**：
```
❌ transplant.c
```

**保留的文件**：
```
✅ computerFrameParse.c
✅ frame_analysis.c
✅ protocol.c
✅ SetParaBao.c
✅ InsTestingEntry.c
```

### 步骤3：创建INAV组

1. **创建新组**：
   - 右键点击项目根节点 `INS_4000`
   - 选择 `Add Group...`
   - 输入组名：`INAV`
   - 点击 `OK`

2. **验证组创建**：
   - 在项目树中应该看到新的 `INAV` 组

### 步骤4：添加INAV算法文件

1. **添加文件到INAV组**：
   - 右键点击 `INAV` 组
   - 选择 `Add Existing Files to Group 'INAV'...`

2. **浏览到INAV目录**：
   - 在文件浏览器中导航到：`../INAV/`
   - 或完整路径：`INS370-8K24-INS600M-21A/INAV/`

3. **选择所有.c文件**：
   - 按住Ctrl键，逐个点击选择以下文件：

```
✅ AnnTempCompen.c           - 温度补偿
✅ adxl355.c                 - ADXL355传感器
✅ ahrs.c                    - 姿态算法
✅ align.c                   - 对准算法
✅ compen.c                  - 补偿算法
✅ dynamic_align.c           - 动态对准
✅ fuseBodyOdomVel.c         - 里程计融合
✅ fuseGnssVelPosHeight.c    - GNSS融合
✅ fuseTwoAntHeading.c       - 双天线融合
✅ ins.c                     - INS接口
✅ interrupt.c               - 中断处理
✅ kalman.c                  - Kalman滤波
✅ matvecmath.c              - 数学运算
✅ navi.c                    - 导航核心
✅ private_math.c            - 私有数学
✅ read_and_check_gnss_data.c - GNSS数据处理
✅ readpaoche.c              - 跑车数据1
✅ readpaoche2.c             - 跑车数据2
✅ readpaoche3.c             - 跑车数据3
✅ sins.c                    - 捷联惯导1
✅ sins2.c                   - 捷联惯导2
```

4. **添加文件**：
   - 点击 `Add` 按钮
   - 文件将被添加到INAV组中

### 步骤5：配置包含路径

1. **打开项目选项**：
   - 右键点击项目根节点 `INS_4000`
   - 选择 `Options for Target 'INS_4000'...`

2. **配置C/C++选项**：
   - 切换到 `C/C++` 标签页
   - 找到 `Include Paths` 部分

3. **添加INAV路径**：
   - 点击 `Include Paths` 右侧的 `...` 按钮
   - 点击 `New (Insert)` 按钮
   - 输入：`../INAV`
   - 点击 `OK`

4. **确认配置**：
   - 确保 `../INAV` 出现在包含路径列表中
   - 点击 `OK` 保存设置

### 步骤6：验证配置

1. **检查项目结构**：
   ```
   INS_4000
   ├── INAV (新组)
   │   ├── navi.c
   │   ├── kalman.c
   │   ├── align.c
   │   └── ... (共21个文件)
   ├── NAV
   │   └── nav_cli.c (只保留这一个)
   ├── Protocol
   │   ├── computerFrameParse.c
   │   ├── frame_analysis.c
   │   ├── protocol.c
   │   ├── SetParaBao.c
   │   └── InsTestingEntry.c
   ├── Source
   │   └── ... (所有原有文件)
   ├── bsp
   │   └── ... (所有驱动文件)
   └── Library
       └── ... (所有库文件)
   ```

2. **检查文件状态**：
   - 所有文件应该显示为黑色（正常）
   - 不应该有红色文件（表示文件不存在）

3. **检查包含路径**：
   - 在项目选项的C/C++标签页中
   - 确认 `../INAV` 在包含路径列表中

## 🧪 测试编译

### 1. 清理项目
- 菜单：`Project -> Clean Targets`
- 等待清理完成

### 2. 重新编译
- 菜单：`Project -> Rebuild all target files`
- 观察编译输出

### 3. 预期编译输出
应该看到类似以下的编译信息：
```
compiling navi.c...
compiling kalman.c...
compiling align.c...
compiling ins.c...
compiling ahrs.c...
... (其他INAV文件)
```

### 4. 不应该看到的错误
```
❌ Error: cannot open source input file "nav.c"
❌ Error: cannot open source input file "transplant.c"
```

## 🔧 故障排除

### 问题1：文件路径错误
**症状**：编译时提示找不到文件
**解决**：
- 检查文件路径是否正确
- 确认INAV目录相对于项目文件的位置

### 问题2：包含路径错误
**症状**：编译时提示找不到头文件
**解决**：
- 检查 `../INAV` 是否在包含路径中
- 确认路径格式正确

### 问题3：许可证问题
**症状**：编译时提示许可证错误
**解决**：
- 激活Keil MDK许可证
- 或使用免费的Community Edition

### 问题4：配置丢失
**症状**：重新打开项目后配置丢失
**解决**：
- 确保保存了项目文件
- 检查项目文件是否有写权限

## ✅ 成功标志

配置成功后，您应该看到：

1. **编译成功**：
   - 0 Error(s), 0 Warning(s)
   - 生成.hex和.bin文件

2. **INAV文件被编译**：
   - 编译输出中包含所有INAV文件
   - 没有"文件找不到"错误

3. **项目结构正确**：
   - INAV组包含25个算法文件
   - 没有红色的不存在文件

完成这些步骤后，您的项目就包含了完整的HPM6750_INS-370M-SD-OK算法实现，可以正常编译和运行了！
