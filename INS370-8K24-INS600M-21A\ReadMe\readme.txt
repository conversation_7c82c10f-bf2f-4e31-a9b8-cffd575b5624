20250606：
1、基于INS600M-21A代码，将其移植到之前的8K24设备上。调试通过。
2、更改地方如下：
	//gmq20250605增加***************************************
	//imu
	combineData.scha634Info.acc_x  = (get_16bit_D32((unsigned short *)&gpagedata.axis3_accx0) / G0);
	combineData.scha634Info.acc_y  = (get_16bit_D32((unsigned short *)&gpagedata.axis3_accz0) / G0);
	combineData.scha634Info.acc_z  = (get_16bit_D32((unsigned short *)&gpagedata.axis3_accy0) / G0);
	combineData.scha634Info.gyro_x = ((int)get_16bit_Int32((unsigned short *)&gpagedata.fog_x0) / FOG_GYRO_FACTORX * ( 1.0));
	combineData.scha634Info.gyro_y = ((int)get_16bit_Int32((unsigned short *)&gpagedata.fog_z0) / FOG_GYRO_FACTORY * (-1.0));
	combineData.scha634Info.gyro_z = ((int)get_16bit_Int32((unsigned short *)&gpagedata.fog_y0) / FOG_GYRO_FACTORZ * (-1.0));
	combineData.scha634Info.temp_due = (gpagedata.fog_tempx / 16.0);
	combineData.scha634Info.temp_uno = (gpagedata.fog_tempz / 16.0);