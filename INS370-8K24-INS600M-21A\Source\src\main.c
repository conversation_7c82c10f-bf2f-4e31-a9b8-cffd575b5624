//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：main.c
// 文件摘要：主程序入口，从HPM6750平台移植到GD32F4xx平台
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.15
// 移植说明：适配GD32F4xx平台
//---------------------------------------------------------

#include "main.h"
#include "uart.h"
#include "timer.h"
#include "flash.h"
#include "sdram.h"
#include "gd32f4xx_it.h"
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"
#include "bsp_gpio.h"
#include "deviceconfig.h"
#include "platform_adapter.h"

uint8_t uart4_sendImuFlag = 0;
uint8_t Original_Data = 0; // 0:正常版本，2:原始数据输出

static volatile uint64_t gpio_isr_rel_time; /* mark the real time from mchtimer */
static volatile uint64_t gpio_isr_pre_time; /* mark the last time marked on gpio_isr */
static volatile uint32_t level_value;       /* mark the level */
static volatile uint32_t pre_level_value;   /* mark the level before enter the isr */
static uint32_t debounce_threshold;         /* debounce threshold */

extern uint8_t fpga_syn;
extern uint32_t fpga_syn_count;
extern uint32_t fpga_loop_count;
uint8_t g_StartUpdateFirm = 0; // 开始升级标志 1:开始升级 0:结束升级
extern void ff_handle_poll(void);

void isr_gpio(void)
{
    /* GD32F4xx GPIO interrupt handler */
    if(RESET != exti_interrupt_flag_get(EXTI_9))
    {
        EXTI3_IRQHandler(); // 检测FPGA的外部中断
        exti_interrupt_flag_clear(EXTI_9);
    }
}

/* GD32F4xx interrupt service routine */
void EXTI5_9_IRQHandler(void)
{
    isr_gpio();
}

void init_gpio(void)
{
    /* Enable GPIO clock */
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_GPIOC);
    rcu_periph_clock_enable(RCU_SYSCFG);

    /* Configure PB9 as input with pull-up for FPGA interrupt */
    gpio_mode_set(GPIOB, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, GPIO_PIN_9);

    /* Configure PC13 as output for LED */
    gpio_mode_set(GPIOC, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_13);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_13);
}

void test_gpio_input_interrupt(void)
{
    /* Configure EXTI line 9 for PB9 */
    syscfg_exti_line_config(EXTI_SOURCE_GPIOB, EXTI_SOURCE_PIN9);

    /* Configure EXTI line 9 */
    exti_init(EXTI_9, EXTI_INTERRUPT, EXTI_TRIG_RISING);
    exti_interrupt_enable(EXTI_9);

    /* Enable NVIC interrupt */
    nvic_irq_enable(EXTI5_9_IRQn, 1, 0);
}

void Exti_Init(void)
{
    init_gpio();
    printf("gpio example\n");

    test_gpio_input_interrupt();
}

void Led_Control(void)
{
    gpio_bit_toggle(GPIOC, GPIO_PIN_13);
}


uint32_t get_boot_reason(void)
{
    uint32_t reset_flag = RCU_RSTSCK;
    printf("boot reason:0x%08x!\r\n", reset_flag);

    if ((reset_flag & RCU_RSTSCK_BORRSTF) != 0U)
        printf("boot reason:brownout reset\r\n");
    if ((reset_flag & RCU_RSTSCK_EPRSTF) != 0U)
        printf("boot reason:external pin reset\r\n");
    if ((reset_flag & RCU_RSTSCK_PORRSTF) != 0U)
        printf("boot reason:power on reset\r\n");
    if ((reset_flag & RCU_RSTSCK_SWRSTF) != 0U)
        printf("boot reason:software reset\r\n");
    if ((reset_flag & RCU_RSTSCK_FWDGTRSTF) != 0U)
        printf("boot reason:free watchdog reset\r\n");
    if ((reset_flag & RCU_RSTSCK_WWDGTRSTF) != 0U)
        printf("boot reason:window watchdog reset\r\n");
    if ((reset_flag & RCU_RSTSCK_LPRSTF) != 0U)
        printf("boot reason:low power reset\r\n");

    /* Clear reset flags */
    rcu_all_reset_flag_clear();

    return reset_flag;
}

int main(void)
{
    uint32_t uiLedCnt = 0;

    /* Initialize system clock and peripherals */
    systick_config();

    /* Initialize GPIO */
    init_gpio();

    /* Initialize external interrupt */
    Exti_Init();

    get_boot_reason();

    SysInit(); // Init inertial navigation device...

    ReadParaFromFlash();

    DeviceInit(); // 系统初始化完成后，进入循环前的准备
    delay_ms(1000);

    char txbuf[50] = {0};
    sprintf(txbuf, "\r\n Into INS 370 Init on GD32F4xx!\n");
    uart4sendmsg(txbuf, strlen(txbuf));

    while (1)
    {
        if (fpga_syn == 1) // 每一帧FPGA数据产生，处理
        {
            fpga_syn = 0;
            SdFileReadOperate();     // 读取SD卡数据
            get_fpgadata();          // 1、获取当前帧FPGA数据，及相关
            AlgorithmDo();           // 2、对获取的数据进行算法处理
            INS912_Output(&gnavout); // 4、算法处理完成的数据，进行打包、发送处理
            uiLedCnt++;
            if (uiLedCnt >= 20)
            {
                uiLedCnt = 0;
                Led_Control();
            }
        }
#ifdef DEVICE_ACC_TYPE_ADLX355
        l355_uart_recv_polling();
#endif
        loopDoOther(); // 循环中，处理其它事宜
        sduart_recv_polling();
        analysisRxdata(); // 用于参数设置和升级
        SdFileWriteOperate(); // 写数据到SD卡
        ff_handle_poll();
    }

    return 0;
}
