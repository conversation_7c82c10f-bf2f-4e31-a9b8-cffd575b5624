#include "gdtypedefine.h"
#include "fpgad.h"
#include "string.h"
#include "frame_analysis.h"

int gdriverspacket = 0;
int ggdworgdata_packet = 0;
gdwrxdata912TX_t	gtmpgdrx;
driversdatatosimulatorlist_t	gdriverdatalist;
driversettingstopc_t	gdriversettings;
gdalgrithomresult_t		galgrithomresultTx;
int gbilldebuguart4 = 0;	//0- normal		1- bill debug	for debug can

int initializationdriversettings(void)
{
	memset(&gdriversettings, 0, sizeof(gdriversettings));
	memset(&ggpsorgdata, 0, sizeof(ggpsorgdata));
	
	unsigned char *ptmpu8;
	unsigned short checksum;
	ptmpu8 = (unsigned char *)&gdriversettings;
	checksum = 0;
	strcpy(gdriversettings.settings.projectname, "INS622-2A");
	//strcpy(gdriversettings.settings.mcutype, "GD + FPGA");
	gdriversettings.settings.mcutype = 1;
	gdriversettings.settings.datatype = 1;
	gdriversettings.settings.imuframe = 0;
	gdriversettings.settings.gnssframe = 0;
	gdriversettings.settings.canframe = 0;
	gdriversettings.settings.ppsframe = 0;
	gdriversettings.settings.gdwframe = 0;	
	gdriversettings.settings.magnetframe = 0;
	gdriversettings.settings.pressureframe = 0;
	gdriversettings.settings.opticalgryoframe = 0;
	gdriversettings.settings.gdwframe912 = 200 * 60 * 225;	//frame rate * 60 second * 120 minute  

	
	gdriversettings.head = 0x55aa;
	gdriversettings.len = sizeof(gdriversettings);
	gdriversettings.type = 7;
	for (int i = 0; i < sizeof(gdriversettings) - 2; i++) {
	checksum += *ptmpu8++;
	}
	gdriversettings.checksum = checksum;       
	return 0;
}

int mcusendtopcdriversdata(int cmd, int listindex, int driverindex)
{
    int i, bfind;
    if (cmd == 0) {
        if (gdriverdatalist.size > 0) {
            driversdatatosimulator_t    tmpdata;
            tmpdata = gdriverdatalist.driversdata[gdriverdatalist.st];
            switch (tmpdata.driversdatatype) {
            case    DRIVERSDATATYPE_GNSS:
                //printf_output_2(0, &tmpdata.data.gnss, sizeof(tmpdata.data.gnss));
                break;
            case    DRIVERSDATATYPE_IMU:
                //printf_output_2(0, &tmpdata.data.imu, sizeof(tmpdata.data.imu));
                break;
            case    DRIVERSDATATYPE_CAN:
                //printf_output_2(0, &tmpdata.data.can, sizeof(tmpdata.data.can));
                break;
            case    DRIVERSDATATYPE_PPS:
                //printf_output_2(0, &tmpdata.data.pps, sizeof(tmpdata.data.pps));
                break;
            case    DRIVERSDATATYPE_GDW:
                //printf_output_2(0, &tmpdata.data.pps, sizeof(tmpdata.data.pps));
				uart4sendmsg((char*)&tmpdata.data.gdw, sizeof(tmpdata.data.gdw));
                break;
            default:
                break;
            }
            gdriverdatalist.st = (gdriverdatalist.st + 1) % GD_DRIVERSDATA_MAXCOUNT;
            gdriverdatalist.size--;
            return 1;
        }   
    }
    //else if (cmd == c_msg_ask_scha63xcacv) {
    //    printf_output_2(0, &gimucacvdata, sizeof(gimucacvdata));
    //    return  1;
    //}
	else if (cmd == c_msg_ask_driversettings) {
        //printf_output_2(0, &gimucacvdata, sizeof(gimucacvdata));
		uart4sendmsg((char*)&gdriversettings, sizeof(gdriversettings));
        return  1;
    }
    else if (cmd == c_msg_ask_retransmission) {
        driversdatatosimulator_t    tmpdata;
        bfind = 0;
        bfind = 0;
        bfind = 0;
        bfind = 0;
        bfind = 0;
        for (i = 1; i < GD_DRIVERSDATA_MAXCOUNT - 1; i++) {
            tmpdata = gdriverdatalist.driversdata[(gdriverdatalist.st - i + GD_DRIVERSDATA_MAXCOUNT) % GD_DRIVERSDATA_MAXCOUNT];
            if (tmpdata.driverspacket == listindex) {
            //if (tmpdata.data.gdw.packet == listindex) {
                bfind = 1;
                break;
            }
        }
        //if (listindex < 0 || listindex >= GD_DRIVERSDATA_MAXCOUNT) return 0;
        //tmpdata = gdriverdatalist.driversdata[listindex];
        if (bfind) {
            switch (tmpdata.driversdatatype) {
            case    DRIVERSDATATYPE_GNSS:
                //if (tmpdata.data.gnss.packetT == listindex && tmpdata.data.gnss.packet == driverindex) {
                    //printf_output_2(0, &tmpdata.data.gnss, sizeof(tmpdata.data.gnss));
                    return 1;
                //}
                //break;
            case    DRIVERSDATATYPE_IMU:
                //if (tmpdata.data.gnss.packetT == listindex && tmpdata.data.gnss.packet == driverindex) {
                    //printf_output_2(0, &tmpdata.data.imu, sizeof(tmpdata.data.imu));
                    return 1;
                //}
                //break;
            case    DRIVERSDATATYPE_CAN:
                //if (tmpdata.data.gnss.packetT == listindex && tmpdata.data.gnss.packet == driverindex) {
                    //printf_output_2(0, &tmpdata.data.can, sizeof(tmpdata.data.can));
                    return  1;
                //}
                //break;
            case    DRIVERSDATATYPE_PPS:
                //if (tmpdata.data.gnss.packetT == listindex && tmpdata.data.gnss.packet == driverindex) {
                    //printf_output_2(0, &tmpdata.data.pps, sizeof(tmpdata.data.pps));
                    return  1;
                //}
                //break;
            case    DRIVERSDATATYPE_GDW:
                //if (tmpdata.data.gdw.packetT == listindex && tmpdata.data.gdw.packet == driverindex) {
                    //printf_output_2(0, &tmpdata.data.pps, sizeof(tmpdata.data.pps));
					uart4sendmsg((char*)&tmpdata.data.gdw, sizeof(tmpdata.data.gdw));
                    return  1;
                //}
                //break;
            default:
                break;
            }
        }
    }
    return 0;
}
