/*!
    \file  navlog.h
    \brief Navigation logging system header file
*/

#ifndef __NAVLOG_H
#define __NAVLOG_H

typedef enum {
    NAV_LOG_LEVEL_DEBUG = 0,
    NAV_LOG_LEVEL_INFO,
    NAV_LOG_LEVEL_WARN,
    NAV_LOG_LEVEL_ERROR
} nav_log_level_t;

typedef enum {
    NAV_LOG_STATUS_OK = 0,
    NAV_LOG_STATUS_ERROR
} nav_log_status_t;

nav_log_status_t nav_log_init(nav_log_level_t level);
nav_log_status_t nav_log_write(nav_log_level_t level, const char *format, ...);

#endif /* __NAVLOG_H */
