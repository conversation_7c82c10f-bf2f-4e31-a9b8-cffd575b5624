/***********************************************************************************
航向角掉电保存修复验证测试程序
All rights reserved I-NAV 2023 2033
***********************************************************************************/

#include "test_azimuth_fix.h"
#include "SetParaBao.h"
#include "nav_includes.h"
#include <stdio.h>
#include <math.h>

// 测试用的外部变量声明
extern _NAV_Data_Full_t NAV_Data_Full;

// 测试航向角掉电保存修复功能
void test_azimuth_save_fix(void)
{
    printf("=== 航向角掉电保存修复功能测试 ===\n\n");

    // 测试1: 验证修复前的问题
    printf("测试1: 验证修复前的问题场景\n");

    // 初始化航向角保存系统
    InitAzimuthSaveSystem();

    // 模拟保存一个航向角值 (90度 = π/2 弧度)
    double test_azimuth = M_PI / 2.0;  // 90度
    printf("保存测试航向角: %.6f 弧度 (%.2f 度)\n", test_azimuth, test_azimuth * 180.0 / M_PI);

    // 保存航向角到Flash
    SaveAzimuthToFlash(test_azimuth);

    // 验证保存是否成功
    int is_valid = IsAzimuthSaveValid();
    printf("保存有效性检查: %s\n", is_valid ? "有效" : "无效");

    if (!is_valid) {
        printf("错误: 航向角保存失败!\n");
        return;
    }

    // 恢复航向角到Pre_att
    double restored_azimuth = RestoreAzimuthFromFlash();
    printf("从Flash恢复的航向角: %.6f 弧度 (%.2f 度)\n", restored_azimuth, restored_azimuth * 180.0 / M_PI);

    // 模拟系统重启后的状态
    memset(&NAV_Data_Full, 0, sizeof(_NAV_Data_Full_t));
    NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;
    NAV_Data_Full.ins_buffer_full_cnt = 0;
    NAV_Data_Full.UseCase = E_USE_CASE_In_Vehicle;
    NAV_Data_Full.Nav_Standard_flag = E_NAV_STANDARD_NO_PROCCSS;

    // 设置基本的IMU数据
    NAV_Data_Full.Macc[0] = 0.0;
    NAV_Data_Full.Macc[1] = 0.0;
    NAV_Data_Full.Macc[2] = 9.8;
    NAV_Data_Full.SINS.db[0] = 0.0;
    NAV_Data_Full.SINS.db[1] = 0.0;
    NAV_Data_Full.SINS.db[2] = 0.0;

    // 测试2: 验证SINS_Init函数中的修复
    printf("\n测试2: 验证SINS_Init函数中的修复\n");

    // 调用SINS_Init函数（模拟系统重启后的初始化）
    printf("调用SINS_Init函数...\n");
    SINS_Init(&NAV_Data_Full);

    // 检查Pre_att[2]是否被正确设置
    printf("SINS_Init后Pre_att[2]: %.6f 弧度 (%.2f 度)\n",
           NAV_Data_Full.Pre_att[2], NAV_Data_Full.Pre_att[2] * 180.0 / M_PI);

    // 验证Pre_att[2]是否正确恢复
    double pre_att_diff = fabs(NAV_Data_Full.Pre_att[2] - test_azimuth);
    if (pre_att_diff < 0.001) {
        printf("✓ SINS_Init修复成功: Pre_att[2]正确恢复了保存的航向角\n");
    } else {
        printf("✗ SINS_Init修复失败: Pre_att[2]没有正确恢复保存的航向角\n");
        printf("  期望值: %.6f 弧度, 实际值: %.6f 弧度, 差值: %.6f 弧度\n",
               test_azimuth, NAV_Data_Full.Pre_att[2], pre_att_diff);
    }

    // 测试3: 验证StartCoarseAlign默认分支的修复
    printf("\n测试3: 验证StartCoarseAlign默认分支的修复\n");

    // 设置条件使其进入默认分支（GPS条件不满足）
    NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
    NAV_Data_Full.GPS.rtkStatus = E_GPS_RTK_INVALID;
    NAV_Data_Full.GPS.headingStatus = E_GPS_RTK_INVALID;
    NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;

    printf("设置GPS条件不满足，测试默认分支...\n");

    // 调用StartCoarseAlign函数
    printf("调用StartCoarseAlign函数...\n");
    unsigned int align_result = StartCoarseAlign(&NAV_Data_Full);
    printf("StartCoarseAlign返回值: %u\n", align_result);

    // 检查SINS.att[2]的值
    printf("默认分支修复后SINS.att[2]: %.6f 弧度 (%.2f 度)\n",
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);

    // 验证修复是否生效
    double angle_diff = fabs(NAV_Data_Full.SINS.att[2] - test_azimuth);
    if (angle_diff < 0.001) { // 允许1毫弧度的误差
        printf("✓ 默认分支修复成功: SINS.att[2]使用了从Flash恢复的航向角\n");
    } else {
        printf("✗ 默认分支修复失败: SINS.att[2]没有使用从Flash恢复的航向角\n");
        printf("  期望值: %.6f 弧度, 实际值: %.6f 弧度, 差值: %.6f 弧度\n",
               test_azimuth, NAV_Data_Full.SINS.att[2], angle_diff);
    }

    // 测试4: 验证无有效保存时的行为
    printf("\n测试4: 验证无有效保存时的行为\n");

    // 清除有效标志
    stSetPara.AzimuthValidFlag = 0;
    printf("清除航向角有效标志\n");

    // 重新初始化系统状态
    memset(&NAV_Data_Full, 0, sizeof(_NAV_Data_Full_t));
    NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;
    NAV_Data_Full.UseCase = E_USE_CASE_In_Vehicle;
    NAV_Data_Full.Macc[0] = 0.0;
    NAV_Data_Full.Macc[1] = 0.0;
    NAV_Data_Full.Macc[2] = 9.8;

    // 调用SINS_Init
    printf("无有效保存时调用SINS_Init函数...\n");
    SINS_Init(&NAV_Data_Full);

    // 检查Pre_att[2]的值
    printf("无有效保存时Pre_att[2]: %.6f 弧度 (%.2f 度)\n",
           NAV_Data_Full.Pre_att[2], NAV_Data_Full.Pre_att[2] * 180.0 / M_PI);

    // 验证是否为默认值
    if (fabs(NAV_Data_Full.Pre_att[2]) < 0.001) {
        printf("✓ 无有效保存时正确使用默认值0\n");
    } else {
        printf("✗ 无有效保存时未使用默认值0\n");
        printf("  实际值: %.6f 弧度\n", NAV_Data_Full.Pre_att[2]);
    }

    // 调用StartCoarseAlign测试默认分支
    printf("无有效保存时调用StartCoarseAlign函数...\n");
    align_result = StartCoarseAlign(&NAV_Data_Full);
    printf("StartCoarseAlign返回值: %u\n", align_result);

    // 检查SINS.att[2]的值
    printf("无有效保存时SINS.att[2]: %.6f 弧度 (%.2f 度)\n",
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);

    // 验证是否为默认值（应该不会被设置，因为没有有效保存）
    if (align_result == 0) {
        printf("✓ 无有效保存时StartCoarseAlign正确返回0（未初始化）\n");
    } else {
        printf("✗ 无有效保存时StartCoarseAlign返回了非0值\n");
    }

    printf("\n=== 航向角掉电保存修复功能测试完成 ===\n");
}

// 打印航向角调试信息
void print_azimuth_fix_debug_info(void)
{
    printf("=== 航向角掉电保存修复调试信息 ===\n");
    
    printf("当前航向角保存状态:\n");
    printf("  保存的航向角: %.6f 弧度 (%.2f 度)\n", 
           stSetPara.SavedAzimuth, stSetPara.SavedAzimuth * 180.0 / M_PI);
    printf("  有效标志: 0x%08X (%s)\n", 
           stSetPara.AzimuthValidFlag, 
           IsAzimuthSaveValid() ? "有效" : "无效");
    printf("  保存次数: %u\n", stSetPara.AzimuthSaveCount);
    
    printf("当前导航数据:\n");
    printf("  Pre_att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.Pre_att[2], NAV_Data_Full.Pre_att[2] * 180.0 / M_PI);
    printf("  SINS.att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("  GPS.Heading_cor: %.2f 度\n", NAV_Data_Full.GPS.Heading_cor);
    
    printf("系统状态:\n");
    printf("  Nav_Standard_flag: %u\n", NAV_Data_Full.Nav_Standard_flag);
    printf("  UseCase: %u\n", NAV_Data_Full.UseCase);
    printf("  GPS.gps_up_flag: %u\n", NAV_Data_Full.GPS.gps_up_flag);
    printf("  GPS.rtkStatus: %u\n", NAV_Data_Full.GPS.rtkStatus);
    printf("  GPS.headingStatus: %u\n", NAV_Data_Full.GPS.headingStatus);
    printf("  ins_buffer_full_flag: %u\n", NAV_Data_Full.ins_buffer_full_flag);
    
    printf("=== 调试信息结束 ===\n");
}
