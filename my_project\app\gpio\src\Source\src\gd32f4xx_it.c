/*!
    \file  gd32f4xx_it.c
    \brief interrupt service routines
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#include "gd32f4xx_it.h"
#include "main.h"
#include "systick.h"
#include "bsp_fmc.h"
#include "INS_Data.h"
#include "bsp_tim.h"
#include "Logger.h"
#include "bsp_gpio.h"
#include "stdarg.h"
#include "fpgad.h"
#include "deviceconfig.h"
#include "appmain.h"
#include "uart.h"

extern uint8_t fisrtTimeGNSSTimeSync;			//
extern uint8_t fpga_syn;
extern uint32_t fpga_syn_count;

extern int g_gpsWeek;							//GPS		
extern double g_gpsSecond;						//GPS		

extern char g_logFileName[];
extern uint8_t g_usb_ready;
extern LogBufTypeDef* g_pLogBuf;

extern uint8_t g_KF_OutData_Rx_Flag;

extern uint32_t g_CAN_Timeout_Start_flag;
extern uint32_t g_CAN_Timeout_Cnt;

//extern FlagStatus g_transfer_complete;

void EXTI5_9_IRQHandler(void)
{
	//if(RESET != exti_interrupt_flag_get(EXTI_7)) 
	{				//ARM 1 to ARM 2 sync INT
//		gtime_t gt;
//		rtc_parameter_struct rtc_arg;
//		struct tm time;
//		DRam_Read(0,(uint16_t*)&g_week,2);
//		DRam_Read(2,(uint16_t*)&g_second,4);
//		if(fisrtTimeGNSSTimeSync == 1)
//		{
//			fisrtTimeGNSSTimeSync = 0;
//			gt = gpst2time(g_week,g_second);
//			gt = timeadd(gt,0.0435);			//GNSS PPS 
//			gt = gpst2utc(gt);					//GPSUTC
//			memcpy(&time,localtime(&gt.time),sizeof(struct tm));
//			rtc_arg.day_of_week = time.tm_wday;
//			rtc_arg.display_format = RTC_24HOUR;
//			rtc_arg.year = time.tm_year/10 + time.tm_year%10;
//			rtc_arg.month = time.tm_mon/10 + time.tm_mon%10;
//			rtc_arg.date = time.tm_mday/10 + time.tm_mday%10;
//			rtc_arg.hour = time.tm_hour/10 + time.tm_hour%10;
//			rtc_arg.minute = time.tm_min/10 + time.tm_min%10;
//			rtc_arg.second = time.tm_sec/10 + time.tm_sec%10;
//	
//			if(rtc_arg.hour > 12)
//				rtc_arg.am_pm = RTC_PM;
//			else
//				rtc_arg.am_pm = RTC_AM;
//			rtc_setup(&rtc_arg);
//		}
		memset(&hINSFPGAData.data_stream, 0, sizeof(hINSFPGAData));
		DRam_Read(0x400,(uint16_t*)&hINSFPGAData.fpga_cache,sizeof(hINSFPGAData.fpga_cache)/sizeof(uint16_t));
		g_LEDIndicatorState = (LEDStateEnumTypeDef)hINSFPGAData.data_stream.reserved[0];
		if(g_usb_ready == 1)
		{
			synthesisLogBuf((uint8_t*)&hINSFPGAData.data_stream,sizeof(hINSFPGAData.fpga_cache)/sizeof(uint8_t),Log_Type_0,g_pLogBuf);
			writeCSVLog((unsigned char*)g_logFileName,g_pLogBuf);
		}
		//exti_interrupt_flag_clear(EXTI_7);
	}
	//else if(RESET != exti_interrupt_flag_get(EXTI_9)) //CAN1   INT
	{			
		//exti_interrupt_flag_clear(EXTI_9);
	}
}

void EXTI3_IRQHandler(void)
{
	fpga_syn = 1;
	fpga_syn_count++;
}

/*!
    \brief      this function handles external lines 10 to 15 interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
void EXTI10_15_IRQHandler(void)
{
	//if(RESET != exti_interrupt_flag_get(EXTI_11))					//FPGA PWM sync INT			622
	{
		//fpga_syn = 1;
//		getRTCWeekSecond(&g_gpsWeek,&g_gpsSecond);
//		DRam_Write(0,(uint16_t*)&hINSData.CAN_Data,sizeof(CanDataTypeDef));
//		if(fisrtTimeGNSSTimeSync == 0)
//		{
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_Low();
//			ARM2_OUTPUT_ARM1_High();								// ARM2 to ARM1 Sync INT
//		}
		//exti_interrupt_flag_clear(EXTI_11);
	}
	//else if(RESET != exti_interrupt_flag_get(EXTI_14))				//CAN2   INT
	{
		//exti_interrupt_flag_clear(EXTI_14);
	}
}

void TIMER0_UP_TIMER9_IRQHandler(void)
{
	//if(SET == timer_interrupt_flag_get(TIMER0,TIMER_INT_FLAG_UP))
	{
		time_periodic_sec_cnt ++;
		if(time_periodic_sec_cnt == 1000)
		{
			time_periodic_sec_cnt = 0;
			time_periodic_min_cnt ++;
			if(time_periodic_min_cnt == 60)
			{
				time_periodic_min_cnt = 0;
				time_periodic_hour_cnt ++;
				memset(g_logFileName,0,sizeof(char)*256);
				//generateCSVLogFileName(g_logFileName);
				if(time_periodic_hour_cnt == 4)
				{
					time_periodic_hour_cnt = 0;
					time_sync_flag = 1;
					//timeSync(g_week,g_second);
				}
			}
		}
	}
}

//1ms
ATTR_RAMFUNC
void TIMER2_IRQHandler(void)
{
        static uint16_t SDCnt=0;

        SDCnt++;
	time_base_periodic_cnt ++;
	time_base_100ms_periodic_cnt ++;
	time_base_20ms_periodic_cnt++;
	//if(SET == timer_interrupt_flag_get(TIMER2, TIMER_INT_FLAG_UP))
	{
		//timer_interrupt_flag_clear(TIMER2, TIMER_INT_FLAG_UP);
		if(time_base_periodic_cnt == 1000)
			time_base_periodic_cnt = 0;
		if(time_base_100ms_periodic_cnt == 100)
		{
			time_base_100ms_periodic_cnt = 0;
			time_base_100ms_Flag = 1;
		}
		if(time_base_20ms_periodic_cnt == 20)
		{
			time_base_20ms_periodic_cnt = 0;
			time_base_20ms_Flag = 1;
		}
		
		if(g_CAN_Timeout_Start_flag == 1)
			g_CAN_Timeout_Cnt ++;
		if(g_CAN_Timeout_Cnt >= 1000)
		{
			g_CAN_Timeout_Cnt = 0;
			g_LEDIndicatorState = (LEDStateEnumTypeDef)(g_LEDIndicatorState & 0x0D);//LED
		}
	}

        //if(((SetSdFlieType==0x02)||(SetSdFlieType==0x03))&&(g_BB00WriteSdFlag==1)&&(SetSdOperateType==0x01))
        ////if(((SetSdFlieType==0x02)||(SetSdFlieType==0x03))&&(SetSdOperateType==0x01))
        //{
        //    g_BB00WriteSdFlag=0;
        //    WriteBB00FileToSd();
        //}

          //if(SDCnt >=5)
          //{
          //    SDCnt=0;
          //    SdFileWriteOperate(); //写数据到SD卡	
          //}
         
}

void TIMER1_IRQHandler(void)
{
	
}

#ifdef USE_ENET_INTERRUPT
/*!
    \brief      this function handles ethernet interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
//void ENET_IRQHandler(void)
//{
//    portBASE_TYPE xHigherPriorityTaskWoken = pdFALSE;

//    /* frame received */
//    if(SET == enet_interrupt_flag_get(ENET_DMA_INT_FLAG_RS)){ 
//        /* give the semaphore to wakeup LwIP task */
//        xSemaphoreGiveFromISR(g_rx_semaphore, &xHigherPriorityTaskWoken);
//    }

//    /* clear the enet DMA Rx interrupt pending bits */
//    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_RS_CLR);
//    enet_interrupt_flag_clear(ENET_DMA_INT_FLAG_NI_CLR);

//    /* switch tasks if necessary */
//    if(pdFALSE != xHigherPriorityTaskWoken){
//        portEND_SWITCHING_ISR(xHigherPriorityTaskWoken);
//    }
//    
//}
#endif /* USE_ENET_INTERRUPT */

				  

void uart4sendmsg(char *txbuf, int size)
{
    //printf("uart4sendmsg1111\n");
    UartIrqSendMsg(txbuf,size);	
}


