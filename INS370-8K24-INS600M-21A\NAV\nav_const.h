/*!
    \file  nav_const.h
    \brief Navigation system constants
*/

#ifndef __NAV_CONST_H
#define __NAV_CONST_H

#ifdef __cplusplus
extern "C" {
#endif

#include <math.h>

/* Mathematical constants */
#ifndef PI
#define PI 3.141592653589793
#endif

#ifndef D2R
#define D2R (PI/180.0)
#endif

#ifndef R2D
#define R2D (180.0/PI)
#endif

/* Earth parameters */
#define WIE 7.2921151467e-5     /* Earth rotation rate (rad/s) */
#define G0  9.80665             /* Standard gravity (m/s²) */

/* Navigation timing */
#define TIME_NAVI       0.01    /* Navigation update period (s) */
#define TIME_FILTER     0.1     /* Filter update period (s) */

/* Matrix dimensions */
#define MAX_MAT_DIM     20      /* Maximum matrix dimension */

/* Default initial position (Shanghai) */
#define DEFAULT_LATI    31.2304 /* Default latitude (degrees) */
#define DEFAULT_LOGI    121.4737 /* Default longitude (degrees) */
#define DEFAULT_HEI     50.0    /* Default height (meters) */

/* Default initial velocity */
#define DEFAULT_VN      0.0     /* Default north velocity (m/s) */
#define DEFAULT_VE      0.0     /* Default east velocity (m/s) */
#define DEFAULT_VU      0.0     /* Default up velocity (m/s) */

#ifdef __cplusplus
}
#endif

#endif /* __NAV_CONST_H */
