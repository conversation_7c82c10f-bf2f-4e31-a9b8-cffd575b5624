/*!
    \file  nav_mahony.h
    \brief Navigation Mahony filter header file
*/

#ifndef __NAV_MAHONY_H
#define __NAV_MAHONY_H

#include "nav.h"
#include "nav_magnet.h"

typedef enum {
    NAV_MAHONY_STATUS_OK = 0,
    NAV_MAHONY_STATUS_ERROR
} nav_mahony_status_t;

typedef struct {
    float quaternion[4];
    float euler[3];
} nav_mahony_result_t;

nav_mahony_status_t nav_mahony_init(void);
nav_mahony_status_t nav_mahony_update(const nav_imu_data_t *imu_data, const nav_magnet_data_t *mag_data, nav_mahony_result_t *result);

#endif /* __NAV_MAHONY_H */
