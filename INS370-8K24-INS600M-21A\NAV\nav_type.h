/*!
    \file  nav_type.h
    \brief Navigation system type definitions
*/

#ifndef __NAV_TYPE_H
#define __NAV_TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* Basic type definitions */
typedef double DPARA;
typedef float SPARA;
typedef int IPARA;
typedef unsigned int UPARA;
typedef char BOOL;

/* Navigation specific types */
typedef double LATI;        /* Latitude */
typedef double LOGI;        /* Longitude */
typedef float HEIGHT;       /* Height */
typedef float VEL;          /* Velocity */
typedef float ATTI;         /* Attitude */
typedef float ACCELER;      /* Acceleration */
typedef float GYRO;         /* Angular velocity */
typedef float MATR;         /* Matrix element */
typedef float VEC;          /* Vector element */
typedef float QUAT;         /* Quaternion element */
typedef float SCAL;         /* Scalar */
typedef uint32_t TIME;      /* Time */
typedef uint32_t COUNT;     /* Counter */
typedef float LEN;          /* Length */

/* Constants */
#ifndef YES
#define YES 1
#endif

#ifndef NO
#define NO 0
#endif

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#ifndef NULL
#define NULL ((void*)0)
#endif

#ifdef __cplusplus
}
#endif

#endif /* __NAV_TYPE_H */
