#include "ins.h"

INS_STATE_ST ins_state ={0};

void sins_state_update(SENS_RAW_ST *psins_raw, GNSS_RAW_ST *pgnss_raw)//״̬һ��Ԥ�����
{	
    ftype dt =  1.0/200.0;
    ftype vecLength = 0.0;

    V3_ST gyro         = {0};
    V3_ST dAngk_1      = {0};
    V3_ST dAngk_1xAngk = {0};   
    V3_ST phi          = {0};

    V3_ST acc          = {0};
    V3_ST dVk_1        = {0};
    
    V3_ST dVrot        = {0};
    V3_ST dAngk_1xdVk  = {0};
    V3_ST dVk_1xdAngk  = {0};
    V3_ST dVscull      = {0};
    V3_ST dVsf         = {0};
    VNED_ST dVsf_k     = {0};
    VNED_ST dVcorg     = {0};
    VNED_ST temp_v     = {0};

    VNED_ST tempned = {0};
    double origgeo[3]= {0};
    double tempgeo[3]= {0};

	static  V3_ST dAngk = {0};//����־ñ���
	static  V3_ST dVk   = {0};

    Q4_ST qh       = {0};
    Q4_ST quat_new = {0};
    ftype recipNorm = 0.0;

	ftype q0 = ins_state.q0; 
	ftype q1 = ins_state.q1; 
	ftype q2 = ins_state.q2; 
	ftype q3 = ins_state.q3;

    ftype Cbnk_1[3][3] = {0};

	Cbnk_1[0][0] = q0*q0 + q1*q1 - q2*q2 - q3*q3;
    Cbnk_1[0][1] = 2*(q1*q2 - q0*q3);
    Cbnk_1[0][2] = 2*(q1*q3 + q0*q2);
    Cbnk_1[1][0] = 2*(q1*q2 + q0*q3);
    Cbnk_1[1][1] = q0*q0 - q1*q1 + q2*q2 - q3*q3;
    Cbnk_1[1][2] = 2*(q2*q3 - q0*q1);
    Cbnk_1[2][0] = 2*(q1*q3 - q0*q2);
    Cbnk_1[2][1] = 2*(q2*q3 + q0*q1);
    Cbnk_1[2][2] = q0*q0 - q1*q1 - q2*q2 + q3*q3;

    //-------------------------------������SINS�����ϵͳ��̬����----------------------------------------------------
    //�������ֵ��ȥ0ƫ�������ֵƯ�Ʋ���
	//���������Ϊ��/�룬Ҫ��ת��Ϊ����/��
    psins_raw->gyro_const_bias.x = 0;
    psins_raw->gyro_const_bias.y = 0;
    psins_raw->gyro_const_bias.z = 0;

    gyro.x = (psins_raw->gyro.x - psins_raw->gyro_const_bias.x)*DEG2RAD;
	gyro.y = (psins_raw->gyro.y - psins_raw->gyro_const_bias.y)*DEG2RAD;
	gyro.z = (psins_raw->gyro.z - psins_raw->gyro_const_bias.z)*DEG2RAD;

    dAngk_1.x = dAngk.x;//ǰһ�����ڵĽ�����
	dAngk_1.y = dAngk.y;
	dAngk_1.z = dAngk.z;

    //���㵱ǰʱ�̵����������,Ҫ��ȥEKF���Ƶ�0ƫ,����0ƫ�������ﷴ����
    //ins_state.gyro_bias.x = 0.0;%������
    //ins_state.gyro_bias.y = 0.0;
    //ins_state.gyro_bias.z = 0.0;

    dAngk.x = (gyro.x - ins_state.gyro_bias.x)*dt;
	dAngk.y = (gyro.y - ins_state.gyro_bias.y)*dt;
	dAngk.z = (gyro.z - ins_state.gyro_bias.z)*dt;

    dAngk_1xAngk = v3_cross(dAngk_1,dAngk);//dAng_k_1��dAng_k�����

    //���￼����Բ׶���Ĳ���
	//��Բ׶�����е�˫����=������+ǰһ������תʸ�����㷽����PHI = dAng + (1/12) dAng_old xdAng
    //PHI = dAng + 1/12*dAng_oldxdAng��������+ǰһ���ڲ���Բ׶����
    phi.x = dAngk.x + fac*dAngk_1xAngk.x;
    phi.y = dAngk.y + fac*dAngk_1xAngk.y;
    phi.z = dAngk.z + fac*dAngk_1xAngk.z;

    //�������תʸ����ģֵ������ϵ�Ƹ���תʸ��ת���ĽǶ�
    //��תʸ��ת��Ԫ��q(h) = cos(fai/2) + sin(fai/2)*FAI/fai
    //0.000001 deg = 1.745e-8rad
    vecLength = sqrt(SQR(phi.x) + SQR(phi.y) + SQR(phi.z));
    if(vecLength < 1e-10)//��תʸ��ģֵ��С����µĴ���
    {
        qh.q0 = 1;
        qh.q1 = 0;
        qh.q2 = 0;
        qh.q3 = 0;
    }
    else
    {
        //��תʸ��ת��Ϊ��Ԫ��
        //q(h) = cos(fai/2) + sin(fai/2)*FAI/fai
        qh.q0 = cos(0.5*vecLength);
        qh.q1 = sin(0.5*vecLength)*phi.x/vecLength;
        qh.q2 = sin(0.5*vecLength)*phi.y/vecLength;
        qh.q3 = sin(0.5*vecLength)*phi.z/vecLength;
    }

    //��Ԫ���˷�Q(t+h) = Q(t)*q(h)�������ݻ���ϵ������µ�ǰ��̬��Ԫ��
    //������Ϊ����ϵ�仯����������ϵ���Ը���GNSS�ĸ���Ƶ��ÿ���Ӹ���һ��Ҳû����
	//    		|p0  -p1  -p2  -p3|   |q0| |p0*q0 - p1*q1 - p2*q2 - p3*q3|   |q0  -q1  -q2  -q3|   |p0|
	//	p x q = |p1   p0  -p3   p2| x |q1|=|p1*q0 + p0*q1 - p3*q2 + p2*q3| = |q1   q0  -q3   q2| x |p1|
	//    		|p2   p3   p0  -p1|   |q2| |p2*q0 + p3*q1 + p0*q2 - p1*q3|   |q2   q3   q0  -q1|   |p2|
	//			|p3  -p2   p1   p0|   |q3| |p3*q0 - p2*q1 + p1*q2 + p0*q3|   |q3  -q2   q1   q0|   |p3|
    quat_new.q0 = ins_state.q0*qh.q0 - ins_state.q1*qh.q1 - ins_state.q2*qh.q2 - ins_state.q3*qh.q3;
    quat_new.q1 = ins_state.q0*qh.q1 + ins_state.q1*qh.q0 + ins_state.q2*qh.q3 - ins_state.q3*qh.q2;
    quat_new.q2 = ins_state.q0*qh.q2 - ins_state.q1*qh.q3 + ins_state.q2*qh.q0 + ins_state.q3*qh.q1;
    quat_new.q3 = ins_state.q0*qh.q3 + ins_state.q1*qh.q2 - ins_state.q2*qh.q1 + ins_state.q3*qh.q0;

    //��Ԫ����һ����ֻ�е�λ��Ԫ�����ܱ�ʾ��̬
    recipNorm = 1.0/sqrt(SQR(quat_new.q0) + SQR(quat_new.q1) + SQR(quat_new.q2) + SQR(quat_new.q3));
    ins_state.q0 = quat_new.q0*recipNorm;
    ins_state.q1 = quat_new.q1*recipNorm;
    ins_state.q2 = quat_new.q2*recipNorm;
    ins_state.q3 = quat_new.q3*recipNorm;
    //-------------------------------������SINS�����ϵͳ��̬����----------------------------------------------------

    //-------------------------------������SINS�����ϵͳ�ٶȸ���----------------------------------------------------
    //�Ӽ����ֵ��ȥ0ƫ�������ֵƯ�Ʋ��֣��ϸ���˵Ӧ���ǰ��׷������������������ֵƯ��ֵ��
	//����Ӽ������Ϊg��Ҫ��ת��Ϊ��/��
	//acc.x  = (sins_raw.acc.x -sins_raw.acc_const_bias.x)*ins_state.gn;
	//acc.y  = (sins_raw.acc.y -sins_raw.acc_const_bias.y)*ins_state.gn;
	//acc.z  = (sins_raw.acc.z -sins_raw.acc_const_bias.z)*ins_state.gn;
    
    sins_raw.acc_const_bias.x = 0.0;
    sins_raw.acc_const_bias.y = 0.0;
    sins_raw.acc_const_bias.z = 0.0;

    acc.x  = sins_raw.acc.x -sins_raw.acc_const_bias.x;
	acc.y  = sins_raw.acc.y -sins_raw.acc_const_bias.y;
	acc.z  = sins_raw.acc.z -sins_raw.acc_const_bias.z;

    dVk_1.x = dVk.x;//ǰһ�����ڵ��ٶ�����
	dVk_1.y = dVk.y;
	dVk_1.z = dVk.z;

    //����������Ҫʵʱ�����Ӽӱ�0ƫ
	//�������ƫ��������ϵ���EKF���ƵļӼ�0ƫ����Ҫ�ɿ���������ٶ�������߽�ģΪ���ٶȸ�˹��������
	//����������(��ģΪ���ٶ�һ�������Ʒ����)��ɣ�ʵ��������ϵ����еļӼ�0ƫEKF���ƺ�ķ���У��
	ins_state.acc_bias.x = 0.0;//����
	ins_state.acc_bias.y = 0.0;
	ins_state.acc_bias.z = 0.0;

    //���㵱ǰ��������������ٶ���������ȥEKF���Ƶ�0ƫ
    //g  = double(9.7925);
    dVk.x   = ins_state.gn*(acc.x -ins_state.acc_bias.x)*dt;
	dVk.y   = ins_state.gn*(acc.y -ins_state.acc_bias.y)*dt;
	dVk.z   = ins_state.gn*(acc.z -ins_state.acc_bias.z)*dt;

    //������ת���deltVrot = 0.5*(deltAngk x deltVk)
	dVrot   = v3_cross(dAngk,dVk);
	dVrot.x = 0.5*dVrot.x;dVrot.y = 0.5*dVrot.y;dVrot.z = 0.5*dVrot.z;
           
	dAngk_1xdVk = v3_cross(dAngk_1,dVk);//����ǰһ�����ڵĽ������͵�ǰ�����ٶ������Ĳ�� deltAng_k_1 x deltVk
	dVk_1xdAngk = v3_cross(dVk_1,dAngk);//����ǰһ�����ڵ��ٶ������͵�ǰ���ڽ������Ĳ��deltVk_1 x deltAng_k
    
    //���㻮����� deltVscull = 1/12 *(deltAng_k_1 x deltVk + deltVk_1 x deltAng_k)
	//������+ǰһ���ڻ�������
	dVscull.x   = fac*(dAngk_1xdVk.x + dVk_1xdAngk.x);
    dVscull.y   = fac*(dAngk_1xdVk.y + dVk_1xdAngk.y);
    dVscull.z   = fac*(dAngk_1xdVk.z + dVk_1xdAngk.z);  

    //�����ٶ���������deltVsf_k = deltVk+deltVrot+deltVscull
	//������������ٶ�����+��ת���+�������
	//dVsf.x = dVk.x + dVrot.x + dVscull.x;
	//dVsf.y = dVk.y + dVrot.y + dVscull.y;
	//dVsf.z = dVk.z + dVrot.z + dVscull.z;

    dVsf.x = dVk.x + dVrot.x + dVscull.x;//�ٶ�����
	dVsf.y = dVk.y + dVrot.y + dVscull.y;
	dVsf.z = dVk.z + dVrot.z + dVscull.z;

    //dVsf�ӻ���ϵת������ϵ
	dVsf_k.n = Cbnk_1[0][0]*dVsf.x + Cbnk_1[0][1]*dVsf.y + Cbnk_1[0][2]*dVsf.z;
	dVsf_k.e = Cbnk_1[1][0]*dVsf.x + Cbnk_1[1][1]*dVsf.y + Cbnk_1[1][2]*dVsf.z;
	dVsf_k.d = Cbnk_1[2][0]*dVsf.x + Cbnk_1[2][1]*dVsf.y + Cbnk_1[2][2]*dVsf.z;

    dVcorg.n = 0.0*dt;//�����к����ٶ�������ٶ�����
	dVcorg.e = 0.0*dt;
	dVcorg.d = ins_state.gn*dt;

    //ȥ���к����ٶ�
	dVsf_k.n = dVsf_k.n + dVcorg.n;
	dVsf_k.e = dVsf_k.e + dVcorg.e;
	dVsf_k.d = dVsf_k.d + dVcorg.d;

    //��ת����������,��ȥ�к����ٶ�=>�ٶȴ�Vk_1���µ�Vk
	ins_state.vn_last = ins_state.vn;//�����ּ��ٶ���
	ins_state.ve_last = ins_state.ve;
	ins_state.vd_last = ins_state.vd;
    
	ins_state.vn = ins_state.vn_last + dVsf_k.n;
	ins_state.ve = ins_state.ve_last + dVsf_k.e;
	ins_state.vd = ins_state.vd_last + dVsf_k.d;
    //-------------------------------������SINS�����ϵͳ�ٶȸ���----------------------------------------------------

    //-------------------------------������SINS�����ϵͳλ�ø���----------------------------------------------------
    temp_v.n = 0.5*(ins_state.vn_last + ins_state.vn);
	temp_v.e = 0.5*(ins_state.ve_last + ins_state.ve);
	temp_v.d = 0.5*(ins_state.vd_last + ins_state.vd);

    ins_state.pn = ins_state.pn + temp_v.n*dt;//λ��=�ٶ����λ���
	ins_state.pe = ins_state.pe + temp_v.e*dt;
	ins_state.pd = ins_state.pd + temp_v.d*dt;

    //tempned.n = ins_state.pn;
    //tempned.e = ins_state.pe;
    //tempned.d = ins_state.pd;

    //origgeo[0] = ins_state.ned_orig.lat;
    //origgeo[1] = ins_state.ned_orig.lon;
    //origgeo[2] = ins_state.ned_orig.alt;
    //ned2geo(tempned,origgeo,tempgeo);
    //ins_state.lat = tempgeo[0];
    //ins_state.lon = tempgeo[1];
    //ins_state.alt = tempgeo[2];

    //����ֱ������ת�������ļ���Ҫע�⣺
    //��Ȧ�İ뾶�ǵ���뾶R��γȦ�İ뾶��R*cos(lat)
    ins_state.lat  = ins_state.ned_orig.lat + (ins_state.pn/WGS84_R)*RAD2DEG;

    ftype Lat_R = WGS84_R*cos(ins_state.lat*DEG2RAD);//γȦ�뾶
    ins_state.lon = ins_state.ned_orig.lon + (ins_state.pe/Lat_R)*RAD2DEG;
    ins_state.alt =  -ins_state.pd - ins_state.ned_orig.alt;
    //-------------------------------������SINS�����ϵͳλ�ø���----------------------------------------------------

    q0 = ins_state.q0;q1 = ins_state.q1;q2 = ins_state.q2;q3 = ins_state.q3;
    ins_state.roll  = RAD2DEG*atan2(2*(q2*q3 + q0*q1), 1.0 - 2.0*(q1*q1 + q2*q2));
    ins_state.pitch = RAD2DEG*asin(-2*(q1*q3 - q0*q2));
    ins_state.yaw   = RAD2DEG*atan2(2*(q1*q2 + q0*q3), 1.0 - 2.0*(q2*q2 + q3*q3));

	if(ins_state.yaw<0)ins_state.yaw = ins_state.yaw+360;
}

void sins_pk_update(SENS_RAW_ST *psins_raw)
{
    static bool     STEKF_IS_RUNNING = false;
    static uint16_t stekf_feed_count = 0;
    uint8_t i = 0,j = 0;

    ftype dt = sins_raw.dt;
    V3_ST acc = {0};
    ftype fx  = 0.0,fy = 0.0,fz = 0.0;
    ftype fn  = 0.0,fe = 0.0,fd = 0.0;

    ftype temp = 0.0;

    //��һʱ�̵ķ������Ҿ���
    //      |q0*q0+q1*q1-q2*q2-q3*q3   2*(q1*q2-q0*q3)             2*(q1*q3+q0*q2)        |
    //      |                                                                             |
    //Cbn = |2*(q1*q2+q0*q3)           q0*q0-q1*q1+q2*q2-q3*q3     2*(q2*q3-q0*q1)        |
    //      |                                                                             |
    //      |2*(q1*q3-q0*q2)           2*(q2*q3+q0*q1)             q0*q0-q1*q1-q2*q2+q3*q3|
    ftype q0 = ins_state.q0; 
    ftype q1 = ins_state.q1; 
    ftype q2 = ins_state.q2; 
    ftype q3 = ins_state.q3;
    
    ftype C00 = q0*q0 + q1*q1 - q2*q2 - q3*q3;
    ftype C01 = 2*(q1*q2 - q0*q3);
    ftype C02 = 2*(q1*q3 + q0*q2);
    ftype C10 = 2*(q1*q2 + q0*q3);
    ftype C11 = q0*q0 - q1*q1 + q2*q2 - q3*q3;
    ftype C12 = 2*(q2*q3 - q0*q1);
    ftype C20 = 2*(q1*q3 - q0*q2);
    ftype C21 = 2*(q2*q3 + q0*q1);
    ftype C22 = q0*q0 - q1*q1 - q2*q2 + q3*q3;

    //gת��Ϊm/s
    sins_raw.acc_const_bias.x = 0.0;
    sins_raw.acc_const_bias.y = 0.0;
    sins_raw.acc_const_bias.z = 0.0;

    acc.x  = (sins_raw.acc.x -sins_raw.acc_const_bias.x)*ins_state.gn;
    acc.y  = (sins_raw.acc.y -sins_raw.acc_const_bias.y)*ins_state.gn;
    acc.z  = (sins_raw.acc.z -sins_raw.acc_const_bias.z)*ins_state.gn;

    ins_state.acc_bias.x = 0.0;
    ins_state.acc_bias.y= 0.0;
    ins_state.acc_bias.z= 0.0;

    fx   = acc.x -ins_state.acc_bias.x;
    fy   = acc.y -ins_state.acc_bias.y;
    fz   = acc.z -ins_state.acc_bias.z;
    
    fn   = C00*fx + C01*fy + C02*fz;
    fe   = C10*fx + C11*fy + C12*fz;
    fd   = C20*fx + C21*fy + C22*fz;

    //--------------------------�����⼸������Ϊ�ɵ��������ǴӴ�����оƬ�ֲ��ϻ�ȡ������ʱ��ϵͳ�µĲ�������ɢ��֮��ͳһ��dt���-------------
     ftype PARAM_GYROXY_KP  = 500.0;
     ftype PARAM_GYROZ_KP   = 500.0;//������Ҫ��������Լ�

     ftype PARAM_GYRO_ARW = 0.05;
	 ftype gyroRapidNoiseSigma   = (PARAM_GYRO_ARW/60.0)*DEG2RAD;
     ftype gyroxRapidNoiseVar    = PARAM_GYROXY_KP*SQR(gyroRapidNoiseSigma);
     ftype gyroyRapidNoiseVar    = PARAM_GYROXY_KP*SQR(gyroRapidNoiseSigma);
     ftype gyrozRapidNoiseVar    = PARAM_GYROZ_KP*SQR(gyroRapidNoiseSigma);//z����΢���һ��

     ftype gyroSlowNoiseSigma = (0.07/3600)*DEG2RAD;

	 ftype gyroxSlowNoiseVar   = 10.0*SQR(gyroSlowNoiseSigma);
	 ftype gyroySlowNoiseVar   = 10.0*SQR(gyroSlowNoiseSigma);
	 ftype gyrozSlowNoiseVar   = 10.0*SQR(gyroSlowNoiseSigma);

     ftype PARAM_ACCXY_KP  = 500.0;
     ftype PARAM_ACCZ_KP   = 500.0;

     ftype PARAM_ACC_VRW   = 500*1E-3*9.8;

	 ftype accRapidNoiseSigma   = PARAM_ACC_VRW/60.0;
	 ftype accxRapidNoiseVar    = PARAM_ACCXY_KP*SQR(accRapidNoiseSigma);
	 ftype accyRapidNoiseVar    = PARAM_ACCXY_KP*SQR(accRapidNoiseSigma);
	 ftype acczRapidNoiseVar    = PARAM_ACCZ_KP*SQR(accRapidNoiseSigma);

     ftype accSlowNoiseSigma = 10*1E-6*9.8/60;//100ug/h

	 ftype accxSlowNoiseVar   = 2.0*SQR(accSlowNoiseSigma);
	 ftype accySlowNoiseVar   = 2.0*SQR(accSlowNoiseSigma);
	 ftype acczSlowNoiseVar   = 2.0*SQR(accSlowNoiseSigma);

     ftype Q00   = gyroxRapidNoiseVar*dt;//���ݹ۲�����,��ģΪ��˹������
     ftype Q11   = gyroyRapidNoiseVar*dt;
     ftype Q22   = gyrozRapidNoiseVar*dt;

     ftype Q33   = accxRapidNoiseVar*dt;//�ӱ��۲�����,��ģΪ��˹������
     ftype Q44   = accyRapidNoiseVar*dt;
     ftype Q55   = acczRapidNoiseVar*dt;

     ftype Q66   = gyroxSlowNoiseVar*dt;//������ƫ��������,��ģΪһ�������Ʒ�
     ftype Q77   = gyroySlowNoiseVar*dt;
     ftype Q88   = gyrozSlowNoiseVar*dt;

     ftype Q99 = accxSlowNoiseVar*dt;//�ӱ�0ƫ��������,��ģΪһ�������Ʒ�
     ftype Q1010 = accySlowNoiseVar*dt;
     ftype Q1111 = acczSlowNoiseVar*dt;
     //--------------------------�����⼸������Ϊ�ɵ��������ǴӴ�����оƬ�ֲ��ϻ�ȡ������ʱ��ϵͳ�µĲ�������ɢ��֮��ͳһ��dt���-------------

     //---------------------���¼�����F*P*F'+G*Q*G'-------------------------------------------------
     ftype F09 = -C00*dt,F010 = -C01*dt,F011 = -C02*dt;
     ftype F19 = -C10*dt,F110 = -C11*dt,F111 = -C12*dt;
     ftype F29 = -C20*dt,F210 = -C21*dt,F211 = -C22*dt;
     ftype F31 = -dt*fd, F32 = dt*fe,F312 = C00*dt,F313 = C01*dt,F314 = C02*dt;
     ftype F40 = dt*fd,  F42 = -dt*fn,F412 = C10*dt,F413 = C11*dt,F414 = C12*dt;
     ftype F50 = -dt*fe, F51 = dt*fn,F512 = C20*dt,F513 = C21*dt,F514 = C22*dt;
     ftype F63 = dt,     F74 = dt,   F85 = dt;
           
     //F99   = 1 - dt/Tgb; F1010 = 1 - dt/Tgb;F1111 = 1 - dt/Tgb;
     //F1212 = 1 - dt/Tab; F1313 = 1 - dt/Tab;F1414 = 1 - dt/Tab;

     ftype F99   = 1, F1010 = 1,F1111 = 1;
     ftype F1212 = 1, F1313 = 1,F1414 = 1;

    ftype GQG00 = C00*C00*Q00 + C01*C01*Q11 + C02*C02*Q22;
    ftype GQG01 = C00*C10*Q00 + C01*C11*Q11 + C02*C12*Q22;
    ftype GQG02 = C00*C20*Q00 + C01*C21*Q11 + C02*C22*Q22;
    ftype GQG11 = C10*C10*Q00 + C11*C11*Q11 + C12*C12*Q22;
    ftype GQG12 = C10*C20*Q00 + C11*C21*Q11 + C12*C22*Q22;
    ftype GQG22 = C20*C20*Q00 + C21*C21*Q11 + C22*C22*Q22;
    ftype GQG33 = C00*C00*Q33 + C01*C01*Q44 + C02*C02*Q55;
    ftype GQG34 = C00*C10*Q33 + C01*C11*Q44 + C02*C12*Q55;
    ftype GQG35 = C00*C20*Q33 + C01*C21*Q44 + C02*C22*Q55;
    ftype GQG44 = C10*C10*Q33 + C11*C11*Q44 + C12* C12*Q55;
    ftype GQG45 = C10*C20*Q33 + C11*C21*Q44 + C12*C22*Q55;
    ftype GQG55 = C20*C20*Q33 + C21*C21*Q44 + C22*C22*Q55;


    GQG00 =0.0;
    GQG01 = 0.0;
    GQG02 = 0.0;
    GQG11 = 0.0;
    GQG12 = 0.0;
    GQG22 = 0.0;
    GQG33 = 0.0;
    GQG34 = 0.0;
    GQG35 = 0.0;
    GQG44 = 0.0;
     GQG45 = 0.0;
    GQG55 = 0.0;
     //ftype GQG1010 = Q77;//һ�������Ʒ����������
     //ftype GQG1111 = Q88;
     //ftype GQG1212 = Q99;
     //ftype GQG1313 = Q1010;
     //ftype GQG1414 = Q1111;
     //ftype GQG1515 = Q1212;

     //ftype GQG1010 = 0.0;//һ�������Ʒ����������
     //ftype GQG1111 = 0.0;
     //ftype GQG1212 = 0.0;
     //ftype GQG1313 = 0.0;
     //ftype GQG1414 = 0.0;
     //ftype GQG1515 = 0.0;

     ftype GQG99   = 0.0;//һ�������Ʒ����������
     ftype GQG1010 = 0.0;
     ftype GQG1111 = 0.0;
     ftype GQG1212 = 0.0;
     ftype GQG1313 = 0.0;
     ftype GQG1414 = 0.0;


     ftype GQG[15] = {0};
     GQG[0]  = Q00;
     GQG[1]  = Q11;
     GQG[2]  = Q22;
     GQG[3]  = Q33;
     GQG[4]  = Q44;
     GQG[5]  = Q55;

     GQG[9]  = Q66;  
     GQG[10] = Q77; 
     GQG[11] = Q88;
     GQG[12] = Q99;
     GQG[13] = Q1010;
     GQG[14] = Q1111;

     ftype nextP[15][15] = {0};

     //ֻ��Ҫ����������
	nextP[0][0]=ins_state.Pk[0][0] + F09*(ins_state.Pk[0][9] + F09*ins_state.Pk[9][9] + F010*ins_state.Pk[9][10] + F011*ins_state.Pk[9][11]) + F010*(ins_state.Pk[0][10] + F09*ins_state.Pk[9][10] + F010*ins_state.Pk[10][10] + F011*ins_state.Pk[10][11]) + F011*(ins_state.Pk[0][11] + F09*ins_state.Pk[9][11] + F010*ins_state.Pk[10][11] + F011*ins_state.Pk[11][11]) + F09*ins_state.Pk[0][9] + F010*ins_state.Pk[0][10] + F011*ins_state.Pk[0][11];
	nextP[0][1]=ins_state.Pk[0][1] + F19*(ins_state.Pk[0][9] + F09*ins_state.Pk[9][9] + F010*ins_state.Pk[9][10] + F011*ins_state.Pk[9][11]) + F110*(ins_state.Pk[0][10] + F09*ins_state.Pk[9][10] + F010*ins_state.Pk[10][10] + F011*ins_state.Pk[10][11]) + F111*(ins_state.Pk[0][11] + F09*ins_state.Pk[9][11] + F010*ins_state.Pk[10][11] + F011*ins_state.Pk[11][11]) + F09*ins_state.Pk[1][9] + F010*ins_state.Pk[1][10] + F011*ins_state.Pk[1][11];
	nextP[0][2]=ins_state.Pk[0][2] + F29*(ins_state.Pk[0][9] + F09*ins_state.Pk[9][9] + F010*ins_state.Pk[9][10] + F011*ins_state.Pk[9][11]) + F210*(ins_state.Pk[0][10] + F09*ins_state.Pk[9][10] + F010*ins_state.Pk[10][10] + F011*ins_state.Pk[10][11]) + F211*(ins_state.Pk[0][11] + F09*ins_state.Pk[9][11] + F010*ins_state.Pk[10][11] + F011*ins_state.Pk[11][11]) + F09*ins_state.Pk[2][9] + F010*ins_state.Pk[2][10] + F011*ins_state.Pk[2][11];
	nextP[0][3]=ins_state.Pk[0][3] + F31*(ins_state.Pk[0][1] + F09*ins_state.Pk[1][9] + F010*ins_state.Pk[1][10] + F011*ins_state.Pk[1][11]) + F32*(ins_state.Pk[0][2] + F09*ins_state.Pk[2][9] + F010*ins_state.Pk[2][10] + F011*ins_state.Pk[2][11]) + F312*(ins_state.Pk[0][12] + F09*ins_state.Pk[9][12] + F010*ins_state.Pk[10][12] + F011*ins_state.Pk[11][12]) + F313*(ins_state.Pk[0][13] + F09*ins_state.Pk[9][13] + F010*ins_state.Pk[10][13] + F011*ins_state.Pk[11][13]) + F314*(ins_state.Pk[0][14] + F09*ins_state.Pk[9][14] + F010*ins_state.Pk[10][14] + F011*ins_state.Pk[11][14]) + F09*ins_state.Pk[3][9] + F010*ins_state.Pk[3][10] + F011*ins_state.Pk[3][11];
	nextP[0][4]=ins_state.Pk[0][4] + F40*(ins_state.Pk[0][0] + F09*ins_state.Pk[0][9] + F010*ins_state.Pk[0][10] + F011*ins_state.Pk[0][11]) + F42*(ins_state.Pk[0][2] + F09*ins_state.Pk[2][9] + F010*ins_state.Pk[2][10] + F011*ins_state.Pk[2][11]) + F412*(ins_state.Pk[0][12] + F09*ins_state.Pk[9][12] + F010*ins_state.Pk[10][12] + F011*ins_state.Pk[11][12]) + F413*(ins_state.Pk[0][13] + F09*ins_state.Pk[9][13] + F010*ins_state.Pk[10][13] + F011*ins_state.Pk[11][13]) + F414*(ins_state.Pk[0][14] + F09*ins_state.Pk[9][14] + F010*ins_state.Pk[10][14] + F011*ins_state.Pk[11][14]) + F09*ins_state.Pk[4][9] + F010*ins_state.Pk[4][10] + F011*ins_state.Pk[4][11];
	nextP[0][5]=ins_state.Pk[0][5] + F50*(ins_state.Pk[0][0] + F09*ins_state.Pk[0][9] + F010*ins_state.Pk[0][10] + F011*ins_state.Pk[0][11]) + F51*(ins_state.Pk[0][1] + F09*ins_state.Pk[1][9] + F010*ins_state.Pk[1][10] + F011*ins_state.Pk[1][11]) + F512*(ins_state.Pk[0][12] + F09*ins_state.Pk[9][12] + F010*ins_state.Pk[10][12] + F011*ins_state.Pk[11][12]) + F513*(ins_state.Pk[0][13] + F09*ins_state.Pk[9][13] + F010*ins_state.Pk[10][13] + F011*ins_state.Pk[11][13]) + F514*(ins_state.Pk[0][14] + F09*ins_state.Pk[9][14] + F010*ins_state.Pk[10][14] + F011*ins_state.Pk[11][14]) + F09*ins_state.Pk[5][9] + F010*ins_state.Pk[5][10] + F011*ins_state.Pk[5][11];
	nextP[0][6]=ins_state.Pk[0][6] + F63*(ins_state.Pk[0][3] + F09*ins_state.Pk[3][9] + F010*ins_state.Pk[3][10] + F011*ins_state.Pk[3][11]) + F09*ins_state.Pk[6][9] + F010*ins_state.Pk[6][10] + F011*ins_state.Pk[6][11];
	nextP[0][7]=ins_state.Pk[0][7] + F74*(ins_state.Pk[0][4] + F09*ins_state.Pk[4][9] + F010*ins_state.Pk[4][10] + F011*ins_state.Pk[4][11]) + F09*ins_state.Pk[7][9] + F010*ins_state.Pk[7][10] + F011*ins_state.Pk[7][11];
	nextP[0][8]=ins_state.Pk[0][8] + F85*(ins_state.Pk[0][5] + F09*ins_state.Pk[5][9] + F010*ins_state.Pk[5][10] + F011*ins_state.Pk[5][11]) + F09*ins_state.Pk[8][9] + F010*ins_state.Pk[8][10] + F011*ins_state.Pk[8][11];
	nextP[0][9]=F99*(ins_state.Pk[0][9] + F09*ins_state.Pk[9][9] + F010*ins_state.Pk[9][10] + F011*ins_state.Pk[9][11]);
	nextP[0][10]=F1010*(ins_state.Pk[0][10] + F09*ins_state.Pk[9][10] + F010*ins_state.Pk[10][10] + F011*ins_state.Pk[10][11]);
	nextP[0][11]=F1111*(ins_state.Pk[0][11] + F09*ins_state.Pk[9][11] + F010*ins_state.Pk[10][11] + F011*ins_state.Pk[11][11]);
	nextP[0][12]=F1212*(ins_state.Pk[0][12] + F09*ins_state.Pk[9][12] + F010*ins_state.Pk[10][12] + F011*ins_state.Pk[11][12]);
	nextP[0][13]=F1313*(ins_state.Pk[0][13] + F09*ins_state.Pk[9][13] + F010*ins_state.Pk[10][13] + F011*ins_state.Pk[11][13]);
	nextP[0][14]=F1414*(ins_state.Pk[0][14] + F09*ins_state.Pk[9][14] + F010*ins_state.Pk[10][14] + F011*ins_state.Pk[11][14]);
	nextP[1][1]=ins_state.Pk[1][1] + F19*(ins_state.Pk[1][9] + F19*ins_state.Pk[9][9] + F110*ins_state.Pk[9][10] + F111*ins_state.Pk[9][11]) + F110*(ins_state.Pk[1][10] + F19*ins_state.Pk[9][10] + F110*ins_state.Pk[10][10] + F111*ins_state.Pk[10][11]) + F111*(ins_state.Pk[1][11] + F19*ins_state.Pk[9][11] + F110*ins_state.Pk[10][11] + F111*ins_state.Pk[11][11]) + F19*ins_state.Pk[1][9] + F110*ins_state.Pk[1][10] + F111*ins_state.Pk[1][11];
	nextP[1][2]=ins_state.Pk[1][2] + F29*(ins_state.Pk[1][9] + F19*ins_state.Pk[9][9] + F110*ins_state.Pk[9][10] + F111*ins_state.Pk[9][11]) + F210*(ins_state.Pk[1][10] + F19*ins_state.Pk[9][10] + F110*ins_state.Pk[10][10] + F111*ins_state.Pk[10][11]) + F211*(ins_state.Pk[1][11] + F19*ins_state.Pk[9][11] + F110*ins_state.Pk[10][11] + F111*ins_state.Pk[11][11]) + F19*ins_state.Pk[2][9] + F110*ins_state.Pk[2][10] + F111*ins_state.Pk[2][11];
	nextP[1][3]=ins_state.Pk[1][3] + F31*(ins_state.Pk[1][1] + F19*ins_state.Pk[1][9] + F110*ins_state.Pk[1][10] + F111*ins_state.Pk[1][11]) + F32*(ins_state.Pk[1][2] + F19*ins_state.Pk[2][9] + F110*ins_state.Pk[2][10] + F111*ins_state.Pk[2][11]) + F312*(ins_state.Pk[1][12] + F19*ins_state.Pk[9][12] + F110*ins_state.Pk[10][12] + F111*ins_state.Pk[11][12]) + F313*(ins_state.Pk[1][13] + F19*ins_state.Pk[9][13] + F110*ins_state.Pk[10][13] + F111*ins_state.Pk[11][13]) + F314*(ins_state.Pk[1][14] + F19*ins_state.Pk[9][14] + F110*ins_state.Pk[10][14] + F111*ins_state.Pk[11][14]) + F19*ins_state.Pk[3][9] + F110*ins_state.Pk[3][10] + F111*ins_state.Pk[3][11];
	nextP[1][4]=ins_state.Pk[1][4] + F40*(ins_state.Pk[0][1] + F19*ins_state.Pk[0][9] + F110*ins_state.Pk[0][10] + F111*ins_state.Pk[0][11]) + F42*(ins_state.Pk[1][2] + F19*ins_state.Pk[2][9] + F110*ins_state.Pk[2][10] + F111*ins_state.Pk[2][11]) + F412*(ins_state.Pk[1][12] + F19*ins_state.Pk[9][12] + F110*ins_state.Pk[10][12] + F111*ins_state.Pk[11][12]) + F413*(ins_state.Pk[1][13] + F19*ins_state.Pk[9][13] + F110*ins_state.Pk[10][13] + F111*ins_state.Pk[11][13]) + F414*(ins_state.Pk[1][14] + F19*ins_state.Pk[9][14] + F110*ins_state.Pk[10][14] + F111*ins_state.Pk[11][14]) + F19*ins_state.Pk[4][9] + F110*ins_state.Pk[4][10] + F111*ins_state.Pk[4][11];
	nextP[1][5]=ins_state.Pk[1][5] + F50*(ins_state.Pk[0][1] + F19*ins_state.Pk[0][9] + F110*ins_state.Pk[0][10] + F111*ins_state.Pk[0][11]) + F51*(ins_state.Pk[1][1] + F19*ins_state.Pk[1][9] + F110*ins_state.Pk[1][10] + F111*ins_state.Pk[1][11]) + F512*(ins_state.Pk[1][12] + F19*ins_state.Pk[9][12] + F110*ins_state.Pk[10][12] + F111*ins_state.Pk[11][12]) + F513*(ins_state.Pk[1][13] + F19*ins_state.Pk[9][13] + F110*ins_state.Pk[10][13] + F111*ins_state.Pk[11][13]) + F514*(ins_state.Pk[1][14] + F19*ins_state.Pk[9][14] + F110*ins_state.Pk[10][14] + F111*ins_state.Pk[11][14]) + F19*ins_state.Pk[5][9] + F110*ins_state.Pk[5][10] + F111*ins_state.Pk[5][11];
	nextP[1][6]=ins_state.Pk[1][6] + F63*(ins_state.Pk[1][3] + F19*ins_state.Pk[3][9] + F110*ins_state.Pk[3][10] + F111*ins_state.Pk[3][11]) + F19*ins_state.Pk[6][9] + F110*ins_state.Pk[6][10] + F111*ins_state.Pk[6][11];
	nextP[1][7]=ins_state.Pk[1][7] + F74*(ins_state.Pk[1][4] + F19*ins_state.Pk[4][9] + F110*ins_state.Pk[4][10] + F111*ins_state.Pk[4][11]) + F19*ins_state.Pk[7][9] + F110*ins_state.Pk[7][10] + F111*ins_state.Pk[7][11];
	nextP[1][8]=ins_state.Pk[1][8] + F85*(ins_state.Pk[1][5] + F19*ins_state.Pk[5][9] + F110*ins_state.Pk[5][10] + F111*ins_state.Pk[5][11]) + F19*ins_state.Pk[8][9] + F110*ins_state.Pk[8][10] + F111*ins_state.Pk[8][11];
	nextP[1][9]=F99*(ins_state.Pk[1][9] + F19*ins_state.Pk[9][9] + F110*ins_state.Pk[9][10] + F111*ins_state.Pk[9][11]);
	nextP[1][10]=F1010*(ins_state.Pk[1][10] + F19*ins_state.Pk[9][10] + F110*ins_state.Pk[10][10] + F111*ins_state.Pk[10][11]);
	nextP[1][11]=F1111*(ins_state.Pk[1][11] + F19*ins_state.Pk[9][11] + F110*ins_state.Pk[10][11] + F111*ins_state.Pk[11][11]);
	nextP[1][12]=F1212*(ins_state.Pk[1][12] + F19*ins_state.Pk[9][12] + F110*ins_state.Pk[10][12] + F111*ins_state.Pk[11][12]);
	nextP[1][13]=F1313*(ins_state.Pk[1][13] + F19*ins_state.Pk[9][13] + F110*ins_state.Pk[10][13] + F111*ins_state.Pk[11][13]);
	nextP[1][14]=F1414*(ins_state.Pk[1][14] + F19*ins_state.Pk[9][14] + F110*ins_state.Pk[10][14] + F111*ins_state.Pk[11][14]);
	nextP[2][2]=ins_state.Pk[2][2] + F29*(ins_state.Pk[2][9] + F29*ins_state.Pk[9][9] + F210*ins_state.Pk[9][10] + F211*ins_state.Pk[9][11]) + F210*(ins_state.Pk[2][10] + F29*ins_state.Pk[9][10] + F210*ins_state.Pk[10][10] + F211*ins_state.Pk[10][11]) + F211*(ins_state.Pk[2][11] + F29*ins_state.Pk[9][11] + F210*ins_state.Pk[10][11] + F211*ins_state.Pk[11][11]) + F29*ins_state.Pk[2][9] + F210*ins_state.Pk[2][10] + F211*ins_state.Pk[2][11];
	nextP[2][3]=ins_state.Pk[2][3] + F31*(ins_state.Pk[1][2] + F29*ins_state.Pk[1][9] + F210*ins_state.Pk[1][10] + F211*ins_state.Pk[1][11]) + F32*(ins_state.Pk[2][2] + F29*ins_state.Pk[2][9] + F210*ins_state.Pk[2][10] + F211*ins_state.Pk[2][11]) + F312*(ins_state.Pk[2][12] + F29*ins_state.Pk[9][12] + F210*ins_state.Pk[10][12] + F211*ins_state.Pk[11][12]) + F313*(ins_state.Pk[2][13] + F29*ins_state.Pk[9][13] + F210*ins_state.Pk[10][13] + F211*ins_state.Pk[11][13]) + F314*(ins_state.Pk[2][14] + F29*ins_state.Pk[9][14] + F210*ins_state.Pk[10][14] + F211*ins_state.Pk[11][14]) + F29*ins_state.Pk[3][9] + F210*ins_state.Pk[3][10] + F211*ins_state.Pk[3][11];
	nextP[2][4]=ins_state.Pk[2][4] + F40*(ins_state.Pk[0][2] + F29*ins_state.Pk[0][9] + F210*ins_state.Pk[0][10] + F211*ins_state.Pk[0][11]) + F42*(ins_state.Pk[2][2] + F29*ins_state.Pk[2][9] + F210*ins_state.Pk[2][10] + F211*ins_state.Pk[2][11]) + F412*(ins_state.Pk[2][12] + F29*ins_state.Pk[9][12] + F210*ins_state.Pk[10][12] + F211*ins_state.Pk[11][12]) + F413*(ins_state.Pk[2][13] + F29*ins_state.Pk[9][13] + F210*ins_state.Pk[10][13] + F211*ins_state.Pk[11][13]) + F414*(ins_state.Pk[2][14] + F29*ins_state.Pk[9][14] + F210*ins_state.Pk[10][14] + F211*ins_state.Pk[11][14]) + F29*ins_state.Pk[4][9] + F210*ins_state.Pk[4][10] + F211*ins_state.Pk[4][11];
	nextP[2][5]=ins_state.Pk[2][5] + F50*(ins_state.Pk[0][2] + F29*ins_state.Pk[0][9] + F210*ins_state.Pk[0][10] + F211*ins_state.Pk[0][11]) + F51*(ins_state.Pk[1][2] + F29*ins_state.Pk[1][9] + F210*ins_state.Pk[1][10] + F211*ins_state.Pk[1][11]) + F512*(ins_state.Pk[2][12] + F29*ins_state.Pk[9][12] + F210*ins_state.Pk[10][12] + F211*ins_state.Pk[11][12]) + F513*(ins_state.Pk[2][13] + F29*ins_state.Pk[9][13] + F210*ins_state.Pk[10][13] + F211*ins_state.Pk[11][13]) + F514*(ins_state.Pk[2][14] + F29*ins_state.Pk[9][14] + F210*ins_state.Pk[10][14] + F211*ins_state.Pk[11][14]) + F29*ins_state.Pk[5][9] + F210*ins_state.Pk[5][10] + F211*ins_state.Pk[5][11];
	nextP[2][6]=ins_state.Pk[2][6] + F63*(ins_state.Pk[2][3] + F29*ins_state.Pk[3][9] + F210*ins_state.Pk[3][10] + F211*ins_state.Pk[3][11]) + F29*ins_state.Pk[6][9] + F210*ins_state.Pk[6][10] + F211*ins_state.Pk[6][11];
	nextP[2][7]=ins_state.Pk[2][7] + F74*(ins_state.Pk[2][4] + F29*ins_state.Pk[4][9] + F210*ins_state.Pk[4][10] + F211*ins_state.Pk[4][11]) + F29*ins_state.Pk[7][9] + F210*ins_state.Pk[7][10] + F211*ins_state.Pk[7][11];
	nextP[2][8]=ins_state.Pk[2][8] + F85*(ins_state.Pk[2][5] + F29*ins_state.Pk[5][9] + F210*ins_state.Pk[5][10] + F211*ins_state.Pk[5][11]) + F29*ins_state.Pk[8][9] + F210*ins_state.Pk[8][10] + F211*ins_state.Pk[8][11];
	nextP[2][9]=F99*(ins_state.Pk[2][9] + F29*ins_state.Pk[9][9] + F210*ins_state.Pk[9][10] + F211*ins_state.Pk[9][11]);
	nextP[2][10]=F1010*(ins_state.Pk[2][10] + F29*ins_state.Pk[9][10] + F210*ins_state.Pk[10][10] + F211*ins_state.Pk[10][11]);
	nextP[2][11]=F1111*(ins_state.Pk[2][11] + F29*ins_state.Pk[9][11] + F210*ins_state.Pk[10][11] + F211*ins_state.Pk[11][11]);
	nextP[2][12]=F1212*(ins_state.Pk[2][12] + F29*ins_state.Pk[9][12] + F210*ins_state.Pk[10][12] + F211*ins_state.Pk[11][12]);
	nextP[2][13]=F1313*(ins_state.Pk[2][13] + F29*ins_state.Pk[9][13] + F210*ins_state.Pk[10][13] + F211*ins_state.Pk[11][13]);
	nextP[2][14]=F1414*(ins_state.Pk[2][14] + F29*ins_state.Pk[9][14] + F210*ins_state.Pk[10][14] + F211*ins_state.Pk[11][14]);
	nextP[3][3]=ins_state.Pk[3][3] + F31*(ins_state.Pk[1][3] + F31*ins_state.Pk[1][1] + F32*ins_state.Pk[1][2] + F312*ins_state.Pk[1][12] + F313*ins_state.Pk[1][13] + F314*ins_state.Pk[1][14]) + F32*(ins_state.Pk[2][3] + F31*ins_state.Pk[1][2] + F32*ins_state.Pk[2][2] + F312*ins_state.Pk[2][12] + F313*ins_state.Pk[2][13] + F314*ins_state.Pk[2][14]) + F312*(ins_state.Pk[3][12] + F31*ins_state.Pk[1][12] + F32*ins_state.Pk[2][12] + F312*ins_state.Pk[12][12] + F313*ins_state.Pk[12][13] + F314*ins_state.Pk[12][14]) + F313*(ins_state.Pk[3][13] + F31*ins_state.Pk[1][13] + F32*ins_state.Pk[2][13] + F312*ins_state.Pk[12][13] + F313*ins_state.Pk[13][13] + F314*ins_state.Pk[13][14]) + F314*(ins_state.Pk[3][14] + F31*ins_state.Pk[1][14] + F32*ins_state.Pk[2][14] + F312*ins_state.Pk[12][14] + F313*ins_state.Pk[13][14] + F314*ins_state.Pk[14][14]) + F31*ins_state.Pk[1][3] + F32*ins_state.Pk[2][3] + F312*ins_state.Pk[3][12] + F313*ins_state.Pk[3][13] + F314*ins_state.Pk[3][14];
	nextP[3][4]=ins_state.Pk[3][4] + F40*(ins_state.Pk[0][3] + F31*ins_state.Pk[0][1] + F32*ins_state.Pk[0][2] + F312*ins_state.Pk[0][12] + F313*ins_state.Pk[0][13] + F314*ins_state.Pk[0][14]) + F42*(ins_state.Pk[2][3] + F31*ins_state.Pk[1][2] + F32*ins_state.Pk[2][2] + F312*ins_state.Pk[2][12] + F313*ins_state.Pk[2][13] + F314*ins_state.Pk[2][14]) + F412*(ins_state.Pk[3][12] + F31*ins_state.Pk[1][12] + F32*ins_state.Pk[2][12] + F312*ins_state.Pk[12][12] + F313*ins_state.Pk[12][13] + F314*ins_state.Pk[12][14]) + F413*(ins_state.Pk[3][13] + F31*ins_state.Pk[1][13] + F32*ins_state.Pk[2][13] + F312*ins_state.Pk[12][13] + F313*ins_state.Pk[13][13] + F314*ins_state.Pk[13][14]) + F414*(ins_state.Pk[3][14] + F31*ins_state.Pk[1][14] + F32*ins_state.Pk[2][14] + F312*ins_state.Pk[12][14] + F313*ins_state.Pk[13][14] + F314*ins_state.Pk[14][14]) + F31*ins_state.Pk[1][4] + F32*ins_state.Pk[2][4] + F312*ins_state.Pk[4][12] + F313*ins_state.Pk[4][13] + F314*ins_state.Pk[4][14];
	nextP[3][5]=ins_state.Pk[3][5] + F50*(ins_state.Pk[0][3] + F31*ins_state.Pk[0][1] + F32*ins_state.Pk[0][2] + F312*ins_state.Pk[0][12] + F313*ins_state.Pk[0][13] + F314*ins_state.Pk[0][14]) + F51*(ins_state.Pk[1][3] + F31*ins_state.Pk[1][1] + F32*ins_state.Pk[1][2] + F312*ins_state.Pk[1][12] + F313*ins_state.Pk[1][13] + F314*ins_state.Pk[1][14]) + F512*(ins_state.Pk[3][12] + F31*ins_state.Pk[1][12] + F32*ins_state.Pk[2][12] + F312*ins_state.Pk[12][12] + F313*ins_state.Pk[12][13] + F314*ins_state.Pk[12][14]) + F513*(ins_state.Pk[3][13] + F31*ins_state.Pk[1][13] + F32*ins_state.Pk[2][13] + F312*ins_state.Pk[12][13] + F313*ins_state.Pk[13][13] + F314*ins_state.Pk[13][14]) + F514*(ins_state.Pk[3][14] + F31*ins_state.Pk[1][14] + F32*ins_state.Pk[2][14] + F312*ins_state.Pk[12][14] + F313*ins_state.Pk[13][14] + F314*ins_state.Pk[14][14]) + F31*ins_state.Pk[1][5] + F32*ins_state.Pk[2][5] + F312*ins_state.Pk[5][12] + F313*ins_state.Pk[5][13] + F314*ins_state.Pk[5][14];
	nextP[3][6]=ins_state.Pk[3][6] + F63*(ins_state.Pk[3][3] + F31*ins_state.Pk[1][3] + F32*ins_state.Pk[2][3] + F312*ins_state.Pk[3][12] + F313*ins_state.Pk[3][13] + F314*ins_state.Pk[3][14]) + F31*ins_state.Pk[1][6] + F32*ins_state.Pk[2][6] + F312*ins_state.Pk[6][12] + F313*ins_state.Pk[6][13] + F314*ins_state.Pk[6][14];
	nextP[3][7]=ins_state.Pk[3][7] + F74*(ins_state.Pk[3][4] + F31*ins_state.Pk[1][4] + F32*ins_state.Pk[2][4] + F312*ins_state.Pk[4][12] + F313*ins_state.Pk[4][13] + F314*ins_state.Pk[4][14]) + F31*ins_state.Pk[1][7] + F32*ins_state.Pk[2][7] + F312*ins_state.Pk[7][12] + F313*ins_state.Pk[7][13] + F314*ins_state.Pk[7][14];
	nextP[3][8]=ins_state.Pk[3][8] + F85*(ins_state.Pk[3][5] + F31*ins_state.Pk[1][5] + F32*ins_state.Pk[2][5] + F312*ins_state.Pk[5][12] + F313*ins_state.Pk[5][13] + F314*ins_state.Pk[5][14]) + F31*ins_state.Pk[1][8] + F32*ins_state.Pk[2][8] + F312*ins_state.Pk[8][12] + F313*ins_state.Pk[8][13] + F314*ins_state.Pk[8][14];
	nextP[3][9]=F99*(ins_state.Pk[3][9] + F31*ins_state.Pk[1][9] + F32*ins_state.Pk[2][9] + F312*ins_state.Pk[9][12] + F313*ins_state.Pk[9][13] + F314*ins_state.Pk[9][14]);
	nextP[3][10]=F1010*(ins_state.Pk[3][10] + F31*ins_state.Pk[1][10] + F32*ins_state.Pk[2][10] + F312*ins_state.Pk[10][12] + F313*ins_state.Pk[10][13] + F314*ins_state.Pk[10][14]);
	nextP[3][11]=F1111*(ins_state.Pk[3][11] + F31*ins_state.Pk[1][11] + F32*ins_state.Pk[2][11] + F312*ins_state.Pk[11][12] + F313*ins_state.Pk[11][13] + F314*ins_state.Pk[11][14]);
	nextP[3][12]=F1212*(ins_state.Pk[3][12] + F31*ins_state.Pk[1][12] + F32*ins_state.Pk[2][12] + F312*ins_state.Pk[12][12] + F313*ins_state.Pk[12][13] + F314*ins_state.Pk[12][14]);
	nextP[3][13]=F1313*(ins_state.Pk[3][13] + F31*ins_state.Pk[1][13] + F32*ins_state.Pk[2][13] + F312*ins_state.Pk[12][13] + F313*ins_state.Pk[13][13] + F314*ins_state.Pk[13][14]);
	nextP[3][14]=F1414*(ins_state.Pk[3][14] + F31*ins_state.Pk[1][14] + F32*ins_state.Pk[2][14] + F312*ins_state.Pk[12][14] + F313*ins_state.Pk[13][14] + F314*ins_state.Pk[14][14]);
	nextP[4][4]=ins_state.Pk[4][4] + F40*(ins_state.Pk[0][4] + F40*ins_state.Pk[0][0] + F42*ins_state.Pk[0][2] + F412*ins_state.Pk[0][12] + F413*ins_state.Pk[0][13] + F414*ins_state.Pk[0][14]) + F42*(ins_state.Pk[2][4] + F40*ins_state.Pk[0][2] + F42*ins_state.Pk[2][2] + F412*ins_state.Pk[2][12] + F413*ins_state.Pk[2][13] + F414*ins_state.Pk[2][14]) + F412*(ins_state.Pk[4][12] + F40*ins_state.Pk[0][12] + F42*ins_state.Pk[2][12] + F412*ins_state.Pk[12][12] + F413*ins_state.Pk[12][13] + F414*ins_state.Pk[12][14]) + F413*(ins_state.Pk[4][13] + F40*ins_state.Pk[0][13] + F42*ins_state.Pk[2][13] + F412*ins_state.Pk[12][13] + F413*ins_state.Pk[13][13] + F414*ins_state.Pk[13][14]) + F414*(ins_state.Pk[4][14] + F40*ins_state.Pk[0][14] + F42*ins_state.Pk[2][14] + F412*ins_state.Pk[12][14] + F413*ins_state.Pk[13][14] + F414*ins_state.Pk[14][14]) + F40*ins_state.Pk[0][4] + F42*ins_state.Pk[2][4] + F412*ins_state.Pk[4][12] + F413*ins_state.Pk[4][13] + F414*ins_state.Pk[4][14];
	nextP[4][5]=ins_state.Pk[4][5] + F50*(ins_state.Pk[0][4] + F40*ins_state.Pk[0][0] + F42*ins_state.Pk[0][2] + F412*ins_state.Pk[0][12] + F413*ins_state.Pk[0][13] + F414*ins_state.Pk[0][14]) + F51*(ins_state.Pk[1][4] + F40*ins_state.Pk[0][1] + F42*ins_state.Pk[1][2] + F412*ins_state.Pk[1][12] + F413*ins_state.Pk[1][13] + F414*ins_state.Pk[1][14]) + F512*(ins_state.Pk[4][12] + F40*ins_state.Pk[0][12] + F42*ins_state.Pk[2][12] + F412*ins_state.Pk[12][12] + F413*ins_state.Pk[12][13] + F414*ins_state.Pk[12][14]) + F513*(ins_state.Pk[4][13] + F40*ins_state.Pk[0][13] + F42*ins_state.Pk[2][13] + F412*ins_state.Pk[12][13] + F413*ins_state.Pk[13][13] + F414*ins_state.Pk[13][14]) + F514*(ins_state.Pk[4][14] + F40*ins_state.Pk[0][14] + F42*ins_state.Pk[2][14] + F412*ins_state.Pk[12][14] + F413*ins_state.Pk[13][14] + F414*ins_state.Pk[14][14]) + F40*ins_state.Pk[0][5] + F42*ins_state.Pk[2][5] + F412*ins_state.Pk[5][12] + F413*ins_state.Pk[5][13] + F414*ins_state.Pk[5][14];
	nextP[4][6]=ins_state.Pk[4][6] + F63*(ins_state.Pk[3][4] + F40*ins_state.Pk[0][3] + F42*ins_state.Pk[2][3] + F412*ins_state.Pk[3][12] + F413*ins_state.Pk[3][13] + F414*ins_state.Pk[3][14]) + F40*ins_state.Pk[0][6] + F42*ins_state.Pk[2][6] + F412*ins_state.Pk[6][12] + F413*ins_state.Pk[6][13] + F414*ins_state.Pk[6][14];
	nextP[4][7]=ins_state.Pk[4][7] + F74*(ins_state.Pk[4][4] + F40*ins_state.Pk[0][4] + F42*ins_state.Pk[2][4] + F412*ins_state.Pk[4][12] + F413*ins_state.Pk[4][13] + F414*ins_state.Pk[4][14]) + F40*ins_state.Pk[0][7] + F42*ins_state.Pk[2][7] + F412*ins_state.Pk[7][12] + F413*ins_state.Pk[7][13] + F414*ins_state.Pk[7][14];
	nextP[4][8]=ins_state.Pk[4][8] + F85*(ins_state.Pk[4][5] + F40*ins_state.Pk[0][5] + F42*ins_state.Pk[2][5] + F412*ins_state.Pk[5][12] + F413*ins_state.Pk[5][13] + F414*ins_state.Pk[5][14]) + F40*ins_state.Pk[0][8] + F42*ins_state.Pk[2][8] + F412*ins_state.Pk[8][12] + F413*ins_state.Pk[8][13] + F414*ins_state.Pk[8][14];
	nextP[4][9]=F99*(ins_state.Pk[4][9] + F40*ins_state.Pk[0][9] + F42*ins_state.Pk[2][9] + F412*ins_state.Pk[9][12] + F413*ins_state.Pk[9][13] + F414*ins_state.Pk[9][14]);
	nextP[4][10]=F1010*(ins_state.Pk[4][10] + F40*ins_state.Pk[0][10] + F42*ins_state.Pk[2][10] + F412*ins_state.Pk[10][12] + F413*ins_state.Pk[10][13] + F414*ins_state.Pk[10][14]);
	nextP[4][11]=F1111*(ins_state.Pk[4][11] + F40*ins_state.Pk[0][11] + F42*ins_state.Pk[2][11] + F412*ins_state.Pk[11][12] + F413*ins_state.Pk[11][13] + F414*ins_state.Pk[11][14]);
	nextP[4][12]=F1212*(ins_state.Pk[4][12] + F40*ins_state.Pk[0][12] + F42*ins_state.Pk[2][12] + F412*ins_state.Pk[12][12] + F413*ins_state.Pk[12][13] + F414*ins_state.Pk[12][14]);
	nextP[4][13]=F1313*(ins_state.Pk[4][13] + F40*ins_state.Pk[0][13] + F42*ins_state.Pk[2][13] + F412*ins_state.Pk[12][13] + F413*ins_state.Pk[13][13] + F414*ins_state.Pk[13][14]);
	nextP[4][14]=F1414*(ins_state.Pk[4][14] + F40*ins_state.Pk[0][14] + F42*ins_state.Pk[2][14] + F412*ins_state.Pk[12][14] + F413*ins_state.Pk[13][14] + F414*ins_state.Pk[14][14]);
	nextP[5][5]=ins_state.Pk[5][5] + F50*(ins_state.Pk[0][5] + F50*ins_state.Pk[0][0] + F51*ins_state.Pk[0][1] + F512*ins_state.Pk[0][12] + F513*ins_state.Pk[0][13] + F514*ins_state.Pk[0][14]) + F51*(ins_state.Pk[1][5] + F50*ins_state.Pk[0][1] + F51*ins_state.Pk[1][1] + F512*ins_state.Pk[1][12] + F513*ins_state.Pk[1][13] + F514*ins_state.Pk[1][14]) + F512*(ins_state.Pk[5][12] + F50*ins_state.Pk[0][12] + F51*ins_state.Pk[1][12] + F512*ins_state.Pk[12][12] + F513*ins_state.Pk[12][13] + F514*ins_state.Pk[12][14]) + F513*(ins_state.Pk[5][13] + F50*ins_state.Pk[0][13] + F51*ins_state.Pk[1][13] + F512*ins_state.Pk[12][13] + F513*ins_state.Pk[13][13] + F514*ins_state.Pk[13][14]) + F514*(ins_state.Pk[5][14] + F50*ins_state.Pk[0][14] + F51*ins_state.Pk[1][14] + F512*ins_state.Pk[12][14] + F513*ins_state.Pk[13][14] + F514*ins_state.Pk[14][14]) + F50*ins_state.Pk[0][5] + F51*ins_state.Pk[1][5] + F512*ins_state.Pk[5][12] + F513*ins_state.Pk[5][13] + F514*ins_state.Pk[5][14];
	nextP[5][6]=ins_state.Pk[5][6] + F63*(ins_state.Pk[3][5] + F50*ins_state.Pk[0][3] + F51*ins_state.Pk[1][3] + F512*ins_state.Pk[3][12] + F513*ins_state.Pk[3][13] + F514*ins_state.Pk[3][14]) + F50*ins_state.Pk[0][6] + F51*ins_state.Pk[1][6] + F512*ins_state.Pk[6][12] + F513*ins_state.Pk[6][13] + F514*ins_state.Pk[6][14];
	nextP[5][7]=ins_state.Pk[5][7] + F74*(ins_state.Pk[4][5] + F50*ins_state.Pk[0][4] + F51*ins_state.Pk[1][4] + F512*ins_state.Pk[4][12] + F513*ins_state.Pk[4][13] + F514*ins_state.Pk[4][14]) + F50*ins_state.Pk[0][7] + F51*ins_state.Pk[1][7] + F512*ins_state.Pk[7][12] + F513*ins_state.Pk[7][13] + F514*ins_state.Pk[7][14];
	nextP[5][8]=ins_state.Pk[5][8] + F85*(ins_state.Pk[5][5] + F50*ins_state.Pk[0][5] + F51*ins_state.Pk[1][5] + F512*ins_state.Pk[5][12] + F513*ins_state.Pk[5][13] + F514*ins_state.Pk[5][14]) + F50*ins_state.Pk[0][8] + F51*ins_state.Pk[1][8] + F512*ins_state.Pk[8][12] + F513*ins_state.Pk[8][13] + F514*ins_state.Pk[8][14];
	nextP[5][9]=F99*(ins_state.Pk[5][9] + F50*ins_state.Pk[0][9] + F51*ins_state.Pk[1][9] + F512*ins_state.Pk[9][12] + F513*ins_state.Pk[9][13] + F514*ins_state.Pk[9][14]);
	nextP[5][10]=F1010*(ins_state.Pk[5][10] + F50*ins_state.Pk[0][10] + F51*ins_state.Pk[1][10] + F512*ins_state.Pk[10][12] + F513*ins_state.Pk[10][13] + F514*ins_state.Pk[10][14]);
	nextP[5][11]=F1111*(ins_state.Pk[5][11] + F50*ins_state.Pk[0][11] + F51*ins_state.Pk[1][11] + F512*ins_state.Pk[11][12] + F513*ins_state.Pk[11][13] + F514*ins_state.Pk[11][14]);
	nextP[5][12]=F1212*(ins_state.Pk[5][12] + F50*ins_state.Pk[0][12] + F51*ins_state.Pk[1][12] + F512*ins_state.Pk[12][12] + F513*ins_state.Pk[12][13] + F514*ins_state.Pk[12][14]);
	nextP[5][13]=F1313*(ins_state.Pk[5][13] + F50*ins_state.Pk[0][13] + F51*ins_state.Pk[1][13] + F512*ins_state.Pk[12][13] + F513*ins_state.Pk[13][13] + F514*ins_state.Pk[13][14]);
	nextP[5][14]=F1414*(ins_state.Pk[5][14] + F50*ins_state.Pk[0][14] + F51*ins_state.Pk[1][14] + F512*ins_state.Pk[12][14] + F513*ins_state.Pk[13][14] + F514*ins_state.Pk[14][14]);
	nextP[6][6]=ins_state.Pk[6][6] + F63*ins_state.Pk[3][6] + F63*(ins_state.Pk[3][6] + F63*ins_state.Pk[3][3]);
	nextP[6][7]=ins_state.Pk[6][7] + F63*ins_state.Pk[3][7] + F74*(ins_state.Pk[4][6] + F63*ins_state.Pk[3][4]);
	nextP[6][8]=ins_state.Pk[6][8] + F63*ins_state.Pk[3][8] + F85*(ins_state.Pk[5][6] + F63*ins_state.Pk[3][5]);
	nextP[6][9]=F99*(ins_state.Pk[6][9] + F63*ins_state.Pk[3][9]);
	nextP[6][10]=F1010*(ins_state.Pk[6][10] + F63*ins_state.Pk[3][10]);
	nextP[6][11]=F1111*(ins_state.Pk[6][11] + F63*ins_state.Pk[3][11]);
	nextP[6][12]=F1212*(ins_state.Pk[6][12] + F63*ins_state.Pk[3][12]);
	nextP[6][13]=F1313*(ins_state.Pk[6][13] + F63*ins_state.Pk[3][13]);
	nextP[6][14]=F1414*(ins_state.Pk[6][14] + F63*ins_state.Pk[3][14]);
	nextP[7][7]=ins_state.Pk[7][7] + F74*ins_state.Pk[4][7] + F74*(ins_state.Pk[4][7] + F74*ins_state.Pk[4][4]);
	nextP[7][8]=ins_state.Pk[7][8] + F74*ins_state.Pk[4][8] + F85*(ins_state.Pk[5][7] + F74*ins_state.Pk[4][5]);
	nextP[7][9]=F99*(ins_state.Pk[7][9] + F74*ins_state.Pk[4][9]);
	nextP[7][10]=F1010*(ins_state.Pk[7][10] + F74*ins_state.Pk[4][10]);
	nextP[7][11]=F1111*(ins_state.Pk[7][11] + F74*ins_state.Pk[4][11]);
	nextP[7][12]=F1212*(ins_state.Pk[7][12] + F74*ins_state.Pk[4][12]);
	nextP[7][13]=F1313*(ins_state.Pk[7][13] + F74*ins_state.Pk[4][13]);
	nextP[7][14]=F1414*(ins_state.Pk[7][14] + F74*ins_state.Pk[4][14]);
	nextP[8][8]=ins_state.Pk[8][8] + F85*ins_state.Pk[5][8] + F85*(ins_state.Pk[5][8] + F85*ins_state.Pk[5][5]);
	nextP[8][9]=F99*(ins_state.Pk[8][9] + F85*ins_state.Pk[5][9]);
	nextP[8][10]=F1010*(ins_state.Pk[8][10] + F85*ins_state.Pk[5][10]);
	nextP[8][11]=F1111*(ins_state.Pk[8][11] + F85*ins_state.Pk[5][11]);
	nextP[8][12]=F1212*(ins_state.Pk[8][12] + F85*ins_state.Pk[5][12]);
	nextP[8][13]=F1313*(ins_state.Pk[8][13] + F85*ins_state.Pk[5][13]);
	nextP[8][14]=F1414*(ins_state.Pk[8][14] + F85*ins_state.Pk[5][14]);
	nextP[9][9]=F99*F99*ins_state.Pk[9][9];
	nextP[9][10]=F99*F1010*ins_state.Pk[9][10];
	nextP[9][11]=F99*F1111*ins_state.Pk[9][11];
	nextP[9][12]=F99*F1212*ins_state.Pk[9][12];
	nextP[9][13]=F99*F1313*ins_state.Pk[9][13];
	nextP[9][14]=F99*F1414*ins_state.Pk[9][14];
	nextP[10][10]=F1010*F1010*ins_state.Pk[10][10];
	nextP[10][11]=F1010*F1111*ins_state.Pk[10][11];
	nextP[10][12]=F1010*F1212*ins_state.Pk[10][12];
	nextP[10][13]=F1010*F1313*ins_state.Pk[10][13];
	nextP[10][14]=F1010*F1414*ins_state.Pk[10][14];
	nextP[11][11]=F1111*F1111*ins_state.Pk[11][11];
	nextP[11][12]=F1111*F1212*ins_state.Pk[11][12];
	nextP[11][13]=F1111*F1313*ins_state.Pk[11][13];
	nextP[11][14]=F1111*F1414*ins_state.Pk[11][14];
	nextP[12][12]=F1212*F1212*ins_state.Pk[12][12];
	nextP[12][13]=F1212*F1313*ins_state.Pk[12][13];
	nextP[12][14]=F1212*F1414*ins_state.Pk[12][14];
	nextP[13][13]=F1313*F1313*ins_state.Pk[13][13];
	nextP[13][14]=F1313*F1414*ins_state.Pk[13][14];
	nextP[14][14]=F1414*F1414*ins_state.Pk[14][14];



     //������ֱ�ӿ���
     //   for(i=0;i<MAX_STATE_DIM;i++)//����������
     //       for(j=0;j<=i-1;j++)nextP[i][j]=nextP[j][i];

    nextP[1][0]=nextP[0][1];
    nextP[2][0]=nextP[0][2];nextP[2][1]=nextP[1][2];
    nextP[3][0]=nextP[0][3];nextP[3][1]=nextP[1][3];nextP[3][2]=nextP[2][3];
    nextP[4][0]=nextP[0][4];nextP[4][1]=nextP[1][4];nextP[4][2]=nextP[2][4];nextP[4][3]=nextP[3][4];
    nextP[5][0]=nextP[0][5];nextP[5][1]=nextP[1][5];nextP[5][2]=nextP[2][5];nextP[5][3]=nextP[3][5];nextP[5][4]=nextP[4][5];
    nextP[6][0]=nextP[0][6];nextP[6][1]=nextP[1][6];nextP[6][2]=nextP[2][6];nextP[6][3]=nextP[3][6];nextP[6][4]=nextP[4][6];nextP[6][5]=nextP[5][6];
    nextP[7][0]=nextP[0][7];nextP[7][1]=nextP[1][7];nextP[7][2]=nextP[2][7];nextP[7][3]=nextP[3][7];nextP[7][4]=nextP[4][7];nextP[7][5]=nextP[5][7];nextP[7][6]=nextP[6][7];
    nextP[8][0]=nextP[0][8];nextP[8][1]=nextP[1][8];nextP[8][2]=nextP[2][8];nextP[8][3]=nextP[3][8];nextP[8][4]=nextP[4][8];nextP[8][5]=nextP[5][8];nextP[8][6]=nextP[6][8];nextP[8][7]=nextP[7][8];
    nextP[9][0]=nextP[0][9];nextP[9][1]=nextP[1][9];nextP[9][2]=nextP[2][9];nextP[9][3]=nextP[3][9];nextP[9][4]=nextP[4][9];nextP[9][5]=nextP[5][9];nextP[9][6]=nextP[6][9];nextP[9][7]=nextP[7][9];nextP[9][8]=nextP[8][9];
    nextP[10][0]=nextP[0][10];nextP[10][1]=nextP[1][10];nextP[10][2]=nextP[2][10];nextP[10][3]=nextP[3][10];nextP[10][4]=nextP[4][10];nextP[10][5]=nextP[5][10];nextP[10][6]=nextP[6][10];nextP[10][7]=nextP[7][10];nextP[10][8]=nextP[8][10];nextP[10][9]=nextP[9][10];
    nextP[11][0]=nextP[0][11];nextP[11][1]=nextP[1][11];nextP[11][2]=nextP[2][11];nextP[11][3]=nextP[3][11];nextP[11][4]=nextP[4][11];nextP[11][5]=nextP[5][11];nextP[11][6]=nextP[6][11];nextP[11][7]=nextP[7][11];nextP[11][8]=nextP[8][11];nextP[11][9]=nextP[9][11];nextP[11][10]=nextP[10][11];
    nextP[12][0]=nextP[0][12];nextP[12][1]=nextP[1][12];nextP[12][2]=nextP[2][12];nextP[12][3]=nextP[3][12];nextP[12][4]=nextP[4][12];nextP[12][5]=nextP[5][12];nextP[12][6]=nextP[6][12];nextP[12][7]=nextP[7][12];nextP[12][8]=nextP[8][12];nextP[12][9]=nextP[9][12];nextP[12][10]=nextP[10][12];nextP[12][11]=nextP[11][12];
    nextP[13][0]=nextP[0][13];nextP[13][1]=nextP[1][13];nextP[13][2]=nextP[2][13];nextP[13][3]=nextP[3][13];nextP[13][4]=nextP[4][13];nextP[13][5]=nextP[5][13];nextP[13][6]=nextP[6][13];nextP[13][7]=nextP[7][13];nextP[13][8]=nextP[8][13];nextP[13][9]=nextP[9][13];nextP[13][10]=nextP[10][13];nextP[13][11]=nextP[11][13];nextP[13][12]=nextP[12][13];
    nextP[14][0]=nextP[0][14];nextP[14][1]=nextP[1][14];nextP[14][2]=nextP[2][14];nextP[14][3]=nextP[3][14];nextP[14][4]=nextP[4][14];nextP[14][5]=nextP[5][14];nextP[14][6]=nextP[6][14];nextP[14][7]=nextP[7][14];nextP[14][8]=nextP[8][14];nextP[14][9]=nextP[9][14];nextP[14][10]=nextP[10][14];nextP[14][11]=nextP[11][14];nextP[14][12]=nextP[12][14];nextP[14][13]=nextP[13][14];

     //---------------------���ϼ�����F*P*F'+G*Q*G'-------------------------------------------------

     //ˮƽ����ʧ׼�ǵķ����С��Ϊ��б��׼���
     if(ins_state.Pk[PHIN_STATE_IDX][PHIN_STATE_IDX] < 3E-5 && ins_state.Pk[PHIE_STATE_IDX][PHIE_STATE_IDX] < 3E-5)
     {
         ins_state.tilt_alig_complete = true;
     }
     else
     {
    	 ins_state.tilt_alig_complete = false;
     }

     //��������ķ����С������Ϊ�����׼���
     if(ins_state.Pk[PHID_STATE_IDX][PHID_STATE_IDX] <= 1.5E-6)
     {
    	ins_state.yaw_alig_complete = true;
     }
     else
     {
    	ins_state.yaw_alig_complete = false;
     }

    //strong track kf
    if(true == STEKF_IS_RUNNING || stekf_feed_count<500)
    {
        stekf_feed_count = stekf_feed_count+1;//��¼���򲹳�����

        //��ȷ������½�
        for(i=0;i<MAX_STATE_DIM;i++)nextP[PHID_STATE_IDX][i] = ins_state.Pk[PHID_STATE_IDX][i]*1.0001;

        for(i=0;i<MAX_STATE_DIM;i++)nextP[i][PHID_STATE_IDX] = ins_state.Pk[i][PHID_STATE_IDX]*1.0001;

        if(stekf_feed_count >= 300)STEKF_IS_RUNNING = false;//300*dt=3�����ϵĲ�����ʶ�������
    }

    //���򾫶�׼��ɲ���ζ�����еĳ�ʼ��׼���
    if(true == ins_state.yaw_alig_complete)
    {
        for(i = PHIN_STATE_IDX;i<=ABZ_STATE_IDX;i++)      
            for(j = PHIN_STATE_IDX;j<=ABZ_STATE_IDX;j++)ins_state.Pk[i][j] = nextP[i][j];
        
        //���ں��򽫲���ǿ����
        if(ins_state.Pk[PHID_STATE_IDX][PHID_STATE_IDX] < 1e-6) 
        {
            ins_state.Pk[PHID_STATE_IDX][PHID_STATE_IDX] = 1.0004*ins_state.Pk[PHID_STATE_IDX][PHID_STATE_IDX]; //�ʵ��Ŵ���ķ����ֹ���򸺶�
        }

        if(ins_state.Pk[PHIN_STATE_IDX][PHIN_STATE_IDX] < 6.0e-6)
        {
            for(i = PHIN_STATE_IDX;i<=PHIE_STATE_IDX;i++)ins_state.Pk[i][i] = 1.0001*ins_state.Pk[i][i]; //�ʵ��Ŵ�ˮƽʧ׼�ǵķ�����ֻ���
           
            for(i = GBX_STATE_IDX; i<=GBZ_STATE_IDX;i++)ins_state.Pk[i][i] = 1.00002*ins_state.Pk[i][i]; //�ʵ��Ŵ�����0ƫ�ķ�����ֻ���
            
            for(i = ABX_STATE_IDX; i<=ABZ_STATE_IDX;i++)ins_state.Pk[i][i] = 1.00002*ins_state.Pk[i][i];//�ʵ��Ŵ�Ӽ�0ƫ�ķ���
        }

        //����ֻ�ǽ�һ�������ɷ���������ӵ�Pkk-1�Խ���Ԫ����
        for(i = PHIN_STATE_IDX;i<=ABZ_STATE_IDX;i++)ins_state.Pk[i][i] = ins_state.Pk[i][i] + GQG[i];

    }
    else//�����׼δ���,GNSS�ٶȹ۲⾫�Ȳ���,���Թ��Ƽӱ�0ƫ
    {
         for(i = PHIN_STATE_IDX;i<=GBZ_STATE_IDX;i++)//Pk_k_1������PK���ں�������ᴦ��      
            for(j = PHIN_STATE_IDX;j<=GBZ_STATE_IDX;j++)ins_state.Pk[i][j] = nextP[i][j];

         for(i=ABX_STATE_IDX;i<=ABZ_STATE_IDX;i++)//�ӱ�0ƫ��Ӧ���к�����0
            for(j=PHIN_STATE_IDX;j<=ABZ_STATE_IDX;j++)ins_state.Pk[i][j] = 0.0;


        
        for(i=PHIN_STATE_IDX;i<=ABZ_STATE_IDX;i++)//�ӱ�0ƫ��Ӧ���к�����0
           for(j=ABX_STATE_IDX;j<=ABZ_STATE_IDX;j++)ins_state.Pk[i][j] = 0.0;

         //����ֻ�ǽ�һ�������ɷ���������ӵ�Pkk-1�Խ���Ԫ����
         for(i = PHIN_STATE_IDX;i<=ABZ_STATE_IDX;i++)ins_state.Pk[i][i] = ins_state.Pk[i][i] + GQG[i];
    }

     
    //ǿ��ʹ��ԳƷ�ֹ��̬�����µ��˲���ɢ
    for(i=PHIN_STATE_IDX;i<=ABZ_STATE_IDX;i++)
    {
        for(j=0;j<=i-1;j++)
        {
            temp = 0.5*(ins_state.Pk[i][j] + ins_state.Pk[j][i]); //ǿ��ʹ��Գ�
            ins_state.Pk[i][j] = temp;
            ins_state.Pk[j][i] = temp;
        }
    }

    //����ֻ��Ҫȷ��Pk_k_1�Խ����ϵ�Ԫ���Ǵ��ڵ���0�ļ���
    for(i=PHIN_STATE_IDX;i<=ABZ_STATE_IDX;i++)
    {
    	if(ins_state.Pk[i][i] < 0)ins_state.Pk[i][i] = 0;	
    }
}

double sq (double a) // for outputconvice, ���Ч������,��sqȫ����Ϊƽ��
{
	double b;
	b = a*a;
	return b;
}

void sins_pk_update3(SENS_RAW_ST sins_raw)
{
    	 /*************mrluo����Ϊ�����ϵͳ��״̬���º�״̬һ��Ԥ������Э�������,matlab��ǰ�Ż���*******************/
	// corvance update
	// intial the parameter
	ftype dt = 1.0/200.0;//mrluo������ʱ����²���
	ftype pi = 3.1415926535897932;//mrluo���õĶȺͻ���ת������Ҫ��PI����

	   //mrluo����=��׼���ƽ��
	   //mrluo���ݽ�����0ƫ��������������ģΪһ�������Ʒ���ܶ�Ӧ0ƫ���ȶ��ȣ�����Ľ��������������ı�׼����ڼ�����������ķ���ǿ��Q
	ftype dAngBiasSigma = 6E-6;

	   //mrluo�Ӽ�0ƫ��������������ģΪһ�������Ʒ���ܶ�Ӧ0ƫ���ȶ��ȣ�������ٶ��������������ı�׼����ڼ�����������ķ���ǿ��Q
	ftype daccBiasSigma = 7E-5;

	   //mrluo���ݽ�����0ƫ�п�������������������������ķ��������ݽ�����0ƫ�еĸ�˹���������µģ���Ӧ�Ƕ�������ߣ����ڼ�����������ķ���ǿ��Q
	   //mrluo��ȫ��ת��Ϊ����
	ftype daxNoise = (dt*1.5*pi/180)*(dt*2*pi/180);//���ݽ����ʹ۲�����ת�������Ĺ۲�����
	ftype dayNoise = (dt*1.5*pi/180)*(dt*2*pi/180);
	ftype dazNoise = (dt*5*pi/180)*(dt*5*pi/180);//float dazNoise = (dt*4*pi/180)*(dt*3*pi/180);

	   //mrluo�ٶ�����������������ķ����ɼӼƼ��ٶ�0ƫ�еĸ�˹���������µģ���Ӧ�ٶ�������ߣ����ڼ�����������ķ���ǿ��Q
	ftype dvxNoise = (dt*2)*(dt*2);//�ӱ����ٶȵĹ۲�����ת�ٶȽ������Ĺ۲�����
	ftype dvyNoise = (dt*2)*(dt*2);
	ftype dvzNoise = (dt*2)*(dt*2);

	ftype dvx = ins_state.deltVk.x;//�ٶ�����
	ftype dvy = ins_state.deltVk.y;
	ftype dvz = ins_state.deltVk.z;

	ftype nav_q0 = ins_state.q0;//���ڱ�ʾ��ǰ��̬�ĵ�λ��Ԫ��
	ftype nav_q1 = ins_state.q1;
	ftype nav_q2 = ins_state.q2;
	ftype nav_q3 = ins_state.q3;

	ftype acc_bias_time = 0;//mrluo һ�������Ʒ�ģ���е�ʱ�����ϵ��
	ftype gyro_bias_time = 0;

    //״̬ת�ƾ���F=I+F*dt��̬���ּ�Cbn���֣�������matlab�����õ�
    //    	    |q0*q0+q1*q1-q2*q2-q3*q3   2*(q1*q2-q0*q3)             2*(q1*q3+q0*q2)        |
    //    	    |                                                                             |
    //  Cbn  =  |2*(q1*q2+q0*q3)           q0*q0-q1*q1+q2*q2-q3*q3     2*(q2*q3-q0*q1)        |
    //    	    |                                                                             |
    //    	    |2*(q1*q3-q0*q2)           2*(q2*q3+q0*q1)             q0*q0-q1*q1-q2*q2+q3*q3|
	ftype SF[11];//���ڼ���F*Pk_1*F'
	   SF[0] = sq(nav_q0) + sq(nav_q1) - sq(nav_q2) - sq(nav_q3);//Cbn[0][0]
	   SF[1] = sq(nav_q0) - sq(nav_q1) + sq(nav_q2) - sq(nav_q3);//Cbn[1][1]
	   SF[2] = sq(nav_q0) - sq(nav_q1) - sq(nav_q2) + sq(nav_q3);//Cbn[2][2]
	   SF[3] = 2*nav_q0*nav_q2 - 2*nav_q1*nav_q3;//-Cbn[2][0]
	   SF[4] = 2*nav_q0*nav_q2 + 2*nav_q1*nav_q3;// Cbn[0][2]
	   SF[5] = 2*nav_q0*nav_q3 + 2*nav_q1*nav_q2;// Cbn[1][0]
	   SF[6] = 2*nav_q0*nav_q3 - 2*nav_q1*nav_q2;//-Cbn[0][1]
	   SF[7] = 2*nav_q0*nav_q1 + 2*nav_q2*nav_q3;// Cbn[2][1]
	   SF[8] = 2*nav_q0*nav_q1 - 2*nav_q2*nav_q3;//-Cbn[1][2]

	   SF[9] = acc_bias_time*dt + 1;//һ�������ɷ�ʱ�����ϵ������
	   SF[10] = dt*gyro_bias_time + 1;

	   //���ݼӱ��Ĺ۲�����ת��NEDϵ
	   ftype SQ[24];//�ü���GQG=G*Q*G'��������
	   SQ[0] = sq(nav_q0) - sq(nav_q1) - sq(nav_q2) + sq(nav_q3);//Cbn[2][2]
	   SQ[1] = sq(nav_q0) - sq(nav_q1) + sq(nav_q2) - sq(nav_q3);//Cbn[1][1]
	   SQ[2] = sq(nav_q0) + sq(nav_q1) - sq(nav_q2) - sq(nav_q3);//Cbn[0][0]
	   SQ[3] = 2*nav_q0*nav_q2 - 2*nav_q1*nav_q3;//-Cbn[2][0]
	   SQ[4] = 2*nav_q0*nav_q3 - 2*nav_q1*nav_q2;//-Cbn[0][1]
	   SQ[5] = 2*nav_q0*nav_q1 + 2*nav_q2*nav_q3;// Cbn[2][1]
	   SQ[6] = 2*nav_q0*nav_q1 - 2*nav_q2*nav_q3;//-Cbn[1][2]
	   SQ[7] = 2*nav_q0*nav_q3 + 2*nav_q1*nav_q2;// Cbn[1][0]
	   SQ[8] = 2*nav_q0*nav_q2 + 2*nav_q1*nav_q3;// Cbn[0][2]

	   //����GQG=G*Q*G'
	   SQ[9]  = dvxNoise*SQ[2]*SQ[7] - dvyNoise*SQ[1]*SQ[4] - dvzNoise*SQ[6]*SQ[8];//GQG[3][4]=GQG[4][3]
	   SQ[10] = daxNoise*SQ[2]*SQ[7] - dayNoise*SQ[1]*SQ[4] - dazNoise*SQ[6]*SQ[8];//GQG[1][0]=GQG[1][0]
	   SQ[11] = dvyNoise*SQ[1]*SQ[5] - dvxNoise*SQ[3]*SQ[7] - dvzNoise*SQ[0]*SQ[6];//GQG[4][5]=GQG[5][4]
	   SQ[12] = dvzNoise*SQ[0]*SQ[8] - dvyNoise*SQ[4]*SQ[5] - dvxNoise*SQ[2]*SQ[3];//GQG[3][5]
	   SQ[13] = dayNoise*SQ[1]*SQ[5] - daxNoise*SQ[3]*SQ[7] - dazNoise*SQ[0]*SQ[6];//GQG[1][2]
	   SQ[14] = dazNoise*SQ[0]*SQ[8] - dayNoise*SQ[4]*SQ[5] - daxNoise*SQ[2]*SQ[3];//GQG[0][2]=GQG[2][0]

	   //�Գ���
	   SQ[15] = sq(SQ[7]);//SQ7^2���ڼ���GQG[1][1]��GQG[4][4]
	   SQ[16] = sq(SQ[8]);//SQ8^2���ڼ���GQG[0][0]��GQG[3][3]
	   SQ[17] = sq(SQ[0]);//SQ0^2���ڼ���GQG[2][2]��GQG[5][5]
	   SQ[18] = sq(SQ[5]);//SQ5^2���ڼ���GQG[2][2]��GQG[5][5]
	   SQ[19] = sq(SQ[3]);//SQ3^2���ڼ���GQG[2][2]��GQG[5][5]
	   SQ[20] = sq(SQ[6]);//SQ6^2���ڼ���GQG[1][1]��GQG[4][4]
	   SQ[21] = sq(SQ[1]);//SQ1^2���ڼ���GQG[1][1]��GQG[4][4]
	   SQ[22] = sq(SQ[4]);//SQ4^2���ڼ���GQG[0][0]��GQG[3][3]
	   SQ[23] = sq(SQ[2]);//SQ2^2���ڼ���GQG[0][0]��GQG[3][3]

	   //���ڼ���P=F*P*F+Q�еĹ�����(fned x)��fned����ķ��Գƾ���
	   //fned = Cbn*fxyz=>fned*dt = Cbn*fxyz*dt=>dv_ned = Cbn*dv_xyz
	   ftype SPP[7];
	   SPP[0] = dvx*SF[5] + dvy*SF[1] - dvz*SF[8];//fe*dt
	   SPP[1] = dvy*SF[7] - dvx*SF[3] + dvz*SF[2];//fd*dt
	   SPP[2] = dvx*SF[0] - dvy*SF[6] + dvz*SF[4];//fn*dt
	   SPP[3] = SF[9];//һ�������ɷ�ʱ�����ϵ������
	   SPP[4] = SF[10];//һ�������ɷ�ʱ�����ϵ������
	   SPP[5] = SF[7]; //Cbn[2][1]
	   SPP[6] = SF[8];//Cbn[0][2]

	   //����Pk_k_1 = F*Pk_1*F' + G*Q*G'
	   //Э����ĸ�����ͨ�������Ż���ɵ�
	   ins_state.Pk_k_1[0][0] = ins_state.Pk[0][0] + daxNoise*SQ[23] + dayNoise*SQ[22] + dazNoise*SQ[16] - ins_state.Pk[9][0]*dt*SF[0] + ins_state.Pk[10][0]*dt*SF[6] - ins_state.Pk[11][0]*dt*SF[4] - dt*SF[0]*(ins_state.Pk[0][9] - ins_state.Pk[9][9]*dt*SF[0] + ins_state.Pk[10][9]*dt*SF[6] - ins_state.Pk[11][9]*dt*SF[4]) + dt*SF[6]*(ins_state.Pk[0][10] - ins_state.Pk[9][10]*dt*SF[0] + ins_state.Pk[10][10]*dt*SF[6] - ins_state.Pk[11][10]*dt*SF[4]) - dt*SF[4]*(ins_state.Pk[0][11] - ins_state.Pk[9][11]*dt*SF[0] + ins_state.Pk[10][11]*dt*SF[6] - ins_state.Pk[11][11]*dt*SF[4]);
	   ins_state.Pk_k_1[0][1] = ins_state.Pk[0][1] + SQ[10] - ins_state.Pk[9][1]*dt*SF[0] + ins_state.Pk[10][1]*dt*SF[6] - ins_state.Pk[11][1]*dt*SF[4] - dt*SF[1]*(ins_state.Pk[0][10] - ins_state.Pk[9][10]*dt*SF[0] + ins_state.Pk[10][10]*dt*SF[6] - ins_state.Pk[11][10]*dt*SF[4]) - dt*SF[5]*(ins_state.Pk[0][9] - ins_state.Pk[9][9]*dt*SF[0] + ins_state.Pk[10][9]*dt*SF[6] - ins_state.Pk[11][9]*dt*SF[4]) + dt*SPP[6]*(ins_state.Pk[0][11] - ins_state.Pk[9][11]*dt*SF[0] + ins_state.Pk[10][11]*dt*SF[6] - ins_state.Pk[11][11]*dt*SF[4]);
	   ins_state.Pk_k_1[1][1] = ins_state.Pk[1][1] + daxNoise*SQ[15] + dayNoise*SQ[21] + dazNoise*SQ[20] - ins_state.Pk[9][1]*dt*SF[5] - ins_state.Pk[10][1]*dt*SF[1] + ins_state.Pk[11][1]*dt*SPP[6] - dt*SF[1]*(ins_state.Pk[1][10] - ins_state.Pk[9][10]*dt*SF[5] - ins_state.Pk[10][10]*dt*SF[1] + ins_state.Pk[11][10]*dt*SPP[6]) - dt*SF[5]*(ins_state.Pk[1][9] - ins_state.Pk[9][9]*dt*SF[5] - ins_state.Pk[10][9]*dt*SF[1] + ins_state.Pk[11][9]*dt*SPP[6]) + dt*SPP[6]*(ins_state.Pk[1][11] - ins_state.Pk[9][11]*dt*SF[5] - ins_state.Pk[10][11]*dt*SF[1] + ins_state.Pk[11][11]*dt*SPP[6]);
	   ins_state.Pk_k_1[0][2] = ins_state.Pk[0][2] + SQ[14] - ins_state.Pk[9][2]*dt*SF[0] + ins_state.Pk[10][2]*dt*SF[6] - ins_state.Pk[11][2]*dt*SF[4] + dt*SF[3]*(ins_state.Pk[0][9] - ins_state.Pk[9][9]*dt*SF[0] + ins_state.Pk[10][9]*dt*SF[6] - ins_state.Pk[11][9]*dt*SF[4]) - dt*SF[2]*(ins_state.Pk[0][11] - ins_state.Pk[9][11]*dt*SF[0] + ins_state.Pk[10][11]*dt*SF[6] - ins_state.Pk[11][11]*dt*SF[4]) - dt*SPP[5]*(ins_state.Pk[0][10] - ins_state.Pk[9][10]*dt*SF[0] + ins_state.Pk[10][10]*dt*SF[6] - ins_state.Pk[11][10]*dt*SF[4]);
	   ins_state.Pk_k_1[1][2] = ins_state.Pk[1][2] + SQ[13] - ins_state.Pk[9][2]*dt*SF[5] - ins_state.Pk[10][2]*dt*SF[1] + ins_state.Pk[11][2]*dt*SPP[6] + dt*SF[3]*(ins_state.Pk[1][9] - ins_state.Pk[9][9]*dt*SF[5] - ins_state.Pk[10][9]*dt*SF[1] + ins_state.Pk[11][9]*dt*SPP[6]) - dt*SF[2]*(ins_state.Pk[1][11] - ins_state.Pk[9][11]*dt*SF[5] - ins_state.Pk[10][11]*dt*SF[1] + ins_state.Pk[11][11]*dt*SPP[6]) - dt*SPP[5]*(ins_state.Pk[1][10] - ins_state.Pk[9][10]*dt*SF[5] - ins_state.Pk[10][10]*dt*SF[1] + ins_state.Pk[11][10]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][2] = ins_state.Pk[2][2] + daxNoise*SQ[19] + dayNoise*SQ[18] + dazNoise*SQ[17] + ins_state.Pk[9][2]*dt*SF[3] - ins_state.Pk[11][2]*dt*SF[2] - ins_state.Pk[10][2]*dt*SPP[5] + dt*SF[3]*(ins_state.Pk[2][9] + ins_state.Pk[9][9]*dt*SF[3] - ins_state.Pk[11][9]*dt*SF[2] - ins_state.Pk[10][9]*dt*SPP[5]) - dt*SF[2]*(ins_state.Pk[2][11] + ins_state.Pk[9][11]*dt*SF[3] - ins_state.Pk[11][11]*dt*SF[2] - ins_state.Pk[10][11]*dt*SPP[5]) - dt*SPP[5]*(ins_state.Pk[2][10] + ins_state.Pk[9][10]*dt*SF[3] - ins_state.Pk[11][10]*dt*SF[2] - ins_state.Pk[10][10]*dt*SPP[5]);
	   ins_state.Pk_k_1[0][3] = ins_state.Pk[0][3] - SPP[1]*(ins_state.Pk[0][1] - ins_state.Pk[9][1]*dt*SF[0] + ins_state.Pk[10][1]*dt*SF[6] - ins_state.Pk[11][1]*dt*SF[4]) + SPP[0]*(ins_state.Pk[0][2] - ins_state.Pk[9][2]*dt*SF[0] + ins_state.Pk[10][2]*dt*SF[6] - ins_state.Pk[11][2]*dt*SF[4]) - ins_state.Pk[9][3]*dt*SF[0] + ins_state.Pk[10][3]*dt*SF[6] - ins_state.Pk[11][3]*dt*SF[4] + dt*SF[0]*(ins_state.Pk[0][12] - ins_state.Pk[9][12]*dt*SF[0] + ins_state.Pk[10][12]*dt*SF[6] - ins_state.Pk[11][12]*dt*SF[4]) - dt*SF[6]*(ins_state.Pk[0][13] - ins_state.Pk[9][13]*dt*SF[0] + ins_state.Pk[10][13]*dt*SF[6] - ins_state.Pk[11][13]*dt*SF[4]) + dt*SF[4]*(ins_state.Pk[0][14] - ins_state.Pk[9][14]*dt*SF[0] + ins_state.Pk[10][14]*dt*SF[6] - ins_state.Pk[11][14]*dt*SF[4]);
	   ins_state.Pk_k_1[1][3] = ins_state.Pk[1][3] - SPP[1]*(ins_state.Pk[1][1] - ins_state.Pk[9][1]*dt*SF[5] - ins_state.Pk[10][1]*dt*SF[1] + ins_state.Pk[11][1]*dt*SPP[6]) + SPP[0]*(ins_state.Pk[1][2] - ins_state.Pk[9][2]*dt*SF[5] - ins_state.Pk[10][2]*dt*SF[1] + ins_state.Pk[11][2]*dt*SPP[6]) - ins_state.Pk[9][3]*dt*SF[5] - ins_state.Pk[10][3]*dt*SF[1] + ins_state.Pk[11][3]*dt*SPP[6] + dt*SF[0]*(ins_state.Pk[1][12] - ins_state.Pk[9][12]*dt*SF[5] - ins_state.Pk[10][12]*dt*SF[1] + ins_state.Pk[11][12]*dt*SPP[6]) - dt*SF[6]*(ins_state.Pk[1][13] - ins_state.Pk[9][13]*dt*SF[5] - ins_state.Pk[10][13]*dt*SF[1] + ins_state.Pk[11][13]*dt*SPP[6]) + dt*SF[4]*(ins_state.Pk[1][14] - ins_state.Pk[9][14]*dt*SF[5] - ins_state.Pk[10][14]*dt*SF[1] + ins_state.Pk[11][14]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][3] = ins_state.Pk[2][3] - SPP[1]*(ins_state.Pk[2][1] + ins_state.Pk[9][1]*dt*SF[3] - ins_state.Pk[11][1]*dt*SF[2] - ins_state.Pk[10][1]*dt*SPP[5]) + SPP[0]*(ins_state.Pk[2][2] + ins_state.Pk[9][2]*dt*SF[3] - ins_state.Pk[11][2]*dt*SF[2] - ins_state.Pk[10][2]*dt*SPP[5]) + ins_state.Pk[9][3]*dt*SF[3] - ins_state.Pk[11][3]*dt*SF[2] - ins_state.Pk[10][3]*dt*SPP[5] + dt*SF[0]*(ins_state.Pk[2][12] + ins_state.Pk[9][12]*dt*SF[3] - ins_state.Pk[11][12]*dt*SF[2] - ins_state.Pk[10][12]*dt*SPP[5]) - dt*SF[6]*(ins_state.Pk[2][13] + ins_state.Pk[9][13]*dt*SF[3] - ins_state.Pk[11][13]*dt*SF[2] - ins_state.Pk[10][13]*dt*SPP[5]) + dt*SF[4]*(ins_state.Pk[2][14] + ins_state.Pk[9][14]*dt*SF[3] - ins_state.Pk[11][14]*dt*SF[2] - ins_state.Pk[10][14]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][3] = ins_state.Pk[3][3] - ins_state.Pk[1][3]*SPP[1] + ins_state.Pk[2][3]*SPP[0] + dvxNoise*SQ[23] + dvyNoise*SQ[22] + dvzNoise*SQ[16] - SPP[1]*(ins_state.Pk[3][1] - ins_state.Pk[1][1]*SPP[1] + ins_state.Pk[2][1]*SPP[0] + ins_state.Pk[12][1]*dt*SF[0] - ins_state.Pk[13][1]*dt*SF[6] + ins_state.Pk[14][1]*dt*SF[4]) + SPP[0]*(ins_state.Pk[3][2] - ins_state.Pk[1][2]*SPP[1] + ins_state.Pk[2][2]*SPP[0] + ins_state.Pk[12][2]*dt*SF[0] - ins_state.Pk[13][2]*dt*SF[6] + ins_state.Pk[14][2]*dt*SF[4]) + ins_state.Pk[12][3]*dt*SF[0] - ins_state.Pk[13][3]*dt*SF[6] + ins_state.Pk[14][3]*dt*SF[4] + dt*SF[0]*(ins_state.Pk[3][12] - ins_state.Pk[1][12]*SPP[1] + ins_state.Pk[2][12]*SPP[0] + ins_state.Pk[12][12]*dt*SF[0] - ins_state.Pk[13][12]*dt*SF[6] + ins_state.Pk[14][12]*dt*SF[4]) - dt*SF[6]*(ins_state.Pk[3][13] - ins_state.Pk[1][13]*SPP[1] + ins_state.Pk[2][13]*SPP[0] + ins_state.Pk[12][13]*dt*SF[0] - ins_state.Pk[13][13]*dt*SF[6] + ins_state.Pk[14][13]*dt*SF[4]) + dt*SF[4]*(ins_state.Pk[3][14] - ins_state.Pk[1][14]*SPP[1] + ins_state.Pk[2][14]*SPP[0] + ins_state.Pk[12][14]*dt*SF[0] - ins_state.Pk[13][14]*dt*SF[6] + ins_state.Pk[14][14]*dt*SF[4]);
	   ins_state.Pk_k_1[0][4] = ins_state.Pk[0][4] + SPP[1]*(ins_state.Pk[0][0] - ins_state.Pk[9][0]*dt*SF[0] + ins_state.Pk[10][0]*dt*SF[6] - ins_state.Pk[11][0]*dt*SF[4]) - SPP[2]*(ins_state.Pk[0][2] - ins_state.Pk[9][2]*dt*SF[0] + ins_state.Pk[10][2]*dt*SF[6] - ins_state.Pk[11][2]*dt*SF[4]) - ins_state.Pk[9][4]*dt*SF[0] + ins_state.Pk[10][4]*dt*SF[6] - ins_state.Pk[11][4]*dt*SF[4] + dt*SF[1]*(ins_state.Pk[0][13] - ins_state.Pk[9][13]*dt*SF[0] + ins_state.Pk[10][13]*dt*SF[6] - ins_state.Pk[11][13]*dt*SF[4]) + dt*SF[5]*(ins_state.Pk[0][12] - ins_state.Pk[9][12]*dt*SF[0] + ins_state.Pk[10][12]*dt*SF[6] - ins_state.Pk[11][12]*dt*SF[4]) - dt*SPP[6]*(ins_state.Pk[0][14] - ins_state.Pk[9][14]*dt*SF[0] + ins_state.Pk[10][14]*dt*SF[6] - ins_state.Pk[11][14]*dt*SF[4]);
	   ins_state.Pk_k_1[1][4] = ins_state.Pk[1][4] + SPP[1]*(ins_state.Pk[1][0] - ins_state.Pk[9][0]*dt*SF[5] - ins_state.Pk[10][0]*dt*SF[1] + ins_state.Pk[11][0]*dt*SPP[6]) - SPP[2]*(ins_state.Pk[1][2] - ins_state.Pk[9][2]*dt*SF[5] - ins_state.Pk[10][2]*dt*SF[1] + ins_state.Pk[11][2]*dt*SPP[6]) - ins_state.Pk[9][4]*dt*SF[5] - ins_state.Pk[10][4]*dt*SF[1] + ins_state.Pk[11][4]*dt*SPP[6] + dt*SF[1]*(ins_state.Pk[1][13] - ins_state.Pk[9][13]*dt*SF[5] - ins_state.Pk[10][13]*dt*SF[1] + ins_state.Pk[11][13]*dt*SPP[6]) + dt*SF[5]*(ins_state.Pk[1][12] - ins_state.Pk[9][12]*dt*SF[5] - ins_state.Pk[10][12]*dt*SF[1] + ins_state.Pk[11][12]*dt*SPP[6]) - dt*SPP[6]*(ins_state.Pk[1][14] - ins_state.Pk[9][14]*dt*SF[5] - ins_state.Pk[10][14]*dt*SF[1] + ins_state.Pk[11][14]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][4] = ins_state.Pk[2][4] + SPP[1]*(ins_state.Pk[2][0] + ins_state.Pk[9][0]*dt*SF[3] - ins_state.Pk[11][0]*dt*SF[2] - ins_state.Pk[10][0]*dt*SPP[5]) - SPP[2]*(ins_state.Pk[2][2] + ins_state.Pk[9][2]*dt*SF[3] - ins_state.Pk[11][2]*dt*SF[2] - ins_state.Pk[10][2]*dt*SPP[5]) + ins_state.Pk[9][4]*dt*SF[3] - ins_state.Pk[11][4]*dt*SF[2] - ins_state.Pk[10][4]*dt*SPP[5] + dt*SF[1]*(ins_state.Pk[2][13] + ins_state.Pk[9][13]*dt*SF[3] - ins_state.Pk[11][13]*dt*SF[2] - ins_state.Pk[10][13]*dt*SPP[5]) + dt*SF[5]*(ins_state.Pk[2][12] + ins_state.Pk[9][12]*dt*SF[3] - ins_state.Pk[11][12]*dt*SF[2] - ins_state.Pk[10][12]*dt*SPP[5]) - dt*SPP[6]*(ins_state.Pk[2][14] + ins_state.Pk[9][14]*dt*SF[3] - ins_state.Pk[11][14]*dt*SF[2] - ins_state.Pk[10][14]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][4] = ins_state.Pk[3][4] + SQ[9] - ins_state.Pk[1][4]*SPP[1] + ins_state.Pk[2][4]*SPP[0] + SPP[1]*(ins_state.Pk[3][0] - ins_state.Pk[1][0]*SPP[1] + ins_state.Pk[2][0]*SPP[0] + ins_state.Pk[12][0]*dt*SF[0] - ins_state.Pk[13][0]*dt*SF[6] + ins_state.Pk[14][0]*dt*SF[4]) - SPP[2]*(ins_state.Pk[3][2] - ins_state.Pk[1][2]*SPP[1] + ins_state.Pk[2][2]*SPP[0] + ins_state.Pk[12][2]*dt*SF[0] - ins_state.Pk[13][2]*dt*SF[6] + ins_state.Pk[14][2]*dt*SF[4]) + ins_state.Pk[12][4]*dt*SF[0] - ins_state.Pk[13][4]*dt*SF[6] + ins_state.Pk[14][4]*dt*SF[4] + dt*SF[5]*(ins_state.Pk[3][12] - ins_state.Pk[1][12]*SPP[1] + ins_state.Pk[2][12]*SPP[0] + ins_state.Pk[12][12]*dt*SF[0] - ins_state.Pk[13][12]*dt*SF[6] + ins_state.Pk[14][12]*dt*SF[4]) + dt*SF[1]*(ins_state.Pk[3][13] - ins_state.Pk[1][13]*SPP[1] + ins_state.Pk[2][13]*SPP[0] + ins_state.Pk[12][13]*dt*SF[0] - ins_state.Pk[13][13]*dt*SF[6] + ins_state.Pk[14][13]*dt*SF[4]) - dt*SPP[6]*(ins_state.Pk[3][14] - ins_state.Pk[1][14]*SPP[1] + ins_state.Pk[2][14]*SPP[0] + ins_state.Pk[12][14]*dt*SF[0] - ins_state.Pk[13][14]*dt*SF[6] + ins_state.Pk[14][14]*dt*SF[4]);
	   ins_state.Pk_k_1[4][4] = ins_state.Pk[4][4] + ins_state.Pk[0][4]*SPP[1] - ins_state.Pk[2][4]*SPP[2] + dvxNoise*SQ[15] + dvyNoise*SQ[21] + dvzNoise*SQ[20] + SPP[1]*(ins_state.Pk[4][0] + ins_state.Pk[0][0]*SPP[1] - ins_state.Pk[2][0]*SPP[2] + ins_state.Pk[12][0]*dt*SF[5] + ins_state.Pk[13][0]*dt*SF[1] - ins_state.Pk[14][0]*dt*SPP[6]) - SPP[2]*(ins_state.Pk[4][2] + ins_state.Pk[0][2]*SPP[1] - ins_state.Pk[2][2]*SPP[2] + ins_state.Pk[12][2]*dt*SF[5] + ins_state.Pk[13][2]*dt*SF[1] - ins_state.Pk[14][2]*dt*SPP[6]) + ins_state.Pk[12][4]*dt*SF[5] + ins_state.Pk[13][4]*dt*SF[1] - ins_state.Pk[14][4]*dt*SPP[6] + dt*SF[5]*(ins_state.Pk[4][12] + ins_state.Pk[0][12]*SPP[1] - ins_state.Pk[2][12]*SPP[2] + ins_state.Pk[12][12]*dt*SF[5] + ins_state.Pk[13][12]*dt*SF[1] - ins_state.Pk[14][12]*dt*SPP[6]) + dt*SF[1]*(ins_state.Pk[4][13] + ins_state.Pk[0][13]*SPP[1] - ins_state.Pk[2][13]*SPP[2] + ins_state.Pk[12][13]*dt*SF[5] + ins_state.Pk[13][13]*dt*SF[1] - ins_state.Pk[14][13]*dt*SPP[6]) - dt*SPP[6]*(ins_state.Pk[4][14] + ins_state.Pk[0][14]*SPP[1] - ins_state.Pk[2][14]*SPP[2] + ins_state.Pk[12][14]*dt*SF[5] + ins_state.Pk[13][14]*dt*SF[1] - ins_state.Pk[14][14]*dt*SPP[6]);
	   ins_state.Pk_k_1[0][5] = ins_state.Pk[0][5] - SPP[0]*(ins_state.Pk[0][0] - ins_state.Pk[9][0]*dt*SF[0] + ins_state.Pk[10][0]*dt*SF[6] - ins_state.Pk[11][0]*dt*SF[4]) + SPP[2]*(ins_state.Pk[0][1] - ins_state.Pk[9][1]*dt*SF[0] + ins_state.Pk[10][1]*dt*SF[6] - ins_state.Pk[11][1]*dt*SF[4]) - ins_state.Pk[9][5]*dt*SF[0] + ins_state.Pk[10][5]*dt*SF[6] - ins_state.Pk[11][5]*dt*SF[4] - dt*SF[3]*(ins_state.Pk[0][12] - ins_state.Pk[9][12]*dt*SF[0] + ins_state.Pk[10][12]*dt*SF[6] - ins_state.Pk[11][12]*dt*SF[4]) + dt*SF[2]*(ins_state.Pk[0][14] - ins_state.Pk[9][14]*dt*SF[0] + ins_state.Pk[10][14]*dt*SF[6] - ins_state.Pk[11][14]*dt*SF[4]) + dt*SPP[5]*(ins_state.Pk[0][13] - ins_state.Pk[9][13]*dt*SF[0] + ins_state.Pk[10][13]*dt*SF[6] - ins_state.Pk[11][13]*dt*SF[4]);
	   ins_state.Pk_k_1[1][5] = ins_state.Pk[1][5] - SPP[0]*(ins_state.Pk[1][0] - ins_state.Pk[9][0]*dt*SF[5] - ins_state.Pk[10][0]*dt*SF[1] + ins_state.Pk[11][0]*dt*SPP[6]) + SPP[2]*(ins_state.Pk[1][1] - ins_state.Pk[9][1]*dt*SF[5] - ins_state.Pk[10][1]*dt*SF[1] + ins_state.Pk[11][1]*dt*SPP[6]) - ins_state.Pk[9][5]*dt*SF[5] - ins_state.Pk[10][5]*dt*SF[1] + ins_state.Pk[11][5]*dt*SPP[6] - dt*SF[3]*(ins_state.Pk[1][12] - ins_state.Pk[9][12]*dt*SF[5] - ins_state.Pk[10][12]*dt*SF[1] + ins_state.Pk[11][12]*dt*SPP[6]) + dt*SF[2]*(ins_state.Pk[1][14] - ins_state.Pk[9][14]*dt*SF[5] - ins_state.Pk[10][14]*dt*SF[1] + ins_state.Pk[11][14]*dt*SPP[6]) + dt*SPP[5]*(ins_state.Pk[1][13] - ins_state.Pk[9][13]*dt*SF[5] - ins_state.Pk[10][13]*dt*SF[1] + ins_state.Pk[11][13]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][5] = ins_state.Pk[2][5] - SPP[0]*(ins_state.Pk[2][0] + ins_state.Pk[9][0]*dt*SF[3] - ins_state.Pk[11][0]*dt*SF[2] - ins_state.Pk[10][0]*dt*SPP[5]) + SPP[2]*(ins_state.Pk[2][1] + ins_state.Pk[9][1]*dt*SF[3] - ins_state.Pk[11][1]*dt*SF[2] - ins_state.Pk[10][1]*dt*SPP[5]) + ins_state.Pk[9][5]*dt*SF[3] - ins_state.Pk[11][5]*dt*SF[2] - ins_state.Pk[10][5]*dt*SPP[5] - dt*SF[3]*(ins_state.Pk[2][12] + ins_state.Pk[9][12]*dt*SF[3] - ins_state.Pk[11][12]*dt*SF[2] - ins_state.Pk[10][12]*dt*SPP[5]) + dt*SF[2]*(ins_state.Pk[2][14] + ins_state.Pk[9][14]*dt*SF[3] - ins_state.Pk[11][14]*dt*SF[2] - ins_state.Pk[10][14]*dt*SPP[5]) + dt*SPP[5]*(ins_state.Pk[2][13] + ins_state.Pk[9][13]*dt*SF[3] - ins_state.Pk[11][13]*dt*SF[2] - ins_state.Pk[10][13]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][5] = ins_state.Pk[3][5] + SQ[12] - ins_state.Pk[1][5]*SPP[1] + ins_state.Pk[2][5]*SPP[0] - SPP[0]*(ins_state.Pk[3][0] - ins_state.Pk[1][0]*SPP[1] + ins_state.Pk[2][0]*SPP[0] + ins_state.Pk[12][0]*dt*SF[0] - ins_state.Pk[13][0]*dt*SF[6] + ins_state.Pk[14][0]*dt*SF[4]) + SPP[2]*(ins_state.Pk[3][1] - ins_state.Pk[1][1]*SPP[1] + ins_state.Pk[2][1]*SPP[0] + ins_state.Pk[12][1]*dt*SF[0] - ins_state.Pk[13][1]*dt*SF[6] + ins_state.Pk[14][1]*dt*SF[4]) + ins_state.Pk[12][5]*dt*SF[0] - ins_state.Pk[13][5]*dt*SF[6] + ins_state.Pk[14][5]*dt*SF[4] - dt*SF[3]*(ins_state.Pk[3][12] - ins_state.Pk[1][12]*SPP[1] + ins_state.Pk[2][12]*SPP[0] + ins_state.Pk[12][12]*dt*SF[0] - ins_state.Pk[13][12]*dt*SF[6] + ins_state.Pk[14][12]*dt*SF[4]) + dt*SF[2]*(ins_state.Pk[3][14] - ins_state.Pk[1][14]*SPP[1] + ins_state.Pk[2][14]*SPP[0] + ins_state.Pk[12][14]*dt*SF[0] - ins_state.Pk[13][14]*dt*SF[6] + ins_state.Pk[14][14]*dt*SF[4]) + dt*SPP[5]*(ins_state.Pk[3][13] - ins_state.Pk[1][13]*SPP[1] + ins_state.Pk[2][13]*SPP[0] + ins_state.Pk[12][13]*dt*SF[0] - ins_state.Pk[13][13]*dt*SF[6] + ins_state.Pk[14][13]*dt*SF[4]);
	   ins_state.Pk_k_1[4][5] = ins_state.Pk[4][5] + SQ[11] + ins_state.Pk[0][5]*SPP[1] - ins_state.Pk[2][5]*SPP[2] - SPP[0]*(ins_state.Pk[4][0] + ins_state.Pk[0][0]*SPP[1] - ins_state.Pk[2][0]*SPP[2] + ins_state.Pk[12][0]*dt*SF[5] + ins_state.Pk[13][0]*dt*SF[1] - ins_state.Pk[14][0]*dt*SPP[6]) + SPP[2]*(ins_state.Pk[4][1] + ins_state.Pk[0][1]*SPP[1] - ins_state.Pk[2][1]*SPP[2] + ins_state.Pk[12][1]*dt*SF[5] + ins_state.Pk[13][1]*dt*SF[1] - ins_state.Pk[14][1]*dt*SPP[6]) + ins_state.Pk[12][5]*dt*SF[5] + ins_state.Pk[13][5]*dt*SF[1] - ins_state.Pk[14][5]*dt*SPP[6] - dt*SF[3]*(ins_state.Pk[4][12] + ins_state.Pk[0][12]*SPP[1] - ins_state.Pk[2][12]*SPP[2] + ins_state.Pk[12][12]*dt*SF[5] + ins_state.Pk[13][12]*dt*SF[1] - ins_state.Pk[14][12]*dt*SPP[6]) + dt*SF[2]*(ins_state.Pk[4][14] + ins_state.Pk[0][14]*SPP[1] - ins_state.Pk[2][14]*SPP[2] + ins_state.Pk[12][14]*dt*SF[5] + ins_state.Pk[13][14]*dt*SF[1] - ins_state.Pk[14][14]*dt*SPP[6]) + dt*SPP[5]*(ins_state.Pk[4][13] + ins_state.Pk[0][13]*SPP[1] - ins_state.Pk[2][13]*SPP[2] + ins_state.Pk[12][13]*dt*SF[5] + ins_state.Pk[13][13]*dt*SF[1] - ins_state.Pk[14][13]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][5] = ins_state.Pk[5][5] - ins_state.Pk[0][5]*SPP[0] + ins_state.Pk[1][5]*SPP[2] + dvxNoise*SQ[19] + dvyNoise*SQ[18] + dvzNoise*SQ[17] - SPP[0]*(ins_state.Pk[5][0] - ins_state.Pk[0][0]*SPP[0] + ins_state.Pk[1][0]*SPP[2] - ins_state.Pk[12][0]*dt*SF[3] + ins_state.Pk[14][0]*dt*SF[2] + ins_state.Pk[13][0]*dt*SPP[5]) + SPP[2]*(ins_state.Pk[5][1] - ins_state.Pk[0][1]*SPP[0] + ins_state.Pk[1][1]*SPP[2] - ins_state.Pk[12][1]*dt*SF[3] + ins_state.Pk[14][1]*dt*SF[2] + ins_state.Pk[13][1]*dt*SPP[5]) - ins_state.Pk[12][5]*dt*SF[3] + ins_state.Pk[14][5]*dt*SF[2] + ins_state.Pk[13][5]*dt*SPP[5] - dt*SF[3]*(ins_state.Pk[5][12] - ins_state.Pk[0][12]*SPP[0] + ins_state.Pk[1][12]*SPP[2] - ins_state.Pk[12][12]*dt*SF[3] + ins_state.Pk[14][12]*dt*SF[2] + ins_state.Pk[13][12]*dt*SPP[5]) + dt*SF[2]*(ins_state.Pk[5][14] - ins_state.Pk[0][14]*SPP[0] + ins_state.Pk[1][14]*SPP[2] - ins_state.Pk[12][14]*dt*SF[3] + ins_state.Pk[14][14]*dt*SF[2] + ins_state.Pk[13][14]*dt*SPP[5]) + dt*SPP[5]*(ins_state.Pk[5][13] - ins_state.Pk[0][13]*SPP[0] + ins_state.Pk[1][13]*SPP[2] - ins_state.Pk[12][13]*dt*SF[3] + ins_state.Pk[14][13]*dt*SF[2] + ins_state.Pk[13][13]*dt*SPP[5]);
	   ins_state.Pk_k_1[0][6] = ins_state.Pk[0][6] + dt*(ins_state.Pk[0][3] - ins_state.Pk[9][3]*dt*SF[0] + ins_state.Pk[10][3]*dt*SF[6] - ins_state.Pk[11][3]*dt*SF[4]) - ins_state.Pk[9][6]*dt*SF[0] + ins_state.Pk[10][6]*dt*SF[6] - ins_state.Pk[11][6]*dt*SF[4];
	   ins_state.Pk_k_1[1][6] = ins_state.Pk[1][6] + dt*(ins_state.Pk[1][3] - ins_state.Pk[9][3]*dt*SF[5] - ins_state.Pk[10][3]*dt*SF[1] + ins_state.Pk[11][3]*dt*SPP[6]) - ins_state.Pk[9][6]*dt*SF[5] - ins_state.Pk[10][6]*dt*SF[1] + ins_state.Pk[11][6]*dt*SPP[6];
	   ins_state.Pk_k_1[2][6] = ins_state.Pk[2][6] + dt*(ins_state.Pk[2][3] + ins_state.Pk[9][3]*dt*SF[3] - ins_state.Pk[11][3]*dt*SF[2] - ins_state.Pk[10][3]*dt*SPP[5]) + ins_state.Pk[9][6]*dt*SF[3] - ins_state.Pk[11][6]*dt*SF[2] - ins_state.Pk[10][6]*dt*SPP[5];
	   ins_state.Pk_k_1[3][6] = ins_state.Pk[3][6] - ins_state.Pk[1][6]*SPP[1] + ins_state.Pk[2][6]*SPP[0] + dt*(ins_state.Pk[3][3] - ins_state.Pk[1][3]*SPP[1] + ins_state.Pk[2][3]*SPP[0] + ins_state.Pk[12][3]*dt*SF[0] - ins_state.Pk[13][3]*dt*SF[6] + ins_state.Pk[14][3]*dt*SF[4]) + ins_state.Pk[12][6]*dt*SF[0] - ins_state.Pk[13][6]*dt*SF[6] + ins_state.Pk[14][6]*dt*SF[4];
	   ins_state.Pk_k_1[4][6] = ins_state.Pk[4][6] + ins_state.Pk[0][6]*SPP[1] - ins_state.Pk[2][6]*SPP[2] + dt*(ins_state.Pk[4][3] + ins_state.Pk[0][3]*SPP[1] - ins_state.Pk[2][3]*SPP[2] + ins_state.Pk[12][3]*dt*SF[5] + ins_state.Pk[13][3]*dt*SF[1] - ins_state.Pk[14][3]*dt*SPP[6]) + ins_state.Pk[12][6]*dt*SF[5] + ins_state.Pk[13][6]*dt*SF[1] - ins_state.Pk[14][6]*dt*SPP[6];
	   ins_state.Pk_k_1[5][6] = ins_state.Pk[5][6] - ins_state.Pk[0][6]*SPP[0] + ins_state.Pk[1][6]*SPP[2] + dt*(ins_state.Pk[5][3] - ins_state.Pk[0][3]*SPP[0] + ins_state.Pk[1][3]*SPP[2] - ins_state.Pk[12][3]*dt*SF[3] + ins_state.Pk[14][3]*dt*SF[2] + ins_state.Pk[13][3]*dt*SPP[5]) - ins_state.Pk[12][6]*dt*SF[3] + ins_state.Pk[14][6]*dt*SF[2] + ins_state.Pk[13][6]*dt*SPP[5];
	   ins_state.Pk_k_1[6][6] = ins_state.Pk[6][6] + ins_state.Pk[3][6]*dt + dt*(ins_state.Pk[6][3] + ins_state.Pk[3][3]*dt);
	   ins_state.Pk_k_1[0][7] = ins_state.Pk[0][7] + dt*(ins_state.Pk[0][4] - ins_state.Pk[9][4]*dt*SF[0] + ins_state.Pk[10][4]*dt*SF[6] - ins_state.Pk[11][4]*dt*SF[4]) - ins_state.Pk[9][7]*dt*SF[0] + ins_state.Pk[10][7]*dt*SF[6] - ins_state.Pk[11][7]*dt*SF[4];
	   ins_state.Pk_k_1[1][7] = ins_state.Pk[1][7] + dt*(ins_state.Pk[1][4] - ins_state.Pk[9][4]*dt*SF[5] - ins_state.Pk[10][4]*dt*SF[1] + ins_state.Pk[11][4]*dt*SPP[6]) - ins_state.Pk[9][7]*dt*SF[5] - ins_state.Pk[10][7]*dt*SF[1] + ins_state.Pk[11][7]*dt*SPP[6];
	   ins_state.Pk_k_1[2][7] = ins_state.Pk[2][7] + dt*(ins_state.Pk[2][4] + ins_state.Pk[9][4]*dt*SF[3] - ins_state.Pk[11][4]*dt*SF[2] - ins_state.Pk[10][4]*dt*SPP[5]) + ins_state.Pk[9][7]*dt*SF[3] - ins_state.Pk[11][7]*dt*SF[2] - ins_state.Pk[10][7]*dt*SPP[5];
	   ins_state.Pk_k_1[3][7] = ins_state.Pk[3][7] - ins_state.Pk[1][7]*SPP[1] + ins_state.Pk[2][7]*SPP[0] + dt*(ins_state.Pk[3][4] - ins_state.Pk[1][4]*SPP[1] + ins_state.Pk[2][4]*SPP[0] + ins_state.Pk[12][4]*dt*SF[0] - ins_state.Pk[13][4]*dt*SF[6] + ins_state.Pk[14][4]*dt*SF[4]) + ins_state.Pk[12][7]*dt*SF[0] - ins_state.Pk[13][7]*dt*SF[6] + ins_state.Pk[14][7]*dt*SF[4];
	   ins_state.Pk_k_1[4][7] = ins_state.Pk[4][7] + ins_state.Pk[0][7]*SPP[1] - ins_state.Pk[2][7]*SPP[2] + dt*(ins_state.Pk[4][4] + ins_state.Pk[0][4]*SPP[1] - ins_state.Pk[2][4]*SPP[2] + ins_state.Pk[12][4]*dt*SF[5] + ins_state.Pk[13][4]*dt*SF[1] - ins_state.Pk[14][4]*dt*SPP[6]) + ins_state.Pk[12][7]*dt*SF[5] + ins_state.Pk[13][7]*dt*SF[1] - ins_state.Pk[14][7]*dt*SPP[6];
	   ins_state.Pk_k_1[5][7] = ins_state.Pk[5][7] - ins_state.Pk[0][7]*SPP[0] + ins_state.Pk[1][7]*SPP[2] + dt*(ins_state.Pk[5][4] - ins_state.Pk[0][4]*SPP[0] + ins_state.Pk[1][4]*SPP[2] - ins_state.Pk[12][4]*dt*SF[3] + ins_state.Pk[14][4]*dt*SF[2] + ins_state.Pk[13][4]*dt*SPP[5]) - ins_state.Pk[12][7]*dt*SF[3] + ins_state.Pk[14][7]*dt*SF[2] + ins_state.Pk[13][7]*dt*SPP[5];
	   ins_state.Pk_k_1[6][7] = ins_state.Pk[6][7] + ins_state.Pk[3][7]*dt + dt*(ins_state.Pk[6][4] + ins_state.Pk[3][4]*dt);
	   ins_state.Pk_k_1[7][7] = ins_state.Pk[7][7] + ins_state.Pk[4][7]*dt + dt*(ins_state.Pk[7][4] + ins_state.Pk[4][4]*dt);
	   ins_state.Pk_k_1[0][8] = ins_state.Pk[0][8] + dt*(ins_state.Pk[0][5] - ins_state.Pk[9][5]*dt*SF[0] + ins_state.Pk[10][5]*dt*SF[6] - ins_state.Pk[11][5]*dt*SF[4]) - ins_state.Pk[9][8]*dt*SF[0] + ins_state.Pk[10][8]*dt*SF[6] - ins_state.Pk[11][8]*dt*SF[4];
	   ins_state.Pk_k_1[1][8] = ins_state.Pk[1][8] + dt*(ins_state.Pk[1][5] - ins_state.Pk[9][5]*dt*SF[5] - ins_state.Pk[10][5]*dt*SF[1] + ins_state.Pk[11][5]*dt*SPP[6]) - ins_state.Pk[9][8]*dt*SF[5] - ins_state.Pk[10][8]*dt*SF[1] + ins_state.Pk[11][8]*dt*SPP[6];
	   ins_state.Pk_k_1[2][8] = ins_state.Pk[2][8] + dt*(ins_state.Pk[2][5] + ins_state.Pk[9][5]*dt*SF[3] - ins_state.Pk[11][5]*dt*SF[2] - ins_state.Pk[10][5]*dt*SPP[5]) + ins_state.Pk[9][8]*dt*SF[3] - ins_state.Pk[11][8]*dt*SF[2] - ins_state.Pk[10][8]*dt*SPP[5];
	   ins_state.Pk_k_1[3][8] = ins_state.Pk[3][8] - ins_state.Pk[1][8]*SPP[1] + ins_state.Pk[2][8]*SPP[0] + dt*(ins_state.Pk[3][5] - ins_state.Pk[1][5]*SPP[1] + ins_state.Pk[2][5]*SPP[0] + ins_state.Pk[12][5]*dt*SF[0] - ins_state.Pk[13][5]*dt*SF[6] + ins_state.Pk[14][5]*dt*SF[4]) + ins_state.Pk[12][8]*dt*SF[0] - ins_state.Pk[13][8]*dt*SF[6] + ins_state.Pk[14][8]*dt*SF[4];
	   ins_state.Pk_k_1[4][8] = ins_state.Pk[4][8] + ins_state.Pk[0][8]*SPP[1] - ins_state.Pk[2][8]*SPP[2] + dt*(ins_state.Pk[4][5] + ins_state.Pk[0][5]*SPP[1] - ins_state.Pk[2][5]*SPP[2] + ins_state.Pk[12][5]*dt*SF[5] + ins_state.Pk[13][5]*dt*SF[1] - ins_state.Pk[14][5]*dt*SPP[6]) + ins_state.Pk[12][8]*dt*SF[5] + ins_state.Pk[13][8]*dt*SF[1] - ins_state.Pk[14][8]*dt*SPP[6];
	   ins_state.Pk_k_1[5][8] = ins_state.Pk[5][8] - ins_state.Pk[0][8]*SPP[0] + ins_state.Pk[1][8]*SPP[2] + dt*(ins_state.Pk[5][5] - ins_state.Pk[0][5]*SPP[0] + ins_state.Pk[1][5]*SPP[2] - ins_state.Pk[12][5]*dt*SF[3] + ins_state.Pk[14][5]*dt*SF[2] + ins_state.Pk[13][5]*dt*SPP[5]) - ins_state.Pk[12][8]*dt*SF[3] + ins_state.Pk[14][8]*dt*SF[2] + ins_state.Pk[13][8]*dt*SPP[5];
	   ins_state.Pk_k_1[6][8] = ins_state.Pk[6][8] + ins_state.Pk[3][8]*dt + dt*(ins_state.Pk[6][5] + ins_state.Pk[3][5]*dt);
	   ins_state.Pk_k_1[7][8] = ins_state.Pk[7][8] + ins_state.Pk[4][8]*dt + dt*(ins_state.Pk[7][5] + ins_state.Pk[4][5]*dt);
	   ins_state.Pk_k_1[8][8] = ins_state.Pk[8][8] + ins_state.Pk[5][8]*dt + dt*(ins_state.Pk[8][5] + ins_state.Pk[5][5]*dt);
	   ins_state.Pk_k_1[0][9] = SPP[4]*(ins_state.Pk[0][9] - ins_state.Pk[9][9]*dt*SF[0] + ins_state.Pk[10][9]*dt*SF[6] - ins_state.Pk[11][9]*dt*SF[4]);
	   ins_state.Pk_k_1[1][9] = SPP[4]*(ins_state.Pk[1][9] - ins_state.Pk[9][9]*dt*SF[5] - ins_state.Pk[10][9]*dt*SF[1] + ins_state.Pk[11][9]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][9] = SPP[4]*(ins_state.Pk[2][9] + ins_state.Pk[9][9]*dt*SF[3] - ins_state.Pk[11][9]*dt*SF[2] - ins_state.Pk[10][9]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][9] = SPP[4]*(ins_state.Pk[3][9] - ins_state.Pk[1][9]*SPP[1] + ins_state.Pk[2][9]*SPP[0] + ins_state.Pk[12][9]*dt*SF[0] - ins_state.Pk[13][9]*dt*SF[6] + ins_state.Pk[14][9]*dt*SF[4]);
	   ins_state.Pk_k_1[4][9] = SPP[4]*(ins_state.Pk[4][9] + ins_state.Pk[0][9]*SPP[1] - ins_state.Pk[2][9]*SPP[2] + ins_state.Pk[12][9]*dt*SF[5] + ins_state.Pk[13][9]*dt*SF[1] - ins_state.Pk[14][9]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][9] = SPP[4]*(ins_state.Pk[5][9] - ins_state.Pk[0][9]*SPP[0] + ins_state.Pk[1][9]*SPP[2] - ins_state.Pk[12][9]*dt*SF[3] + ins_state.Pk[14][9]*dt*SF[2] + ins_state.Pk[13][9]*dt*SPP[5]);
	   ins_state.Pk_k_1[6][9] = SPP[4]*(ins_state.Pk[6][9] + ins_state.Pk[3][9]*dt);
	   ins_state.Pk_k_1[7][9] = SPP[4]*(ins_state.Pk[7][9] + ins_state.Pk[4][9]*dt);
	   ins_state.Pk_k_1[8][9] = SPP[4]*(ins_state.Pk[8][9] + ins_state.Pk[5][9]*dt);
	   ins_state.Pk_k_1[9][9] = ins_state.Pk[9][9]*sq(SPP[4]);
	   ins_state.Pk_k_1[0][10] = SPP[4]*(ins_state.Pk[0][10] - ins_state.Pk[9][10]*dt*SF[0] + ins_state.Pk[10][10]*dt*SF[6] - ins_state.Pk[11][10]*dt*SF[4]);
	   ins_state.Pk_k_1[1][10] = SPP[4]*(ins_state.Pk[1][10] - ins_state.Pk[9][10]*dt*SF[5] - ins_state.Pk[10][10]*dt*SF[1] + ins_state.Pk[11][10]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][10] = SPP[4]*(ins_state.Pk[2][10] + ins_state.Pk[9][10]*dt*SF[3] - ins_state.Pk[11][10]*dt*SF[2] - ins_state.Pk[10][10]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][10] = SPP[4]*(ins_state.Pk[3][10] - ins_state.Pk[1][10]*SPP[1] + ins_state.Pk[2][10]*SPP[0] + ins_state.Pk[12][10]*dt*SF[0] - ins_state.Pk[13][10]*dt*SF[6] + ins_state.Pk[14][10]*dt*SF[4]);
	   ins_state.Pk_k_1[4][10] = SPP[4]*(ins_state.Pk[4][10] + ins_state.Pk[0][10]*SPP[1] - ins_state.Pk[2][10]*SPP[2] + ins_state.Pk[12][10]*dt*SF[5] + ins_state.Pk[13][10]*dt*SF[1] - ins_state.Pk[14][10]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][10] = SPP[4]*(ins_state.Pk[5][10] - ins_state.Pk[0][10]*SPP[0] + ins_state.Pk[1][10]*SPP[2] - ins_state.Pk[12][10]*dt*SF[3] + ins_state.Pk[14][10]*dt*SF[2] + ins_state.Pk[13][10]*dt*SPP[5]);
	   ins_state.Pk_k_1[6][10] = SPP[4]*(ins_state.Pk[6][10] + ins_state.Pk[3][10]*dt);
	   ins_state.Pk_k_1[7][10] = SPP[4]*(ins_state.Pk[7][10] + ins_state.Pk[4][10]*dt);
	   ins_state.Pk_k_1[8][10] = SPP[4]*(ins_state.Pk[8][10] + ins_state.Pk[5][10]*dt);
	   ins_state.Pk_k_1[9][10] = ins_state.Pk[9][10]*sq(SPP[4]);
	   ins_state.Pk_k_1[10][10] = ins_state.Pk[10][10]*sq(SPP[4]);
	   ins_state.Pk_k_1[0][11] = SPP[4]*(ins_state.Pk[0][11] - ins_state.Pk[9][11]*dt*SF[0] + ins_state.Pk[10][11]*dt*SF[6] - ins_state.Pk[11][11]*dt*SF[4]);
	   ins_state.Pk_k_1[1][11] = SPP[4]*(ins_state.Pk[1][11] - ins_state.Pk[9][11]*dt*SF[5] - ins_state.Pk[10][11]*dt*SF[1] + ins_state.Pk[11][11]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][11] = SPP[4]*(ins_state.Pk[2][11] + ins_state.Pk[9][11]*dt*SF[3] - ins_state.Pk[11][11]*dt*SF[2] - ins_state.Pk[10][11]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][11] = SPP[4]*(ins_state.Pk[3][11] - ins_state.Pk[1][11]*SPP[1] + ins_state.Pk[2][11]*SPP[0] + ins_state.Pk[12][11]*dt*SF[0] - ins_state.Pk[13][11]*dt*SF[6] + ins_state.Pk[14][11]*dt*SF[4]);
	   ins_state.Pk_k_1[4][11] = SPP[4]*(ins_state.Pk[4][11] + ins_state.Pk[0][11]*SPP[1] - ins_state.Pk[2][11]*SPP[2] + ins_state.Pk[12][11]*dt*SF[5] + ins_state.Pk[13][11]*dt*SF[1] - ins_state.Pk[14][11]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][11] = SPP[4]*(ins_state.Pk[5][11] - ins_state.Pk[0][11]*SPP[0] + ins_state.Pk[1][11]*SPP[2] - ins_state.Pk[12][11]*dt*SF[3] + ins_state.Pk[14][11]*dt*SF[2] + ins_state.Pk[13][11]*dt*SPP[5]);
	   ins_state.Pk_k_1[6][11] = SPP[4]*(ins_state.Pk[6][11] + ins_state.Pk[3][11]*dt);
	   ins_state.Pk_k_1[7][11] = SPP[4]*(ins_state.Pk[7][11] + ins_state.Pk[4][11]*dt);
	   ins_state.Pk_k_1[8][11] = SPP[4]*(ins_state.Pk[8][11] + ins_state.Pk[5][11]*dt);
	   ins_state.Pk_k_1[9][11] = ins_state.Pk[9][11]*sq(SPP[4]);
	   ins_state.Pk_k_1[10][11] = ins_state.Pk[10][11]*sq(SPP[4]);
	   ins_state.Pk_k_1[11][11] = ins_state.Pk[11][11]*sq(SPP[4]);
	   ins_state.Pk_k_1[0][12] = SPP[3]*(ins_state.Pk[0][12] - ins_state.Pk[9][12]*dt*SF[0] + ins_state.Pk[10][12]*dt*SF[6] - ins_state.Pk[11][12]*dt*SF[4]);
	   ins_state.Pk_k_1[1][12] = SPP[3]*(ins_state.Pk[1][12] - ins_state.Pk[9][12]*dt*SF[5] - ins_state.Pk[10][12]*dt*SF[1] + ins_state.Pk[11][12]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][12] = SPP[3]*(ins_state.Pk[2][12] + ins_state.Pk[9][12]*dt*SF[3] - ins_state.Pk[11][12]*dt*SF[2] - ins_state.Pk[10][12]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][12] = SPP[3]*(ins_state.Pk[3][12] - ins_state.Pk[1][12]*SPP[1] + ins_state.Pk[2][12]*SPP[0] + ins_state.Pk[12][12]*dt*SF[0] - ins_state.Pk[13][12]*dt*SF[6] + ins_state.Pk[14][12]*dt*SF[4]);
	   ins_state.Pk_k_1[4][12] = SPP[3]*(ins_state.Pk[4][12] + ins_state.Pk[0][12]*SPP[1] - ins_state.Pk[2][12]*SPP[2] + ins_state.Pk[12][12]*dt*SF[5] + ins_state.Pk[13][12]*dt*SF[1] - ins_state.Pk[14][12]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][12] = SPP[3]*(ins_state.Pk[5][12] - ins_state.Pk[0][12]*SPP[0] + ins_state.Pk[1][12]*SPP[2] - ins_state.Pk[12][12]*dt*SF[3] + ins_state.Pk[14][12]*dt*SF[2] + ins_state.Pk[13][12]*dt*SPP[5]);
	   ins_state.Pk_k_1[6][12] = SPP[3]*(ins_state.Pk[6][12] + ins_state.Pk[3][12]*dt);
	   ins_state.Pk_k_1[7][12] = SPP[3]*(ins_state.Pk[7][12] + ins_state.Pk[4][12]*dt);
	   ins_state.Pk_k_1[8][12] = SPP[3]*(ins_state.Pk[8][12] + ins_state.Pk[5][12]*dt);
	   ins_state.Pk_k_1[9][12] = ins_state.Pk[9][12]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[10][12] = ins_state.Pk[10][12]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[11][12] = ins_state.Pk[11][12]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[12][12] = ins_state.Pk[12][12]*sq(SPP[3]);
	   ins_state.Pk_k_1[0][13] = SPP[3]*(ins_state.Pk[0][13] - ins_state.Pk[9][13]*dt*SF[0] + ins_state.Pk[10][13]*dt*SF[6] - ins_state.Pk[11][13]*dt*SF[4]);
	   ins_state.Pk_k_1[1][13] = SPP[3]*(ins_state.Pk[1][13] - ins_state.Pk[9][13]*dt*SF[5] - ins_state.Pk[10][13]*dt*SF[1] + ins_state.Pk[11][13]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][13] = SPP[3]*(ins_state.Pk[2][13] + ins_state.Pk[9][13]*dt*SF[3] - ins_state.Pk[11][13]*dt*SF[2] - ins_state.Pk[10][13]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][13] = SPP[3]*(ins_state.Pk[3][13] - ins_state.Pk[1][13]*SPP[1] + ins_state.Pk[2][13]*SPP[0] + ins_state.Pk[12][13]*dt*SF[0] - ins_state.Pk[13][13]*dt*SF[6] + ins_state.Pk[14][13]*dt*SF[4]);
	   ins_state.Pk_k_1[4][13] = SPP[3]*(ins_state.Pk[4][13] + ins_state.Pk[0][13]*SPP[1] - ins_state.Pk[2][13]*SPP[2] + ins_state.Pk[12][13]*dt*SF[5] + ins_state.Pk[13][13]*dt*SF[1] - ins_state.Pk[14][13]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][13] = SPP[3]*(ins_state.Pk[5][13] - ins_state.Pk[0][13]*SPP[0] + ins_state.Pk[1][13]*SPP[2] - ins_state.Pk[12][13]*dt*SF[3] + ins_state.Pk[14][13]*dt*SF[2] + ins_state.Pk[13][13]*dt*SPP[5]);
	   ins_state.Pk_k_1[6][13] = SPP[3]*(ins_state.Pk[6][13] + ins_state.Pk[3][13]*dt);
	   ins_state.Pk_k_1[7][13] = SPP[3]*(ins_state.Pk[7][13] + ins_state.Pk[4][13]*dt);
	   ins_state.Pk_k_1[8][13] = SPP[3]*(ins_state.Pk[8][13] + ins_state.Pk[5][13]*dt);
	   ins_state.Pk_k_1[9][13] = ins_state.Pk[9][13]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[10][13] = ins_state.Pk[10][13]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[11][13] = ins_state.Pk[11][13]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[12][13] = ins_state.Pk[12][13]*sq(SPP[3]);
	   ins_state.Pk_k_1[13][13] = ins_state.Pk[13][13]*sq(SPP[3]);
	   ins_state.Pk_k_1[0][14] = SPP[3]*(ins_state.Pk[0][14] - ins_state.Pk[9][14]*dt*SF[0] + ins_state.Pk[10][14]*dt*SF[6] - ins_state.Pk[11][14]*dt*SF[4]);
	   ins_state.Pk_k_1[1][14] = SPP[3]*(ins_state.Pk[1][14] - ins_state.Pk[9][14]*dt*SF[5] - ins_state.Pk[10][14]*dt*SF[1] + ins_state.Pk[11][14]*dt*SPP[6]);
	   ins_state.Pk_k_1[2][14] = SPP[3]*(ins_state.Pk[2][14] + ins_state.Pk[9][14]*dt*SF[3] - ins_state.Pk[11][14]*dt*SF[2] - ins_state.Pk[10][14]*dt*SPP[5]);
	   ins_state.Pk_k_1[3][14] = SPP[3]*(ins_state.Pk[3][14] - ins_state.Pk[1][14]*SPP[1] + ins_state.Pk[2][14]*SPP[0] + ins_state.Pk[12][14]*dt*SF[0] - ins_state.Pk[13][14]*dt*SF[6] + ins_state.Pk[14][14]*dt*SF[4]);
	   ins_state.Pk_k_1[4][14] = SPP[3]*(ins_state.Pk[4][14] + ins_state.Pk[0][14]*SPP[1] - ins_state.Pk[2][14]*SPP[2] + ins_state.Pk[12][14]*dt*SF[5] + ins_state.Pk[13][14]*dt*SF[1] - ins_state.Pk[14][14]*dt*SPP[6]);
	   ins_state.Pk_k_1[5][14] = SPP[3]*(ins_state.Pk[5][14] - ins_state.Pk[0][14]*SPP[0] + ins_state.Pk[1][14]*SPP[2] - ins_state.Pk[12][14]*dt*SF[3] + ins_state.Pk[14][14]*dt*SF[2] + ins_state.Pk[13][14]*dt*SPP[5]);
	   ins_state.Pk_k_1[6][14] = SPP[3]*(ins_state.Pk[6][14] + ins_state.Pk[3][14]*dt);
	   ins_state.Pk_k_1[7][14] = SPP[3]*(ins_state.Pk[7][14] + ins_state.Pk[4][14]*dt);
	   ins_state.Pk_k_1[8][14] = SPP[3]*(ins_state.Pk[8][14] + ins_state.Pk[5][14]*dt);
	   ins_state.Pk_k_1[9][14] =  ins_state.Pk[9][14]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[10][14] = ins_state.Pk[10][14]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[11][14] = ins_state.Pk[11][14]*SPP[3]*SPP[4];
	   ins_state.Pk_k_1[12][14] = ins_state.Pk[12][14]*sq(SPP[3]);
	   ins_state.Pk_k_1[13][14] = ins_state.Pk[13][14]*sq(SPP[3]);
	   ins_state.Pk_k_1[14][14] = ins_state.Pk[14][14]*sq(SPP[3]);

	   //--------------------------------------����������0ƫ�ͼӱ�0ƫ�Ĺ�������-----------------------------------------
	    ftype processNoise[15]; //������������ƫ������,Ҳ����һ�������Ʒ�Ĺ�������
	    processNoise[0] = 0;
	    processNoise[1] = 0;
	    processNoise[2] = 0;
	    processNoise[3] = 0;
	    processNoise[4] = 0;
	    processNoise[5] = 0;
	    processNoise[6] = 0;
	    processNoise[7] = 0;
	    processNoise[8] = 0;

	    //�����������������=��׼���ƽ��
	    ////Q=G*Q*G'�жԽ��߲���
	    processNoise[9] =   1.5*dAngBiasSigma*dAngBiasSigma;//�����������mrluo ����0ƫ���ȶ��ȱ�׼���ƽ��������1.5������δ��ģ����
	    processNoise[10] =  1.5*dAngBiasSigma*dAngBiasSigma;
	    processNoise[11] =  1.5*dAngBiasSigma*dAngBiasSigma;

	    processNoise[12] =  daccBiasSigma*daccBiasSigma; //��ƫ����mrlu���ӱ�����0ƫ���ȶ��ȱ�׼���ƽ��
	    processNoise[13] =  daccBiasSigma*daccBiasSigma;
	    processNoise[14] =  daccBiasSigma*daccBiasSigma;
	    //--------------------------------------������������0ƫ�ͼӱ�0ƫ�Ĺ�������-----------------------------------------
	   /*************mrluo����Ϊ�����ϵͳ��״̬���º�״̬һ��Ԥ������Э�������,matlab��ǰ�Ż���*******************/

	    //---------------------------------����Ϊ����ǿ���ٵĴ�λʧ׼�Ƕ�׼-------------------------------------
		int8_t i=0;
		int8_t j=0;

		// mrluo ��������ķ����С������Ϊ�����׼��ɣ���Ϊ��ʼ�������úܴ�
		// mrluo ע�������Ƕ�Pk�жϲ��Ƕ�Pk_k_1�ж�
        if (ins_state.Pk[2][2] < 0.005)
        {
        	ins_state.yaw_alig_complete = true;
        }

        //mrluo ����׼��ɺ�Pk��ʵ��ӳ��ǰ״̬�ķ���
        //mrluo �ٷ����ӱ�0ƫ
	    if (true == ins_state.yaw_alig_complete) //���duizhun��׼
	    {

	    	//mrluo Pkk-1������Pk������ᴦ��
	    	//mrluo Pkk-1�ǶԳƵģ�������ֱ�Ӹ��ƣ�������ֱ�ӿ��������Ǽ��ɣ����ұ�֤�˶Գ�
			 for(i=0;i<=14; i++)
			 {
			        for(j=0;j<=14;j++)
			        {
			            if (j <= i)
			            {
			            	ins_state.Pk[i][j] = ins_state.Pk_k_1[j][i];
			            }
			            else
			            {
			            	ins_state.Pk[i][j] = ins_state.Pk_k_1[i][j];
			            }
			        }
			 }

			 //mrluo ����P171,��ν������������PK��С����ѧ��KF�Ѿ���������PK��û�з�Ӧ��ʵ״̬�����
			 //mrluo �Ӷ�����������Ϣʧȥ��ϵͳ�ĵ���������
			 //mrluo ˮƽʧ׼������С��Ϊ���ֻ��ԣ���ֹ�����������ʶȷŴ󷽲����������Ϣ��ϵͳ���������ã��ﵽǿ����Ч��
			 //mrluo ���ں��򽫲���ǿ����
			 //mrluo 7.5e-5�����ֵ����EKF��������ʱ����ķ�������
			 if (ins_state.Pk[0][0] < 7.5e-5 )
			 {
			     for (i=0;i<=1;i++)
			     {
			    	 ins_state.Pk[i][i] = 1.00001*ins_state.Pk[i][i]; // �ʵ��Ŵ�ˮƽʧ׼�ǵķ�����ֻ���
			     }
			     for (i=2;i<=2;i++)
			     {
			    	 ins_state.Pk[i][i] = 1.000004*ins_state.Pk[i][i]; //�ʵ��Ŵ���ķ����ֹ���򸺶�
			     }

			 }

	    	for (i=9;i<=11;i++)//mrluo�ʵ��Ŵ�����0ƫ�ķ���
	    	{
	    		//ins_state.Pk[i][i] = 1.00002*ins_state.Pk[i][i]; //���ֻ���
	    	}

	    	for (i=12;i<=14;i++)//mrluo�ʵ��Ŵ�Ӽ�0ƫ�ķ���
	    	{
	    		//ins_state.Pk[i][i] = 1.00002*ins_state.Pk[i][i];
	    	}

	    	for (i=0;i<=14;i++)//mrluoһ�������ɷ���������ӵ�Pkk-1�Խ���Ԫ����
	    	{
	    		ins_state.Pk[i][i] = ins_state.Pk[i][i] + processNoise[i];
	    	}
	    }
	    else  // mrluo��׼û����ɣ���ʼ��׼�ڼ䲻���ǼӼƣ���Ϊ��ʼ��׼�ڼ�fn = gn��Vn=0,û���õ��ӱ�
	    {
			 for(i=0;i<=11; i++)
			 {
				    //mrluo Pkk-1������Pk������ᴦ��
				    //Pkk-1�ǶԳƵģ�������ֱ�ӿ��������Ǽ���
			        for(j=0;j<=11;j++)
			        {
			            if (j <= i)
			            {
			            	ins_state.Pk[i][j] = ins_state.Pk_k_1[j][i];
			            }
			            else
			            {
			            	ins_state.Pk[i][j] =ins_state.Pk_k_1[i][j];
			            }
			        }
			 }

	    	for (i=0;i<11;i++)//mrluo һ�������ɷ���������ӵ�Pkk-1�Խ���Ԫ����
	    	{
	    		ins_state.Pk[i][i] = ins_state.Pk[i][i] + processNoise[i];
	    	}
	    }

	    for (i=0;i<=14;i++)//mrluo Pkk-1�Խ���Ԫ�ش��ڵ���0
	    {
	        if (ins_state.Pk[i][i] < 0)
	        {
	        	ins_state.Pk[i][i] = 0;
	        }
	    }
	    //---------------------------------����Ϊ����ǿ���ٵĴ�λʧ׼�Ƕ�׼-------------------------------------
}