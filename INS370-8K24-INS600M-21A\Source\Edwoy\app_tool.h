#ifndef __APP_TOOL_H__
#define __APP_TOOL_H__

#include "pjt_glb_head.h"
#include "main.h"

///===Macro=====================================================================================================================
#define SIZE_256         ((uint32_t)0x00000100U)        // size of 256B
#define SIZE_512         ((uint32_t)0x00000200U)        // size of 512B
#define SIZE_1KB         ((uint32_t)0x00000400U)        // size of 1KB
#define SIZE_2KB         ((uint32_t)0x00000800U)        // size of 2KB
#define SIZE_4KB         ((uint32_t)0x00001000U)        // size of 4KB
//#define SIZE_16KB        ((uint32_t)0x00004000U)        /*!< size of 16KB*/
//#define SIZE_64KB        ((uint32_t)0x00010000U)        /*!< size of 64KB*/
//#define SIZE_128KB       ((uint32_t)0x00020000U)        /*!< size of 128KB*/
//#define SIZE_256KB       ((uint32_t)0x00040000U)        /*!< size of 256KB*/
#define SIZE_1MB         ((uint32_t)0x00100000U)       // size of 1MB

#define CALC_SIZE(a)     (sizeof(a)/sizeof(*(a)))
///===END Macro====================================================================================================END Macro====

int complement2original(int num);
int app_hd6089data_covert(int orix);
  
uint16_t  app_accum_verify_16bit(uc16_t* srcbuf,u32_t srcqty);
uint16_t  app_accum_verify_8bit(uc8_t* ,u32_t);
uint8_t  crc_verify_8bit(uc8_t* srcbuf,u32_t srcqty);

/*****************************************************************
 * \brief Accumulated value verification v2
 * \param srcbuf,
 * \param srcqty,
 * \return Accumulated value `s low 16bit
 * \update
       v1.0 ,2024.2.10, validating
******************************************************************/
uint16_t  app_accum_verify_16bit_v2(uvc16_t* srcbuf,u32_t srcqty);


	
#endif //__APP_TOOL_H__
////=================================================All End ============================================================================
