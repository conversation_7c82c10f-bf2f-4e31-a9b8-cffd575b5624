#ifndef _TYPEDEFINE_H
#define _TYPEDEFINE_H
#include "appmain.h"
/***********************************************************************************************************************************/
/*TYPEDEFINE.h                                                                                                                   */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*                                                                                                                             */
/*GNSSlocusGen.m                                                              */
/*                       */
/*******************************************************************************************************************************************/
typedef char INT8;                   //8INT8

typedef unsigned char UINT8;         //8UINT8

typedef short int INT16;             //16INT16

typedef unsigned short int UINT16;   //16UINT16
#ifndef INT32 
typedef long INT32; 				//32INT32
#endif
#ifndef UINT32 
typedef unsigned long UINT32;         //32UINT32
#endif
typedef UINT32 COUNT;     			//32COUNT

typedef UINT8 BOOL;                  //BOOL

typedef double TIME;                 //TIME

typedef UINT8 MODE;                  //MODE

typedef UINT8 u8;

typedef UINT8 CODE;                  //CODE

typedef UINT8 STATE;                 //STATE

typedef UINT8 PHASE;                 //PHASE

typedef INT32 IPARA;                 //IPARA

typedef float FPARA;                //FPARA

typedef double DPARA;                //DPARA

typedef double LATI;                 //LATI

typedef double LOGI;                 //LOGI

typedef double HEIGHT;               //HEIGHT

typedef double ATTI;                 //ATTI

typedef double MATR;               	//MATR

typedef double VEC;                  //VEC

typedef double SCAL;               	//SCAL

typedef double QUAT;                 //QUAT

typedef double VEL;                  //VEL

typedef double LEN;                  //LEN

typedef double ACCELER;              //ACCELER

typedef double ANGRATE;              //ANGRATE

typedef double DELANG;               //DELANG

typedef double DELVEL;               //DELVEL

typedef UINT32* ADDR;               //ADDR

typedef UINT32 UIRAW;               //

typedef double DRAW;                //
#endif
