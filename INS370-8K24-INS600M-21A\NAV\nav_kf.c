/*!
    \file  nav_kf.c
    \brief Navigation Kalman filter wrapper
    \note  This file provides wrapper for INAV Kalman filter
*/

#include "nav_kf.h"
#include "../INAV/FUNCTION.h"
#include "../INAV/GLOBALDATA.h"

/*!
    \brief      Initialize Kalman filter
    \param[in]  none
    \param[out] none
    \retval     nav_kf_status_t
*/
nav_kf_status_t nav_kf_init(void)
{
    /* Initialize INAV Kalman filter */
    Kalman_Init(&g_<PERSON><PERSON>);
    
    return NAV_KF_STATUS_OK;
}

/*!
    \brief      Update Kalman filter
    \param[in]  gnss_data: GNSS measurement data
    \param[out] none
    \retval     nav_kf_status_t
*/
nav_kf_status_t nav_kf_update(const nav_gnss_data_t *gnss_data)
{
    if (gnss_data == NULL) {
        return NAV_KF_STATUS_ERROR;
    }
    
    /* Update GNSS data for <PERSON>lman filter */
    if (gnss_data->valid) {
        g_GNSSData_In_Use.isPosEn = YES;
        g_GNSSData_In_Use.r_GNSSLati = gnss_data->latitude * D2R;
        g_GNSSData_In_Use.r_GNSSLogi = gnss_data->longitude * D2R;
        g_GNSSData_In_Use.GNSSHeight = gnss_data->altitude;
        
        for (int i = 0; i < 3; i++) {
            g_GNSSData_In_Use.GNSSVn[i] = gnss_data->velocity[i];
        }
        
        /* Run Kalman filter startup */
        Kalman_StartUp(&g_Kalman, &g_GNSSData_In_Use, &g_SysVar, &g_Navi);
        
        /* Run Kalman computation if started */
        if (g_Kalman.isKalmanStart == YES) {
            KalCompute(&g_GNSSData_In_Use, &g_Kalman);
            g_Kalman.isKalmanStart = NO;
        }
    } else {
        g_GNSSData_In_Use.isPosEn = NO;
    }
    
    return NAV_KF_STATUS_OK;
}

/*!
    \brief      Get Kalman filter state
    \param[in]  none
    \param[out] state: filter state vector
    \retval     nav_kf_status_t
*/
nav_kf_status_t nav_kf_get_state(nav_kf_state_t *state)
{
    if (state == NULL) {
        return NAV_KF_STATUS_ERROR;
    }
    
    /* Copy state from INAV Kalman filter */
    for (int i = 0; i < NAV_KF_STATE_DIM && i < DIM_STATE; i++) {
        state->x[i] = g_Kalman.Xk[i];
    }
    
    state->valid = (g_Kalman.isKalmanStart == YES);
    state->count = g_Kalman.Kal_Count;
    
    return NAV_KF_STATUS_OK;
}

/*!
    \brief      Reset Kalman filter
    \param[in]  none
    \param[out] none
    \retval     nav_kf_status_t
*/
nav_kf_status_t nav_kf_reset(void)
{
    /* Reset INAV Kalman filter */
    Kalman_Init(&g_Kalman);
    
    return NAV_KF_STATUS_OK;
}

/*!
    \brief      Set Kalman filter parameters
    \param[in]  params: filter parameters
    \param[out] none
    \retval     nav_kf_status_t
*/
nav_kf_status_t nav_kf_set_params(const nav_kf_params_t *params)
{
    if (params == NULL) {
        return NAV_KF_STATUS_ERROR;
    }
    
    /* Update filter parameters */
    /* Note: This is a simplified implementation */
    /* In practice, you would update the Q, R matrices etc. */
    
    return NAV_KF_STATUS_OK;
}

/*!
    \brief      Get Kalman filter parameters
    \param[in]  none
    \param[out] params: filter parameters
    \retval     nav_kf_status_t
*/
nav_kf_status_t nav_kf_get_params(nav_kf_params_t *params)
{
    if (params == NULL) {
        return NAV_KF_STATUS_ERROR;
    }
    
    /* Get filter parameters */
    /* Note: This is a simplified implementation */
    params->process_noise = 1e-6;
    params->measurement_noise = 1e-4;
    params->initial_uncertainty = 1e-2;
    
    return NAV_KF_STATUS_OK;
}
