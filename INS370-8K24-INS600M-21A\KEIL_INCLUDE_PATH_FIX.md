# Keil包含路径修复指南

## 🎯 问题诊断

### 当前错误分析
```
cannot open source input file "RTE_Components.h"  ← 找不到RTE_Components.h
cannot open source input file "board.h"           ← 找不到board.h  
cannot open source input file "app_tool.h"        ← 找不到app_tool.h
```

### 根本原因
- ✅ 文件已存在，但包含路径配置不正确
- ❌ sins2.c仍在项目中，有大量兼容性问题

## 🚀 完整解决方案

### 步骤1: 配置包含路径

#### 在Keil中添加以下包含路径：

1. **打开项目选项**：
   - 右键点击项目根节点 `INS_4000`
   - 选择 `Options for Target 'INS_4000'...`

2. **配置C/C++选项**：
   - 切换到 `C/C++` 标签页
   - 在 `Include Paths` 中添加以下路径：

```
.                                                    ← 项目根目录 (RTE_Components.h, board.h)
../INAV                                             ← INAV算法头文件
../Source/inc                                       ← Source头文件
../Source/Edwoy                                     ← app_tool.h位置
../Protocol                                         ← Protocol头文件
../Common/inc                                       ← Common头文件
../bsp/inc                                          ← BSP头文件
../Library/CMSIS                                    ← CMSIS库
../Library/GD32F4xx_standard_peripheral/Include    ← GD32标准库
../NAV                                              ← NAV头文件
../RTT                                              ← RTT头文件
```

### 步骤2: 移除有问题的文件

#### 移除sins2.c (关键步骤)
1. 在Project窗口中找到 `INAV` 组
2. 右键点击 `sins2.c`
3. 选择 `Remove File from Group`

**原因**: sins2.c有50+个兼容性错误，需要大量修改

### 步骤3: 验证文件位置

#### 确认关键文件存在：
```
✅ INS370-8K24-INS600M-21A/RTE_Components.h        ← 已创建
✅ INS370-8K24-INS600M-21A/board.h                 ← 已创建
✅ INS370-8K24-INS600M-21A/Source/Edwoy/app_tool.h ← 已存在
```

### 步骤4: 重新编译

1. **Clean项目**: `Project -> Clean Targets`
2. **Rebuild项目**: `Project -> Rebuild all target files`

## 📊 预期结果

### 移除sins2.c + 修复包含路径后：
- ❌ **修复前**: 80+个包含路径错误 + 30+个sins2.c错误
- ✅ **修复后**: 预计0-5个错误

### 应该看到的编译输出：
```
compiling systick.c...                 ← 不再报错找不到board.h
compiling fpgad.c...                   ← 不再报错找不到board.h
compiling protocol.c...                ← 不再报错找不到board.h
compiling SetParaBao.c...              ← 不再报错找不到app_tool.h
compiling navi.c...                    ← INAV算法开始编译
compiling kalman.c...                  ← Kalman滤波编译
compiling align.c...                   ← 对准算法编译
compiling sins.c...                    ← SINS算法编译
compiling ins.c...                     ← INS接口编译
...
0 Error(s), 0 Warning(s)               ← 编译成功
```

### 不应该看到的错误：
```
❌ cannot open source input file "RTE_Components.h"
❌ cannot open source input file "board.h"
❌ cannot open source input file "app_tool.h"
❌ sins2.c相关的任何错误
```

## 🔧 如果仍有问题

### 问题1: 包含路径仍然不正确
**解决**: 检查路径是否相对于项目文件位置正确

### 问题2: 某些头文件仍找不到
**解决**: 逐个检查文件是否存在，路径是否正确

### 问题3: 其他编译错误
**解决**: 查看具体错误信息，可能需要添加宏定义

## 💡 重要说明

### 为什么移除sins2.c？
1. **兼容性问题**: sins2.c是从HPM6750平台移植过来的，有大量GD32F4xx不兼容的代码
2. **功能重复**: sins.c已经提供完整的SINS算法功能
3. **快速解决**: 移除后可以立即解决大部分编译错误

### 核心功能保留
移除sins2.c后，系统仍具备：
- ✅ 完整的惯性导航算法
- ✅ Kalman滤波功能
- ✅ 初始对准算法
- ✅ 传感器融合算法
- ✅ 所有其他INAV功能

### 后续优化
如果确实需要sins2.c的特定功能：
1. 可以后续进行详细的兼容性修复
2. 或者寻找GD32F4xx平台的对应实现

## 📋 操作检查清单

### 必须完成的操作：
- [ ] 添加项目根目录 `.` 到包含路径
- [ ] 添加 `../Source/Edwoy` 到包含路径
- [ ] 添加其他必要的包含路径
- [ ] 移除 `sins2.c` 文件
- [ ] Clean + Rebuild项目

### 验证成功的标志：
- [ ] 编译输出中看到INAV文件被编译
- [ ] 没有"找不到头文件"的错误
- [ ] 没有sins2.c相关的错误
- [ ] 编译成功或错误数量大幅减少

完成这些步骤后，项目应该能够成功编译，或者至少错误数量会从80+个减少到5个以内。
