/*****************************************************文件说明******************************************************************************/
/*文件名称：TEST_INAV.C                                                                                                                    */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                                          */
/*编写人：                                                                                                                                 */
/*包含文件：                                                                                                                               */
/*测试用例：                                                                                                                               */
/*说明：本文件为INAV算法的测试程序，用于验证移植的算法是否正常工作                                                                        */
/*******************************************************************************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>

// 测试模式下的简化定义
#ifdef TEST_MODE

// 基本数据类型定义
typedef double DPARA;
typedef float SPARA;
typedef int IPARA;
typedef unsigned int UPARA;
typedef char BOOL;
typedef unsigned char BYTE;
typedef unsigned short WORD;
typedef unsigned int DWORD;

#define YES 1
#define NO 0
#define TRUE 1
#define FALSE 0

// 数学常数
#define PI 3.141592653589793
#define D2R (PI/180.0)
#define R2D (180.0/PI)

// 简化的PAOCHE_FRAME_STRUCT结构体
typedef struct {
    double latitude;
    double longitude;
    double height;
    double velocity_n;
    double velocity_e;
    double velocity_u;
    double heading;
    int valid;
} PAOCHE_FRAME_STRUCT;

// 包含INAV头文件
#include "CONST.h"
#include "TYPEDEFINE.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"

// 测试数据生成函数
void generate_test_imu_data(DPARA gyro[3], DPARA acc[3], double time)
{
    // 生成模拟的IMU数据
    // 陀螺仪数据 (rad/s) - 模拟静止状态下的地球自转
    gyro[0] = 0.0001 * sin(time * 0.1);  // X轴
    gyro[1] = 0.0001 * cos(time * 0.1);  // Y轴  
    gyro[2] = 0.000072921 * cos(30.0 * D2R); // Z轴 - 地球自转分量
    
    // 加速度计数据 (m/s²) - 模拟重力加速度
    acc[0] = 0.01 * sin(time * 0.05);    // X轴噪声
    acc[1] = 0.01 * cos(time * 0.05);    // Y轴噪声
    acc[2] = -9.8 + 0.01 * sin(time * 0.03); // Z轴重力
}

void generate_test_gnss_data(PAOCHE_FRAME_STRUCT* gnss, double time)
{
    // 生成模拟的GNSS数据
    gnss->latitude = 30.0 + 0.0001 * sin(time * 0.01);  // 纬度 (度)
    gnss->longitude = 120.0 + 0.0001 * cos(time * 0.01); // 经度 (度)
    gnss->height = 100.0 + 1.0 * sin(time * 0.02);       // 高度 (米)
    gnss->velocity_n = 0.1 * sin(time * 0.005);          // 北向速度
    gnss->velocity_e = 0.1 * cos(time * 0.005);          // 东向速度
    gnss->velocity_u = 0.05 * sin(time * 0.008);         // 天向速度
    gnss->heading = 45.0 + 5.0 * sin(time * 0.003);      // 航向角 (度)
    gnss->valid = (((int)time) % 10 < 8) ? 1 : 0;        // 80%的时间有效
}

void print_navigation_result(p_Navi navi, double time)
{
    printf("时间: %.2f秒\n", time);
    printf("位置: 纬度=%.6f°, 经度=%.6f°, 高度=%.2fm\n", 
           navi->d_Lati, navi->d_Logi, navi->Height);
    printf("速度: Vn=%.3fm/s, Ve=%.3fm/s, Vu=%.3fm/s\n", 
           navi->Vn[0], navi->Vn[1], navi->Vn[2]);
    printf("姿态: 航向=%.2f°, 俯仰=%.2f°, 横滚=%.2f°\n", 
           navi->d_Atti[0], navi->d_Atti[1], navi->d_Atti[2]);
    printf("工作状态: %d\n", g_SysVar.WorkPhase);
    printf("----------------------------------------\n");
}

int main(void)
{
    printf("=== INAV算法测试程序 ===\n");
    printf("开始测试移植的INAV算法...\n\n");
    
    // 初始化INAV算法
    printf("1. 初始化INAV算法...\n");
    INAV_Init();
    
    // 设置初始位置 (上海地区)
    INAV_SetInitialPosition(31.2304, 121.4737, 50.0);
    printf("设置初始位置: 纬度=31.2304°, 经度=121.4737°, 高度=50.0m\n\n");
    
    // 模拟运行
    printf("2. 开始模拟运行...\n");
    double simulation_time = 0.0;
    double dt = 0.01; // 10ms周期
    int print_counter = 0;
    
    for(int i = 0; i < 10000; i++) // 运行100秒
    {
        DPARA gyro[3], acc[3];
        PAOCHE_FRAME_STRUCT gnss_data;
        PAOCHE_FRAME_STRUCT* gnss_ptr = NULL;
        
        // 生成测试数据
        generate_test_imu_data(gyro, acc, simulation_time);
        
        // 每秒生成一次GNSS数据
        if(i % 100 == 0)
        {
            generate_test_gnss_data(&gnss_data, simulation_time);
            gnss_ptr = &gnss_data;
        }
        
        // 运行INAV算法
        INAV_Process(gyro, acc, gnss_ptr);
        
        // 每5秒打印一次结果
        if(i % 500 == 0)
        {
            p_Navi navi = INAV_GetNaviResult();
            print_navigation_result(navi, simulation_time);
        }
        
        simulation_time += dt;
    }
    
    printf("3. 测试完成!\n");
    printf("最终导航结果:\n");
    p_Navi final_navi = INAV_GetNaviResult();
    print_navigation_result(final_navi, simulation_time);
    
    p_SysVar sys_status = INAV_GetSystemStatus();
    printf("系统状态信息:\n");
    printf("工作阶段: %d\n", sys_status->WorkPhase);
    printf("GNSS有效: %s\n", sys_status->isGNSSValid ? "是" : "否");
    printf("系统运行时间: %.2f秒\n", sys_status->Time);
    
    printf("\n=== 测试结束 ===\n");
    
    return 0;
}

#else
// 非测试模式下的空实现
int main(void)
{
    printf("请在测试模式下编译运行此程序\n");
    return 0;
}
#endif
