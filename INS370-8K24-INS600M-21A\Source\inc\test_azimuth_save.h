/***********************************************************************************
航向角掉电保存功能测试程序头文件
All rights reserved I-NAV 2023 2033
***********************************************************************************/

#ifndef _TEST_AZIMUTH_SAVE_H_
#define _TEST_AZIMUTH_SAVE_H_

#include <stdint.h>

// 测试函数声明
void run_azimuth_save_tests(void);
void print_azimuth_debug_info(void);

// 单独的测试函数
void test_azimuth_save_restore(void);
void test_azimuth_validation(void);
void test_azimuth_multiple_saves(void);
void test_azimuth_counter(void);
void test_system_restart_simulation(void);

// 辅助函数
void print_test_result(const char* test_name, int passed);

#endif // _TEST_AZIMUTH_SAVE_H_
