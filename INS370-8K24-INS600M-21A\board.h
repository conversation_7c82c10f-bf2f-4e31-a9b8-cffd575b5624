/*!
    \file    board.h
    \brief   Board configuration header file for GD32F4xx platform
    \note    Platform adapter from HPM6750 to GD32F4xx
*/

#ifndef __BOARD_H
#define __BOARD_H

#ifdef __cplusplus
extern "C" {
#endif

/* Include GD32F4xx standard library */
#include "gd32f4xx.h"
#include "gd32f4xx_libopt.h"

/* Include BSP headers */
#include "bsp_gpio.h"
#include "bsp_uart.h"
#include "bsp_tim.h"
#include "bsp_fmc.h"
#include "bsp_can.h"
#include "bsp_i2c.h"
#include "bsp_rtc.h"
#include "bsp_sys.h"
#include "bsp_adc.h"
#include "bsp_exti.h"
#include "bsp_flash.h"
#include "bsp_fwdgt.h"

/* Include platform adapter */
#include "platform_adapter.h"

/* System configuration */
#define SYSTEM_CLOCK_FREQ       240000000U  /* 240MHz */
#define APB1_CLOCK_FREQ         60000000U   /* 60MHz */
#define APB2_CLOCK_FREQ         120000000U  /* 120MHz */

/* Board LED definitions */
#define LED0_PIN                GPIO_PIN_6
#define LED0_GPIO_PORT          GPIOF
#define LED0_GPIO_CLK           RCU_GPIOF

#define LED1_PIN                GPIO_PIN_7
#define LED1_GPIO_PORT          GPIOF
#define LED1_GPIO_CLK           RCU_GPIOF

#define LED2_PIN                GPIO_PIN_8
#define LED2_GPIO_PORT          GPIOF
#define LED2_GPIO_CLK           RCU_GPIOF

#define LED3_PIN                GPIO_PIN_9
#define LED3_GPIO_PORT          GPIOF
#define LED3_GPIO_CLK           RCU_GPIOF

/* Board key definitions */
#define KEY_TAMPER_PIN          GPIO_PIN_13
#define KEY_TAMPER_GPIO_PORT    GPIOC
#define KEY_TAMPER_GPIO_CLK     RCU_GPIOC
#define KEY_TAMPER_EXTI_LINE    EXTI_13
#define KEY_TAMPER_EXTI_PORT    EXTI_SOURCE_GPIOC
#define KEY_TAMPER_EXTI_PIN     EXTI_SOURCE_PIN13
#define KEY_TAMPER_EXTI_IRQn    EXTI10_15_IRQn

#define KEY_WAKEUP_PIN          GPIO_PIN_0
#define KEY_WAKEUP_GPIO_PORT    GPIOA
#define KEY_WAKEUP_GPIO_CLK     RCU_GPIOA
#define KEY_WAKEUP_EXTI_LINE    EXTI_0
#define KEY_WAKEUP_EXTI_PORT    EXTI_SOURCE_GPIOA
#define KEY_WAKEUP_EXTI_PIN     EXTI_SOURCE_PIN0
#define KEY_WAKEUP_EXTI_IRQn    EXTI0_IRQn

#define KEY_USER_PIN            GPIO_PIN_14
#define KEY_USER_GPIO_PORT      GPIOB
#define KEY_USER_GPIO_CLK       RCU_GPIOB
#define KEY_USER_EXTI_LINE      EXTI_14
#define KEY_USER_EXTI_PORT      EXTI_SOURCE_GPIOB
#define KEY_USER_EXTI_PIN       EXTI_SOURCE_PIN14
#define KEY_USER_EXTI_IRQn      EXTI10_15_IRQn

/* UART definitions */
#define EVAL_COM0               USART0
#define EVAL_COM0_CLK           RCU_USART0
#define EVAL_COM0_TX_PIN        GPIO_PIN_9
#define EVAL_COM0_RX_PIN        GPIO_PIN_10
#define EVAL_COM0_GPIO_PORT     GPIOA
#define EVAL_COM0_GPIO_CLK      RCU_GPIOA
#define EVAL_COM0_AF            GPIO_AF_7

#define EVAL_COM1               USART1
#define EVAL_COM1_CLK           RCU_USART1
#define EVAL_COM1_TX_PIN        GPIO_PIN_2
#define EVAL_COM1_RX_PIN        GPIO_PIN_3
#define EVAL_COM1_GPIO_PORT     GPIOA
#define EVAL_COM1_GPIO_CLK      RCU_GPIOA
#define EVAL_COM1_AF            GPIO_AF_7

/* SPI definitions */
#define SPI0_SCK_PIN            GPIO_PIN_5
#define SPI0_MISO_PIN           GPIO_PIN_6
#define SPI0_MOSI_PIN           GPIO_PIN_7
#define SPI0_GPIO_PORT          GPIOA
#define SPI0_GPIO_CLK           RCU_GPIOA
#define SPI0_AF                 GPIO_AF_5

/* I2C definitions */
#define I2C0_SCL_PIN            GPIO_PIN_6
#define I2C0_SDA_PIN            GPIO_PIN_7
#define I2C0_GPIO_PORT          GPIOB
#define I2C0_GPIO_CLK           RCU_GPIOB
#define I2C0_AF                 GPIO_AF_4

/* CAN definitions */
#define CAN0_RX_PIN             GPIO_PIN_0
#define CAN0_TX_PIN             GPIO_PIN_1
#define CAN0_GPIO_PORT          GPIOD
#define CAN0_GPIO_CLK           RCU_GPIOD
#define CAN0_AF                 GPIO_AF_9

/* ADC definitions */
#define ADC_CHANNEL_0           ADC_CHANNEL_0
#define ADC_CHANNEL_1           ADC_CHANNEL_1
#define ADC_CHANNEL_2           ADC_CHANNEL_2
#define ADC_CHANNEL_3           ADC_CHANNEL_3

/* Timer definitions */
#define TIMER_BASIC             TIMER5
#define TIMER_BASIC_CLK         RCU_TIMER5
#define TIMER_BASIC_IRQn        TIMER5_IRQn

/* Memory definitions */
#define SRAM_BASE               0x20000000U
#define SRAM_SIZE               0x00050000U  /* 320KB */
#define FLASH_BASE              0x08000000U
#define FLASH_SIZE              0x00300000U  /* 3MB */

/* External memory definitions */
#define EXMC_BANK0_ADDR         0x60000000U
#define EXMC_BANK1_ADDR         0x64000000U
#define EXMC_BANK2_ADDR         0x68000000U
#define EXMC_BANK3_ADDR         0x6C000000U

/* Function declarations */
void board_init(void);
void board_led_init(void);
void board_key_init(void);
void board_uart_init(void);
void board_timer_init(void);

/* LED control functions */
void board_led_on(uint8_t led_num);
void board_led_off(uint8_t led_num);
void board_led_toggle(uint8_t led_num);

/* Key functions */
uint8_t board_key_state_get(uint8_t key_num);

/* Delay functions */
void board_delay_ms(uint32_t ms);
void board_delay_us(uint32_t us);

#ifdef __cplusplus
}
#endif

#endif /* __BOARD_H */
