/*!
    \file  nav_imu.c
    \brief Navigation IMU interface
*/

#include "nav_imu.h"

nav_imu_status_t nav_imu_init(void)
{
    return NAV_IMU_STATUS_OK;
}

nav_imu_status_t nav_imu_calibrate(nav_imu_calib_t *calib)
{
    (void)calib;
    return NAV_IMU_STATUS_OK;
}

nav_imu_status_t nav_imu_process(const nav_imu_raw_t *raw, nav_imu_data_t *processed)
{
    if (raw == NULL || processed == NULL) {
        return NAV_IMU_STATUS_ERROR;
    }
    
    // Simple pass-through for now
    for (int i = 0; i < 3; i++) {
        processed->gyro[i] = raw->gyro[i];
        processed->acc[i] = raw->acc[i];
    }
    processed->timestamp = raw->timestamp;
    processed->valid = raw->valid;
    
    return NAV_IMU_STATUS_OK;
}
