/***********************************************************************************
nav imu module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-17          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
#include "SetParaBao.h"  // 包含航向角保存功能的声明

unsigned char Gyro_data_check(_NAV_Data_Full_t* NAV_Data_Full_p);
unsigned char Acc_data_check(_NAV_Data_Full_t* NAV_Data_Full_p);
unsigned char install_data_check(_NAV_Data_Full_t* NAV_Data_Full_p);

/******************************************************************************
*原  型：void Get_Param_Data(Param_Data_t* Param_Data,CombineDataTypeDef Sensors_Data_Rew)
*功  能：获取配置参数
*输  入：无
*输  出：无
*******************************************************************************/
void Get_Param_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	unsigned short i= 0;
	{
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->Param.gnssArmLength[i] = CombineData_p->Param.gnssArmLength[i];
			NAV_Data_Full_p->Param.gnssAtt_from_vehicle[i] = CombineData_p->Param.gnssAtt_from_vehicle[i];//*****
		//	NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[i] = combineData.Adj.gnssAtt_from_vehicle2[i];//*****
			NAV_Data_Full_p->Param.OD_ArmLength[i] = CombineData_p->Param.OBArmLength[i];
			NAV_Data_Full_p->Param.OD_Att_from_vehicle[i] = CombineData_p->Param.OBAtt_from_vehicle[i];//车底盘相对IMU的安装角度
			
			NAV_Data_Full_p->Param.wb_set[i] = CombineData_p->Param.wb_set[i];
		}
		NAV_Data_Full_p->imuSelect = CombineData_p->imuSelect;
		NAV_Data_Full_p->debug = CombineData_p->debug;//1;//
		NAV_Data_Full_p->memsType = CombineData_p->memsType;
 }
	//默认采用GPS融合，一旦rtk非固定，采用轮速融合或运动模型约束
	//NAV_Data_Full_p->KF.use_gps_flag=CombineData_p->fusion;
	NAV_Data_Full_p->KF.use_gps_flag=E_FUNSION_GPS;
#if 0	
	if(CombineData_p->fusion == 0)
	{
		NAV_Data_Full_p->KF.use_gps_flag = 1;
	}
	else if(CombineData_p->fusion == 1 )
	{
		NAV_Data_Full_p->KF.use_gps_flag = 3;
	}
	else if(CombineData_p->fusion == 2)
	{
		NAV_Data_Full_p->KF.use_gps_flag = 2;
	}	
#endif

	//安装参数
//	if(RETURN_FAIL == install_data_check(&NAV_Data_Full))
//	{
//		//SetNavStatus(E_NAV_STATUS_START);		
//	}

}
/******************************************************************************
*原  型：void Load_Standard_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
*功  能：加载标定参数
*输  入：无
*输  出：无
*******************************************************************************/
void Load_Standard_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	//标定参数
	{
		  SetNavStandardFlag(CombineData_p->Adj.Nav_Standard_flag);
		//NAV_Data_Full_p->Nav_Standard_flag = CombineData_p->Adj.Nav_Standard_flag;
		if(CombineData_p->Adj.Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
		{
			NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[0] = CombineData_p->Adj.gnssAtt_from_vehicle2[0];//天线安装角精准误差，单位度
			NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[1] = CombineData_p->Adj.gnssAtt_from_vehicle2[1];
			NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2] = CombineData_p->Adj.gnssAtt_from_vehicle2[2];
			NAV_Data_Full_p->Param.acc_off[0] = CombineData_p->Adj.acc_off[0];
			NAV_Data_Full_p->Param.acc_off[1] = CombineData_p->Adj.acc_off[1];
			NAV_Data_Full_p->Param.acc_off[2] = CombineData_p->Adj.acc_off[2];
			NAV_Data_Full_p->Param.gyro_off[0] = CombineData_p->Adj.gyro_off[0];
			NAV_Data_Full_p->Param.gyro_off[1] = CombineData_p->Adj.gyro_off[1];
			NAV_Data_Full_p->Param.gyro_off[2] = CombineData_p->Adj.gyro_off[2];
//			NAV_Data_Full_p->ODS.att_ods2_b_filte[2] = combineData.Adj.att_ods2_b_filte_2;
//			NAV_Data_Full_p->ODS.att_ods2_b_filte[0] = combineData.Adj.att_ods2_b_filte_2_x;
			//********
			NAV_Data_Full_p->ODS.att_ods2_b_filte[2] = combineData.Adj.att_ods2b_filter_deg[2];//要确定里面存在从flash中读取的数据！！！
			NAV_Data_Full_p->ODS.att_ods2_b_filte[1] = combineData.Adj.att_ods2b_filter_deg[1];
			NAV_Data_Full_p->ODS.att_ods2_b_filte[0] = combineData.Adj.att_ods2b_filter_deg[0];
		}
		
	}
	//获取bias
	int i=0;
			if(NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
		{
			 for (i =0;i<3;i++)
			  {
							NAV_Data_Full_p->SINS.eb[i] = NAV_Data_Full_p->Param.gyro_off[i];
			    NAV_Data_Full_p->SINS.db[i] = NAV_Data_Full_p->Param.acc_off[i];
					}
					
		}
		else
		{
		 	for (i = 0; i < 3; i++)
				{
					NAV_Data_Full_p->SINS.eb[i] = 0.0;
					NAV_Data_Full_p->SINS.db[i] = 0.0;
				}
		}

}
#if 0
void Save_Standard_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	//标定参数
	{
		CombineData_p->Adj.Nav_Standard_flag		=	E_NAV_STANDARD_PROCCSSED;
		CombineData_p->Adj.gnssAtt_from_vehicle2[2] = 	NAV_Data_Full_p->Param.gnssAtt_from_vehicle2[2];
		CombineData_p->Adj.att_ods2_b_filte_2		=	NAV_Data_Full_p->ODS.att_ods2_b_filte[2];
	}
}
#endif

#if 0
void CalGPS2IMUDiffTime(_NAV_Data_Full_t* NAV_Data_Full_p,unsigned short ppsDelay,float gps_deltaT,float imu_deltaT)
{
	//NAV_Data_Full_p->GPS.delay_pps = (ppsDelay - gps_deltaT + imu_deltaT)*0.001;
	//NAV_Data_Full_p->GPS.delay_pps = 0.0;
	NAV_Data_Full_p->GPS.delay_pps = ppsDelay*0.001;
	//NAV_Data_Full_p->GPS.delay_pps=(imu_deltaT - gps_deltaT )*0.01+ppsDelay*0.001;
}
#endif

//去除陀螺仪，加计零偏
void InitZeroOffsetCompenstation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	NAV_Data_Full_p->IMU.gyro_use[0]	-=	NAV_Data_Full_p->Param.gyro_off[0];
	NAV_Data_Full_p->IMU.gyro_use[1]	-=	NAV_Data_Full_p->Param.gyro_off[1];
	NAV_Data_Full_p->IMU.gyro_use[2]	-=	NAV_Data_Full_p->Param.gyro_off[2];
	NAV_Data_Full_p->IMU.acc_use[0] 	-=	NAV_Data_Full_p->Param.acc_off[0];
	NAV_Data_Full_p->IMU.acc_use[1]		-=	NAV_Data_Full_p->Param.acc_off[1];
	NAV_Data_Full_p->IMU.acc_use[2]		-= 	NAV_Data_Full_p->Param.acc_off[2];
}

/******************************************************************************
*原  型：void Get_IMU_Data(void)
*功  能：传感器数据获取（输入）
*输  入：无
*输  出：无
*******************************************************************************/
void Get_IMU_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	unsigned short i = 0;
	static double gyro_fog_old = 0;
	double fog_temp = 0;
	double tempax,tempay,tempaz,tempgx,tempgy,tempgz;
	//float  imu_detlaT=0.0;
	//float  gps_detlaT=0.0;
	//NAV_Data_Full_p->IMU_Cnt++;
	//IMU
	{
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->IMU.gyro_use_pre[i] = NAV_Data_Full_p->IMU.gyro_use[i];
			NAV_Data_Full_p->IMU.acc_use_pre[i] = NAV_Data_Full_p->IMU.acc_use[i];
		}
		for(i = 0;i<3;i++)
		{
			NAV_Data_Full_p->IMU.gyro_fog_raw[i] = CombineData_p->ifogInfo.gyroGrp[i];
		}		
		NAV_Data_Full_p->IMU.temp_fog_raw  = CombineData_p->ifogInfo.sensorTemp;
		
		
		if(E_IMU_MANU_460 == NAV_Data_Full_p->memsType)
		{
			for(i = 0;i<3;i++)
			{
				NAV_Data_Full_p->IMU.gyro_raw[i] = CombineData_p->imuInfo.gyroGrp[i];
				NAV_Data_Full_p->IMU.acc_raw[i] = CombineData_p->imuInfo.accelGrp[i]*g0;
			}		
			NAV_Data_Full_p->IMU.temp_mems_raw = CombineData_p->imuInfo.sensorTemp;	
			//imu_detlaT = CombineData_p->imuInfo.deltaT;
		}
		else if(E_IMU_MANU_SCHA63X== NAV_Data_Full_p->memsType)
		{
			NAV_Data_Full_p->IMU.gyro_raw[0] = CombineData_p->scha634Info.gyro_x;
			NAV_Data_Full_p->IMU.gyro_raw[1] = CombineData_p->scha634Info.gyro_y;
			NAV_Data_Full_p->IMU.gyro_raw[2] = CombineData_p->scha634Info.gyro_z;
			
			NAV_Data_Full_p->IMU.acc_raw[0] = CombineData_p->scha634Info.acc_x*G0;
			NAV_Data_Full_p->IMU.acc_raw[1] = CombineData_p->scha634Info.acc_y*G0;
			NAV_Data_Full_p->IMU.acc_raw[2] = CombineData_p->scha634Info.acc_z*G0;	
			NAV_Data_Full_p->IMU.temp_mems_raw = CombineData_p->scha634Info.temp_due;	
			//imu_detlaT=CombineData_p->scha634Info.deltaT;
		}
		else if(E_IMU_MANU_ADIS16465== NAV_Data_Full_p->memsType)
		{
			NAV_Data_Full_p->IMU.gyro_raw[0] = CombineData_p->adis16465Info.gyro_x;
			NAV_Data_Full_p->IMU.gyro_raw[1] = CombineData_p->adis16465Info.gyro_y;
			NAV_Data_Full_p->IMU.gyro_raw[2] = CombineData_p->adis16465Info.gyro_z;
			
			NAV_Data_Full_p->IMU.acc_raw[0] = CombineData_p->adis16465Info.acc_x*G0;//*g0;	
			NAV_Data_Full_p->IMU.acc_raw[1] = CombineData_p->adis16465Info.acc_y*G0;//*g0;	
			NAV_Data_Full_p->IMU.acc_raw[2] = CombineData_p->adis16465Info.acc_z*G0;//*g0;				
			NAV_Data_Full_p->IMU.temp_mems_raw = CombineData_p->adis16465Info.temp;	
			//imu_detlaT=CombineData_p->adis16465Info.deltaT;
		}
		else if(E_IMU_MANU_EPSON_G370== NAV_Data_Full_p->memsType)
		{
			NAV_Data_Full_p->IMU.gyro_raw[0] = CombineData_p->epsonInfo.gyro_x;
			NAV_Data_Full_p->IMU.gyro_raw[1] = CombineData_p->epsonInfo.gyro_y;
			NAV_Data_Full_p->IMU.gyro_raw[2] = CombineData_p->epsonInfo.gyro_z;
			
			NAV_Data_Full_p->IMU.acc_raw[0] = CombineData_p->epsonInfo.acc_x*g0;	
			NAV_Data_Full_p->IMU.acc_raw[1] = CombineData_p->epsonInfo.acc_y*g0;	
			NAV_Data_Full_p->IMU.acc_raw[2] = CombineData_p->epsonInfo.acc_z*g0;				
			NAV_Data_Full_p->IMU.temp_mems_raw = CombineData_p->epsonInfo.temp;
			//imu_detlaT=CombineData_p->epsonInfo.deltaT;
		}
		
		
		if(E_IMU_MANU_460== NAV_Data_Full_p->memsType)//精准460
		{
			NAV_Data_Full_p->IMU.gyro[0] = -NAV_Data_Full_p->IMU.gyro_raw[1];	
			NAV_Data_Full_p->IMU.gyro[1] = -NAV_Data_Full_p->IMU.gyro_raw[0];	
			NAV_Data_Full_p->IMU.gyro[2] = -NAV_Data_Full_p->IMU.gyro_raw[2];	
			NAV_Data_Full_p->IMU.acc[0] = -NAV_Data_Full_p->IMU.acc_raw[1];	
			NAV_Data_Full_p->IMU.acc[1] = -NAV_Data_Full_p->IMU.acc_raw[0];	
			NAV_Data_Full_p->IMU.acc[2] = -NAV_Data_Full_p->IMU.acc_raw[2];

			NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;
		}
		else if(E_IMU_MANU_SCHA63X == NAV_Data_Full_p->memsType)//村田	
		{
			NAV_Data_Full_p->IMU.gyro[0] = NAV_Data_Full_p->IMU.gyro_raw[1];	
			NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[0];	
			NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	
			NAV_Data_Full_p->IMU.acc[0] = NAV_Data_Full_p->IMU.acc_raw[1];	
			NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[0];	
			NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];

			NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;			
		}
		else if(E_IMU_MANU_ADIS16465 == NAV_Data_Full_p->memsType)
		{
			NAV_Data_Full_p->IMU.gyro[0] = -NAV_Data_Full_p->IMU.gyro_raw[1];	
			NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[0];	
			NAV_Data_Full_p->IMU.gyro[2] = NAV_Data_Full_p->IMU.gyro_raw[2];	
			NAV_Data_Full_p->IMU.acc[0] = -NAV_Data_Full_p->IMU.acc_raw[1];	
			NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[0];	
			NAV_Data_Full_p->IMU.acc[2] = NAV_Data_Full_p->IMU.acc_raw[2];

			NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;			
		}
		else if(E_IMU_MANU_EPSON_G370 == NAV_Data_Full_p->memsType)
		{
			NAV_Data_Full_p->IMU.gyro[0] = -NAV_Data_Full_p->IMU.gyro_raw[0];	
			NAV_Data_Full_p->IMU.gyro[1] = NAV_Data_Full_p->IMU.gyro_raw[1];	
			NAV_Data_Full_p->IMU.gyro[2] = -NAV_Data_Full_p->IMU.gyro_raw[2];	
			NAV_Data_Full_p->IMU.acc[0] = -NAV_Data_Full_p->IMU.acc_raw[0];	
			NAV_Data_Full_p->IMU.acc[1] = NAV_Data_Full_p->IMU.acc_raw[1];	
			NAV_Data_Full_p->IMU.acc[2] = -NAV_Data_Full_p->IMU.acc_raw[2];

			NAV_Data_Full_p->IMU.temp_mems = NAV_Data_Full_p->IMU.temp_mems_raw;			
		}
		
	
		NAV_Data_Full_p->IMU.gyro_fog[0] = NAV_Data_Full_p->IMU.gyro_fog_raw[0];	
		NAV_Data_Full_p->IMU.gyro_fog[1] = NAV_Data_Full_p->IMU.gyro_fog_raw[1];	
		NAV_Data_Full_p->IMU.gyro_fog[2] = NAV_Data_Full_p->IMU.gyro_fog_raw[2];
		//NAV_Data_Full_p->IMU.gyro_fog[2] = NAV_Data_Full_p->IMU.gyro_fog_raw[2]/(1+ fog_dk_filte);			
        NAV_Data_Full_p->IMU.temp_fog = NAV_Data_Full_p->IMU.temp_fog_raw;	
	   
        //NAV_USE_FOG
		if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)
		{	
			NAV_Data_Full_p->IMU.gyro_use[0] = NAV_Data_Full_p->IMU.gyro[0]*DEG2RAD;	
			NAV_Data_Full_p->IMU.gyro_use[1] = NAV_Data_Full_p->IMU.gyro[1]*DEG2RAD;	
			NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->IMU.gyro_fog[2]*DEG2RAD;
			NAV_Data_Full_p->IMU.acc_use[0] = NAV_Data_Full_p->IMU.acc[0];	
			NAV_Data_Full_p->IMU.acc_use[1] = NAV_Data_Full_p->IMU.acc[1];	
			NAV_Data_Full_p->IMU.acc_use[2] = NAV_Data_Full_p->IMU.acc[2];
			NAV_Data_Full_p->IMU.temp_use = NAV_Data_Full_p->IMU.temp_fog;
			
			fog_temp = NAV_Data_Full_p->IMU.gyro_fog[2]*DEG2RAD;
			if (fabs(fog_temp-gyro_fog_old)*RAD2DEG>50)
			{
				NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->IMU.gyro_pre[2];
				gyro_fog_old = NAV_Data_Full_p->IMU.gyro_use[2];
			}
			else
			{
				NAV_Data_Full_p->IMU.gyro_use[2] = fog_temp;
				gyro_fog_old = NAV_Data_Full_p->IMU.gyro_use[2];
			}		
		}
		else
		{
//			NAV_Data_Full_p->IMU.gyro_use[0] = NAV_Data_Full_p->IMU.gyro[0]*DEG2RAD - NAV_Data_Full_p->Param.gyro_off[0];
//			NAV_Data_Full_p->IMU.gyro_use[1] = NAV_Data_Full_p->IMU.gyro[1]*DEG2RAD - NAV_Data_Full_p->Param.gyro_off[1];
//			NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->IMU.gyro[2]*DEG2RAD - NAV_Data_Full_p->Param.gyro_off[2];
//			NAV_Data_Full_p->IMU.acc_use[0] = NAV_Data_Full_p->IMU.acc[0] - NAV_Data_Full_p->Param.acc_off[0];
//			NAV_Data_Full_p->IMU.acc_use[1] = NAV_Data_Full_p->IMU.acc[1] - NAV_Data_Full_p->Param.acc_off[1];
//			NAV_Data_Full_p->IMU.acc_use[2] = NAV_Data_Full_p->IMU.acc[2] - NAV_Data_Full_p->Param.acc_off[2];
			
			NAV_Data_Full_p->IMU.gyro_use[0] = NAV_Data_Full_p->IMU.gyro[0]*DEG2RAD;
			NAV_Data_Full_p->IMU.gyro_use[1] = NAV_Data_Full_p->IMU.gyro[1]*DEG2RAD;
			NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->IMU.gyro[2]*DEG2RAD;
			NAV_Data_Full_p->IMU.acc_use[0] = NAV_Data_Full_p->IMU.acc[0];
			NAV_Data_Full_p->IMU.acc_use[1] = NAV_Data_Full_p->IMU.acc[1];
			NAV_Data_Full_p->IMU.acc_use[2] = NAV_Data_Full_p->IMU.acc[2];
			NAV_Data_Full_p->IMU.temp_use = NAV_Data_Full_p->IMU.temp_mems;	
   //******Guolong Zhang*******20250102***调整安装轴向******->******
			tempgx = NAV_Data_Full_p->IMU.gyro_use[0];	
			tempgy = NAV_Data_Full_p->IMU.gyro_use[1];	
			tempgz = NAV_Data_Full_p->IMU.gyro_use[2];	
			tempax = NAV_Data_Full_p->IMU.acc_use[0];	
			tempay = NAV_Data_Full_p->IMU.acc_use[1];	
			tempaz = NAV_Data_Full_p->IMU.acc_use[2];
			
			NAV_Data_Full_p->Axis_flag=CombineData_p->Axis_flag;
			if(NAV_Data_Full_p->Axis_flag)
			{
					for(i=0;i<3;i++)
					{
						NAV_Data_Full_p->Axis[i]=CombineData_p->Axis[i];
						NAV_Data_Full_p->Axis_sign[i]=CombineData_p->Axis_sign[i];
					}
				CombineData_p->Axis_flag = RETURN_FAIL;//*****读取之后置零，等待用户重新设定***20250103新增*****Guolong Zhang********
								
				NAV_Data_Full_p->ins_buffer_full_flag = RETURN_FAIL;
				NAV_Data_Full_p->ZUPT_flag = RETURN_FAIL;
				NAV_Data_Full_p->acc_gyr_Cnt = 0;
				NAV_Data_Full_p->Nav_Status = E_NAV_STATUS_START;
				NAV_Data_Full_p->ins_buffer_full_cnt = 0;
				NAV_Data_Full_p->Modacc = 0.0;
				NAV_Data_Full_p->Modgyr = 0.0;
				NAV_Data_Full_p->Modacc_std2 = 0.0;
				NAV_Data_Full_p->Modgyr_std2 = 0.0;
				NAV_Data_Full_p->Macc[0] = 0.0;
				NAV_Data_Full_p->Macc[1] = 0.0;
				NAV_Data_Full_p->Macc[2] = 0.0;
				NAV_Data_Full_p->Mgyr[0]=0.0;
				NAV_Data_Full_p->Mgyr[1]=0.0;
				NAV_Data_Full_p->Mgyr[2]=0.0;
				NAV_Data_Full_p->KF_CheckCnt = 0;
				NAV_Data_Full_p->Subkf2_Cnt = 0;
				NAV_Data_Full_p->ZUPTyaw_ST_Cnt = 0;
				NAV_Data_Full_p->SINS.Init_flag = RETURN_FAIL;//每次启动时默认为0，尚未初始化
				NAV_Data_Full_p->Pre_att_flag = RETURN_FAIL;//每次启动置0,尚未进入组合状态
			}
			//
			{
				//*****X轴调整*******
				switch(NAV_Data_Full_p->Axis[0])
				{
					case 'X':
					{
						NAV_Data_Full_p->IMU.acc_use[0] = NAV_Data_Full_p->Axis_sign[0]*tempax;
						NAV_Data_Full_p->IMU.gyro_use[0] = NAV_Data_Full_p->Axis_sign[0]*tempgx;	
					}
					break;
					case 'Y':
					{
						NAV_Data_Full_p->IMU.acc_use[0] = NAV_Data_Full_p->Axis_sign[0]*tempay;
						NAV_Data_Full_p->IMU.gyro_use[0] = NAV_Data_Full_p->Axis_sign[0]*tempgy;	
					}
					break;
					case 'Z':
					{
						NAV_Data_Full_p->IMU.acc_use[0] = NAV_Data_Full_p->Axis_sign[0]*tempaz;
						NAV_Data_Full_p->IMU.gyro_use[0] = NAV_Data_Full_p->Axis_sign[0]*tempgz;	
					}
					break;
					default:break;
				}
				//*****Y轴调整*******
				switch(NAV_Data_Full_p->Axis[1])
				{
					case 'X':
					{
						NAV_Data_Full_p->IMU.acc_use[1] = NAV_Data_Full_p->Axis_sign[1]*tempax;
						NAV_Data_Full_p->IMU.gyro_use[1] = NAV_Data_Full_p->Axis_sign[1]*tempgx;	
					}
					break;
					case 'Y':
					{
						NAV_Data_Full_p->IMU.acc_use[1] = NAV_Data_Full_p->Axis_sign[1]*tempay;
						NAV_Data_Full_p->IMU.gyro_use[1] = NAV_Data_Full_p->Axis_sign[1]*tempgy;	
					}
					break;
					case 'Z':
					{
						NAV_Data_Full_p->IMU.acc_use[1] = NAV_Data_Full_p->Axis_sign[1]*tempaz;
						NAV_Data_Full_p->IMU.gyro_use[1] = NAV_Data_Full_p->Axis_sign[1]*tempgz;	
					}
					break;
					default:break;
				}
				//*****Z轴调整*******
				switch(NAV_Data_Full_p->Axis[2])
				{
					case 'X':
					{
						NAV_Data_Full_p->IMU.acc_use[2] = NAV_Data_Full_p->Axis_sign[2]*tempax;
						NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->Axis_sign[2]*tempgx;	
					}
					break;
					case 'Y':
					{
						NAV_Data_Full_p->IMU.acc_use[2] = NAV_Data_Full_p->Axis_sign[2]*tempay;
						NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->Axis_sign[2]*tempgy;	
					}
					break;
					case 'Z':
					{
						NAV_Data_Full_p->IMU.acc_use[2] = NAV_Data_Full_p->Axis_sign[2]*tempaz;
						NAV_Data_Full_p->IMU.gyro_use[2] = NAV_Data_Full_p->Axis_sign[2]*tempgz;	
					}
					break;
					default:break;
				}
			}
			//*******Guolong Zhang******20250102***调整安装轴向******<-******				
		}
		//savebuff();
		//对原始数据减去零偏********
		//InitZeroOffsetCompenstation(NAV_Data_Full_p);
#ifndef linux
		//如果是光纤陀螺，则读取标定参数并对数据进行标定
		if(E_IMU_ATT_IFOG==NAV_Data_Full_p->imuSelect)
		{
		Load_Calib_Parms(NAV_Data_Full_p,CombineData_p);
		
		double tmp[3]={0},tmp2[3]={0};
		tmp[0]=NAV_Data_Full_p->IMU.gyro_use[0]*RAD2DEG;
		tmp[1]=NAV_Data_Full_p->IMU.gyro_use[1]*RAD2DEG;
		tmp[2]=NAV_Data_Full_p->IMU.gyro_use[2]*RAD2DEG;
		//nav_calib(tmp, &NAV_Data_Full_p->gyroCalib, tmp2);
		//NAV_Data_Full_p->IMU.gyro_use[0] = tmp2[0]*DEG2RAD;
		//NAV_Data_Full_p->IMU.gyro_use[1] = tmp2[1]*DEG2RAD;
		//NAV_Data_Full_p->IMU.gyro_use[2] = tmp2[2]*DEG2RAD ;

#if 0			
		memset(tmp,0,sizeof(tmp));
		memset(tmp2,0,sizeof(tmp2));
		tmp[0]=NAV_Data_Full_p->IMU.acc_use[0];
		tmp[1]=NAV_Data_Full_p->IMU.acc_use[1];
		tmp[2]=NAV_Data_Full_p->IMU.acc_use[2];
		nav_calib(tmp, &NAV_Data_Full_p->accCalib, tmp2);
		NAV_Data_Full_p->IMU.acc_use[0] = tmp2[0];
		NAV_Data_Full_p->IMU.acc_use[1] = tmp2[1];
		NAV_Data_Full_p->IMU.acc_use[2] = tmp2[2];
#endif		
			
		}
#endif		
		
#if 0		
		//对IMU原始观测量进行kalman滤波
		for(i = 0;i<3;i++)
		{
			//Scalar_KalmanFilte(&NAV_Data_Full.IMU.gyro_use_kalman[i],NAV_Data_Full.IMU.gyro_use[i],&P_gyro_Cvn,Q_gyro_Cvn,R_gyro_Cvn);
			//Scalar_KalmanFilte(&NAV_Data_Full.IMU.acc_use_kalman[i],NAV_Data_Full.IMU.acc_use[i],&P_acc_Cvn,Q_acc_Cvn,R_acc_Cvn);
			NAV_Data_Full.IMU.gyro_use_kalman[i]	=	NAV_Data_Full.IMU.gyro_use_pre[i];
			NAV_Data_Full.IMU.acc_use_kalman[i]	=	NAV_Data_Full.IMU.acc_use_pre[i];
		}
#endif		
		//IMU奇异值检测与去除
		if(0)
		{
			if(		(fabs(NAV_Data_Full.IMU.gyro_use[0]-NAV_Data_Full.IMU.gyro_use_pre[0])*RAD2DEG)>GYRO_ABNORMAL_THRESHOLD
				||	(fabs(NAV_Data_Full.IMU.gyro_use[1]-NAV_Data_Full.IMU.gyro_use_pre[2])*RAD2DEG)>GYRO_ABNORMAL_THRESHOLD)
#ifdef linux
					inav_log(INAVMD(LOG_ERR),"gyro_use is over");	
#endif
			for(i = 0;i<3;i++)
			{
					NAV_Data_Full.IMU.gyro_use[1] = NAV_Data_Full.IMU.gyro_use_kalman[1];
			}
			
			if(		(fabs(NAV_Data_Full.IMU.acc_use[0]-NAV_Data_Full.IMU.acc_use_pre[0])>ACC_ABNORMAL_THRESHOLD)
				//||  (fabs(NAV_Data_Full.IMU.acc_use[2]-NAV_Data_Full.IMU.acc_use_pre[0])>ACC_ABNORMAL_THRESHOLD)
			   )
			{
#ifdef linux
				inav_log(INAVMD(LOG_ERR),"acc_use is over");	
#endif
				for(i = 0;i<3;i++)
				{
					NAV_Data_Full.IMU.acc_use[i] = NAV_Data_Full.IMU.acc_use_kalman[i];
				}					
			}		
		}		
	}

//	//gyro data check
//	if(RETURN_SUCESS == Gyro_data_check(&NAV_Data_Full))
//	{
//		SetNavStatus(E_NAV_STATUS_START);
//	}
	//加计核实
	if((RETURN_FAIL == Acc_data_check(&NAV_Data_Full)) || (RETURN_FAIL == Gyro_data_check(&NAV_Data_Full)))
	{
		 // SetNavStatus(E_NAV_STATUS_START);		
	}
	//*****20240711*********zupt check*******GuoLong Zhang*****->*****
 double  modacc=0.0,  modgyr=0.0;
	double  acc_mean=0.0,gyr_mean=0.0;
	double  acc_std=0.0, gyr_std=0.0;
	double  acc_std2=0.0,gyr_std2=0.0;
//	double  axm=0.0,     aym=0.0,     azm=0.0;
//	double  gxm = 0.0, gym = 0.0, gzm = 0.0;
	double premodacc=0.0, premodgyr=0.0,pre_acc[3]={0.0,0.0,0.0},pre_gyr[3]={0.0,0.0,0.0};
	modacc=sqrt(CombineData_p->scha634Info.acc_x*CombineData_p->scha634Info.acc_x+
	            CombineData_p->scha634Info.acc_y*CombineData_p->scha634Info.acc_y+
	            CombineData_p->scha634Info.acc_z*CombineData_p->scha634Info.acc_z);//****g****
	
	modgyr=sqrt(CombineData_p->scha634Info.gyro_x*CombineData_p->scha634Info.gyro_x+
	            CombineData_p->scha634Info.gyro_y*CombineData_p->scha634Info.gyro_y+
	            CombineData_p->scha634Info.gyro_z*CombineData_p->scha634Info.gyro_z);//****deg/s****
//
	premodacc=NAV_Data_Full_p->buffer_acc[NAV_Data_Full_p->acc_gyr_Cnt];
	premodgyr=NAV_Data_Full_p->buffer_gyr[NAV_Data_Full_p->acc_gyr_Cnt];//更新之前先保存上一时刻的数值
	NAV_Data_Full_p->buffer_acc[NAV_Data_Full_p->acc_gyr_Cnt]=modacc;
	NAV_Data_Full_p->buffer_gyr[NAV_Data_Full_p->acc_gyr_Cnt]=modgyr;
//********更新之前先保存上一时刻的各轴数值**********
 pre_acc[0]=NAV_Data_Full_p->Ma_x[NAV_Data_Full_p->acc_gyr_Cnt];
 pre_acc[1]=NAV_Data_Full_p->Ma_y[NAV_Data_Full_p->acc_gyr_Cnt];
	pre_acc[2]=NAV_Data_Full_p->Ma_z[NAV_Data_Full_p->acc_gyr_Cnt];
	pre_gyr[0]=NAV_Data_Full_p->Mg_x[NAV_Data_Full_p->acc_gyr_Cnt];
 pre_gyr[1]=NAV_Data_Full_p->Mg_y[NAV_Data_Full_p->acc_gyr_Cnt];
	pre_gyr[2]=NAV_Data_Full_p->Mg_z[NAV_Data_Full_p->acc_gyr_Cnt];
	NAV_Data_Full_p->Ma_x[NAV_Data_Full_p->acc_gyr_Cnt]=NAV_Data_Full_p->IMU.acc_use[0];
	NAV_Data_Full_p->Ma_y[NAV_Data_Full_p->acc_gyr_Cnt]=NAV_Data_Full_p->IMU.acc_use[1];
	NAV_Data_Full_p->Ma_z[NAV_Data_Full_p->acc_gyr_Cnt]=NAV_Data_Full_p->IMU.acc_use[2];
	NAV_Data_Full_p->Mg_x[NAV_Data_Full_p->acc_gyr_Cnt] = NAV_Data_Full_p->IMU.gyro_use[0];
	NAV_Data_Full_p->Mg_y[NAV_Data_Full_p->acc_gyr_Cnt] = NAV_Data_Full_p->IMU.gyro_use[1];
	NAV_Data_Full_p->Mg_z[NAV_Data_Full_p->acc_gyr_Cnt] = NAV_Data_Full_p->IMU.gyro_use[2];
	NAV_Data_Full_p->acc_gyr_Cnt++;
	if(NAV_Data_Full_p->acc_gyr_Cnt == ZUPT_SIZE)
	{
		NAV_Data_Full_p->ins_buffer_full_flag = RETURN_SUCESS;//*****缓存数组已满****
		NAV_Data_Full_p->acc_gyr_Cnt=0;
	}
	
	if(NAV_Data_Full_p->ins_buffer_full_flag)//*******首次上电数组存满以后，窗口数据不断更新****
	{
		NAV_Data_Full_p->ins_buffer_full_cnt++;
		if(NAV_Data_Full_p->ins_buffer_full_cnt <= 1)//计算窗口内首个均值和std
		{
			 for(i=0;i<ZUPT_SIZE;i++)
     {			
			   NAV_Data_Full_p->Modacc	+=(NAV_Data_Full_p->buffer_acc[i]/ZUPT_SIZE);
			   NAV_Data_Full_p->Modgyr +=(NAV_Data_Full_p->buffer_gyr[i]/ZUPT_SIZE);
     }
				for(i=0;i<ZUPT_SIZE;i++)
					{
			   NAV_Data_Full_p->Modacc_std2 +=(NAV_Data_Full_p->buffer_acc[i]-NAV_Data_Full_p->Modacc)*(NAV_Data_Full_p->buffer_acc[i]-NAV_Data_Full_p->Modacc)/ZUPT_SIZE;
		  	 NAV_Data_Full_p->Modgyr_std2 +=(NAV_Data_Full_p->buffer_gyr[i]-NAV_Data_Full_p->Modgyr)*(NAV_Data_Full_p->buffer_gyr[i]-NAV_Data_Full_p->Modgyr)/ZUPT_SIZE;//****
					}
					for(i=0;i<ZUPT_SIZE;i++)//计算窗口内各轴均值**************
					{
						NAV_Data_Full_p->Macc[0] += (NAV_Data_Full_p->Ma_x[i]/ZUPT_SIZE);
						NAV_Data_Full_p->Macc[1] += (NAV_Data_Full_p->Ma_y[i]/ZUPT_SIZE);
						NAV_Data_Full_p->Macc[2] += (NAV_Data_Full_p->Ma_z[i]/ZUPT_SIZE);
						NAV_Data_Full_p->Mgyr[0] += (NAV_Data_Full_p->Mg_x[i] / ZUPT_SIZE);
						NAV_Data_Full_p->Mgyr[1] += (NAV_Data_Full_p->Mg_y[i] / ZUPT_SIZE);
						NAV_Data_Full_p->Mgyr[2] += (NAV_Data_Full_p->Mg_z[i] / ZUPT_SIZE);
					}
		}
		else//********递推计算，减少计算量**********
		{
			 		acc_mean	=NAV_Data_Full_p->Modacc+(modacc-premodacc)/ZUPT_SIZE;
			   gyr_mean =NAV_Data_Full_p->Modgyr+(modgyr-premodgyr)/ZUPT_SIZE;//均值
			//
			   acc_std2=NAV_Data_Full_p->Modacc_std2+((modacc-acc_mean)*(modacc-acc_mean)-(premodacc-NAV_Data_Full_p->Modacc)*(premodacc-NAV_Data_Full_p->Modacc))/ZUPT_SIZE;
			   gyr_std2=NAV_Data_Full_p->Modgyr_std2+((modgyr-gyr_mean)*(modgyr-gyr_mean)-(premodgyr-NAV_Data_Full_p->Modgyr)*(premodgyr-NAV_Data_Full_p->Modgyr))/ZUPT_SIZE;//方差
			 //******赋值**** 
     NAV_Data_Full_p->Modacc=acc_mean;
			  NAV_Data_Full_p->Modgyr=gyr_mean;
			  NAV_Data_Full_p->Modacc_std2=acc_std2;
			  NAV_Data_Full_p->Modgyr_std2=gyr_std2;
			//窗口内各轴均值*********
				for(i=0;i<3;i++)
				{
						NAV_Data_Full_p->Macc[i] += (NAV_Data_Full_p->IMU.acc_use[i]-pre_acc[i])/ZUPT_SIZE;
						NAV_Data_Full_p->Mgyr[i] += (NAV_Data_Full_p->IMU.gyro_use[i]-pre_gyr[i]) / ZUPT_SIZE;
				}
			}
			//是否符合静止的判断条件
			if (sqrt(acc_std2) < TH_acc && sqrt(gyr_std2) < TH_gyr)
			{
				NAV_Data_Full_p->ZUPT_flag = RETURN_SUCESS;
			}
			else
			{
				NAV_Data_Full_p->ZUPT_flag = RETURN_FAIL;
			}
	}
	//*****20240711*********zupt check*******GuoLong Zhang*****<-*****
	//*****20240805*********turnning check*******GuoLong Zhang*****->*****
	if (sqrt(NAV_Data_Full_p->Mgyr[2] * NAV_Data_Full_p->Mgyr[2] + NAV_Data_Full_p->Mgyr[1] * NAV_Data_Full_p->Mgyr[1]
		+ NAV_Data_Full_p->Mgyr[0] * NAV_Data_Full_p->Mgyr[0]) * RAD2DEG > 15 * MIN_TURN_GYRO_VALUE)//**********是否急转弯*****
	{
		 NAV_Data_Full_p->TurnningFlag = RETURN_SUCESS; //*****
	}
	else
	{
		 NAV_Data_Full_p->TurnningFlag = RETURN_FAIL;
	}
	//*****20240805*********turnning check*******GuoLong Zhang*****<-*****
	//**********20241207********根据客户反馈问题，增加水平姿态输出功能**************->***********
//	if((NAV_Data_Full_p->Nav_Status>0) && 
//		   (!(NAV_Data_Full_p->Pre_att_flag))
//	  )//加载过系统参数，尚未进入组合状态
//	{
//			double SMA[3]={0.0,0.0,0.0};
//			SMA[0]=NAV_Data_Full_p->IMU.acc_use[0]-NAV_Data_Full_p->SINS.db[0];
//			SMA[1]=NAV_Data_Full_p->IMU.acc_use[1]-NAV_Data_Full_p->SINS.db[1];
//			SMA[2]=NAV_Data_Full_p->IMU.acc_use[2]-NAV_Data_Full_p->SINS.db[2];
//			
//	//		NAV_Data_Full_p->SINS.att[0] = atan2( NAV_Data_Full_p->Macc[1],sqrt(NAV_Data_Full_p->Macc[0]*NAV_Data_Full_p->Macc[0]+NAV_Data_Full_p->Macc[2]*NAV_Data_Full_p->Macc[2]));//俯仰角
//	//		NAV_Data_Full_p->SINS.att[1] = atan2(-NAV_Data_Full_p->Macc[0],NAV_Data_Full_p->Macc[2]);//横滚角
//			NAV_Data_Full_p->Pre_att[0] = atan2( SMA[1],sqrt(SMA[0]*SMA[0]+SMA[2]*SMA[2]));//俯仰角
//			NAV_Data_Full_p->Pre_att[1] = atan2(-SMA[0],SMA[2]);//横滚角
//			NAV_Data_Full_p->Pre_att[2] =0.0;//未接天线，heading默认为0
//			if(NAV_Data_Full_p->Nav_Standard_flag == E_NAV_STANDARD_PROCCSSED)
//			{
//				NAV_Data_Full_p->Pre_att[0] =NAV_Data_Full_p->Pre_att[0] + DEG2RAD*NAV_Data_Full_p->ODS.att_ods2_b_filte[0];//补偿安装角
//			}
//	}
		//**********20241207********根据客户反馈问题，增加水平姿态输出功能**************<-***********  
	//*****20250401*****Guolong Zhang****针对狗熊机器人的需求增加功能***扩展应用到默认算法版本**->******
	if( (NAV_Data_Full_p->Nav_Status>0)&& 
		   (!(NAV_Data_Full_p->Pre_att_flag))&&
	    (NAV_Data_Full_p->ins_buffer_full_flag)
		  )
	{
		double normAcc=0.0,tmpTS=0.0,GN[3]={0.0,0.0,1.0},vb[3]={0.0,0.0,0.0},Err[3]={0.0,0.0,0.0};
		double ACCfb[3]={0.0,0.0,0.0},BiasGyr[3]={0.0,0.0,0.0},Gyrk[3]={0.0,0.0,0.0},TempQb[4]={1.0,0.0,0.0,0.0},TempQ[4]={1.0,0.0,0.0,0.0},TmpPHI[3]={0.0,0.0,0.0};
		int i=0;
		double Tor=0.0,belt=0.0,KP=0.0,KI=0.0;
		Tor=3.2;
		belt=2.146/Tor;
		KP=2.0*belt;
		KI=belt*belt;
		tmpTS=1.0/SAMPLE_FREQ;//默认版本是200Hz
			if(NAV_Data_Full_p->ZUPT_flag)
			{
					BiasGyr[0]=NAV_Data_Full_p->Mgyr[0];
					BiasGyr[1]=NAV_Data_Full_p->Mgyr[1];
					BiasGyr[2]=NAV_Data_Full_p->Mgyr[2];
			}
			ACCfb[0]=NAV_Data_Full_p->IMU.acc_use[0]-NAV_Data_Full_p->SINS.db[0];//扣除bias
			ACCfb[1]=NAV_Data_Full_p->IMU.acc_use[1]-NAV_Data_Full_p->SINS.db[1];
			ACCfb[2]=NAV_Data_Full_p->IMU.acc_use[2]-NAV_Data_Full_p->SINS.db[2];
			normAcc=sqrt(ACCfb[0]*ACCfb[0]+ACCfb[1]*ACCfb[1]+ACCfb[2]*ACCfb[2]);
			for(int i=0;i<3;i++)
			{
				 Gyrk[i]=(NAV_Data_Full_p->IMU.gyro_use_pre[i]+NAV_Data_Full_p->IMU.gyro_use[i])/2.0-BiasGyr[i];
			}
			ACCfb[0]=ACCfb[0]/normAcc;
			ACCfb[1]=ACCfb[1]/normAcc;
			ACCfb[2]=ACCfb[2]/normAcc;//归一化
			//姿态、四元数初始化
			if(NAV_Data_Full_p->ins_buffer_full_cnt <= 1)//初值
			{
				 	NAV_Data_Full_p->Pre_att[0] = atan2( NAV_Data_Full_p->Macc[1],sqrt(NAV_Data_Full_p->Macc[0]*NAV_Data_Full_p->Macc[0]+NAV_Data_Full_p->Macc[2]*NAV_Data_Full_p->Macc[2]));//俯仰角
			   NAV_Data_Full_p->Pre_att[1] = atan2(-NAV_Data_Full_p->Macc[0],NAV_Data_Full_p->Macc[2]);//横滚角

			   // 掉电保存航向角功能：从Flash恢复航向角
			   double restored_azimuth = RestoreAzimuthFromFlash();
			   if (IsAzimuthSaveValid()) {
			       NAV_Data_Full_p->Pre_att[2] = restored_azimuth; // 恢复保存的航向角
			   } else {
			       NAV_Data_Full_p->Pre_att[2] = 0.0; // 如果没有有效保存，使用默认值0
			   }

				  att2qnb(NAV_Data_Full_p->Pre_att,NAV_Data_Full_p->SINS.qnb);
			}
			else
			{
				for(i=0;i<3;i++)
				{
					 TmpPHI[i]=Gyrk[i]*tmpTS;
				}
				rv2q(TmpPHI, TempQb);
   //gyro更新姿态
			qnbmul(NAV_Data_Full_p->SINS.qnb, TempQb, TempQ);
			Qnb2Cnb(TempQ, NAV_Data_Full_p->SINS.Cb2n);
			matmul("TN", 3, 1, 3, 1, NAV_Data_Full_p->SINS.Cb2n,GN,0.0,vb);
			cross3(ACCfb,vb,Err);
			for(int i=0;i<3;i++)
			{
				 Gyrk[i]=Gyrk[i]+KP*Err[i]+KI*Err[i]*tmpTS;//修正gyro
			}
			//********校正后的四元数**********
				for(i=0;i<3;i++)
				{
					 TmpPHI[i]=Gyrk[i]*tmpTS;
				}
				rv2q(TmpPHI, TempQb);
   //校正后的四元数和姿态
			qnbmul(NAV_Data_Full_p->SINS.qnb, TempQb, NAV_Data_Full_p->SINS.qnb);
								//四元数归一化
	  NAV_Data_Full_p->SINS.q_Norm = sqrt( NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.qnb[0]
									+ NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.qnb[1]
									+ NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.qnb[2]
									+ NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.qnb[3]
								);
			NAV_Data_Full_p->SINS.q_Norm_f = 1.0/NAV_Data_Full_p->SINS.q_Norm;
			NAV_Data_Full_p->SINS.qnb[0] = NAV_Data_Full_p->SINS.qnb[0]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[1] = NAV_Data_Full_p->SINS.qnb[1]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[2] = NAV_Data_Full_p->SINS.qnb[2]*NAV_Data_Full_p->SINS.q_Norm_f;
			NAV_Data_Full_p->SINS.qnb[3] = NAV_Data_Full_p->SINS.qnb[3]*NAV_Data_Full_p->SINS.q_Norm_f;
			qnb2att(NAV_Data_Full_p->SINS.qnb, NAV_Data_Full_p->Pre_att);
		}
	}
		//*****20250401*****Guolong Zhang****针对狗熊机器人的需求增加功能*****<-******
	
}
/******************************************************************************R
*原  型：u8 Gyro_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：陀螺超量程检测
*输  入：无
*输  出：无
*******************************************************************************/
unsigned char Gyro_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned char abnormal_flag = RETURN_SUCESS;
	if(  (fabs(NAV_Data_Full_p->IMU.gyro_use[0])>MAX_GYRO_VALUE*DEG2RAD)
	   ||(fabs(NAV_Data_Full_p->IMU.gyro_use[1])>MAX_GYRO_VALUE*DEG2RAD)
	   ||(fabs(NAV_Data_Full_p->IMU.gyro_use[2])>MAX_GYRO_VALUE*DEG2RAD)
	  )
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"Gyro_data_check=[%f,%f,%f]\r\n"\
		,NAV_Data_Full.IMU.gyro_use[0],NAV_Data_Full.IMU.gyro_use[1],NAV_Data_Full.IMU.gyro_use[2]);
#endif	
		abnormal_flag = RETURN_FAIL;
	}
	return abnormal_flag;
}

/******************************************************************************
*原  型：u8 Acc_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：加速度检测
*输  入：无
*输  出：无
*******************************************************************************/
unsigned char Acc_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned char abnormal_flag = RETURN_SUCESS;
	if(  (fabs(NAV_Data_Full_p->IMU.acc_use[0])>MAX_ACC_VALUE)
	   ||(fabs(NAV_Data_Full_p->IMU.acc_use[1])>MAX_ACC_VALUE)
	   ||(fabs(NAV_Data_Full_p->IMU.acc_use[2])>MAX_ACC_VALUE)
	  )
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"Acc_data_check=[%f,%f,%f]\r\n"\
		,NAV_Data_Full.IMU.acc_use[0],NAV_Data_Full.IMU.acc_use[1],NAV_Data_Full.IMU.acc_use[2]);
#endif
		abnormal_flag = RETURN_FAIL;
	}
	return abnormal_flag;
}
/******************************************************************************
*原  型：savebuff(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：安装参数检测
*输  入：无
*输  出：无
*******************************************************************************/
void savebuff()
{
	static int index = 0;
	index = index % MAX_IMU_BUFF;
	for (int i =0;i<3;i++)
	{
	NAV_Data_Full.IMU.acc_use_buffer[i+index*3] = NAV_Data_Full.IMU.acc_use[i];
	NAV_Data_Full.IMU.gyro_use_buffer[i+index*3] = NAV_Data_Full.IMU.gyro_use[i];
	}
	index ++ ;
}

/******************************************************************************
*原  型：u8 install_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：安装参数检测
*输  入：无
*输  出：无
*******************************************************************************/
unsigned char install_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned char abnormal_flag = RETURN_SUCESS;	
	if(norm(NAV_Data_Full_p->Param.gnssArmLength,3)>MAX_ARM_VALUE)
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"install_data_check:gnssArmLength=[%f,%f,%f]\r\n"\
		,NAV_Data_Full_p->Param.gnssArmLength[0],NAV_Data_Full_p->Param.gnssArmLength[1],NAV_Data_Full_p->Param.gnssArmLength[2]);
#endif
		abnormal_flag = RETURN_FAIL;
	}
	return abnormal_flag;
}

/*************************************Calibration*********************************************/
//从驱动数据读取标定参数
void Load_Calib_Parms(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p)
{
	memcpy(&NAV_Data_Full_temp->gyroCalib,&CombineData_p->gyroCalib,sizeof(_calib_t));
	memcpy(&NAV_Data_Full_temp->accCalib,&CombineData_p->accCalib,sizeof(_calib_t));
}
/******************************************************************************
*原  型：unsigned char calibGyro(double* output)
*功  能：对陀螺/加计数据进行标定
*输  入：original 原始陀螺/加计数据，
		pcalib	 标定参数
*输  出：standard 标定后的陀螺/加计数据
*******************************************************************************/
unsigned char nav_calib(double *original,_calib_t* pcalib,double *standard)
{
	double ab[3]={0.0};
	double invfac[9]={0.0};
	if(NULL==original || NULL==pcalib || NULL==standard)
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"nav_calib NULL");
#endif	
		return 0;
	}

	if(RETURN_FAIL == pcalib->flag)
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"nav_calib parameter invalid");
#endif			
		return 0;
	}
	
	//原始数据-零偏
	matrixSum(original, pcalib->zeroOffset, 3, 1, -1, ab);
	
	memcpy(invfac,pcalib->factor,sizeof(invfac));
	matinv(invfac, 3);
	matmul("NN", 3, 1, 3, 1.0, invfac, ab, 0.0, standard);
	
	return 1;
}

