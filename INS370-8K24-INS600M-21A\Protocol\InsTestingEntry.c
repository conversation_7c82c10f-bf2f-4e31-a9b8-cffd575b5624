/*!
    \file    INS912AlgorithmEntry.c
    \brief   INS912 algorithm interface functions 
    \version v1.0.1
	\author	 Bill
	\date    2023/10/23
*/
#include "InsTestingEntry.h"
#include "string.h"
#include "frame_analysis.h"
#include "EXTERNGLOBALDATA.h"
#include "ins.h"
#include "FUNCTION.h"
#include "datado.h"
#include "deviceconfig.h"
#include "app_tool.h"
#include "pjt_board.h"
#include "gdtypedefine.h"
#include "appmain.h"
#include "uart.h"

//*********20250102***ZJZ*********
//用于坐标系的调整
static const char SetCoord[6][3] =
{
    {'X','Y','Z'},
    {'X','Z','Y'},
    {'Y','X','Z'},
    {'Y','Z','X'},
    {'Z','X','Y'},
    {'Z','Y','X'},
};

static const float SetDir[8][3] =
{
    {1.0,1.0,1.0},
    {-1.0,1.0,1.0},
    {1.0,-1.0,1.0},
    {1.0,1.0,-1.0},
    {-1.0,-1.0,1.0},
    {1.0,-1.0,-1.0},
    {-1.0,1.0,-1.0},
    {-1.0,-1.0,-1.0},
};

extern uint8_t g_StartUpdateFirm;//开始升级标志 1:开始升级 0:结束升级

arraytodata_t	ginputdata;
gdwrxdata912_t	gins912data;
MiddleWare_t StrMiddleWare;//加计和陀螺中间件，用于坐标轴调整
char	gins912data_cash[600];
PAOCHE_FRAME_STRUCT paochedata;//912机型

uint8_t BB00SdData[SDRX_BB00_MAXCOUNT] = {0};
uint8_t g_BB00WriteSdFlag=0;//写入SD卡文件BB00原始数据标志

FpgadataPreDoSend_t gfpgadataPredoSend;	//发送FPGA原始数据predo	
SdDataSave_t	gSdDataSave;									

#ifdef DEVICE_TYPE_370
const double coe_fog_gx = (1.0*(1.0/756027	));//20240814//1.335030071e-06;//光钎陀螺标度因数，1/X
const double coe_fog_gy = (1.0*(1.0/7457331));//1.319549770e-06;
const double coe_fog_gz = (1.0*(1.0/748142	));//1.324544541e-06;
#endif
#ifdef DEVICE_TYPE_600_21A
	#ifdef DEVICE_GYRO_TYPE_IMU_SCHA63X_subType03
		const float coe_fog_gxyz_factor = 80.0;		//村田后缀型号03，因子=80.0
	#else
		const float coe_fog_gxyz_factor = 160.0;	//村田后缀型号不是03，因子=160.0
	#endif
const double coe_fog_gx = (1/coe_fog_gxyz_factor);			//村田IMU 标度因数，1/X
const double coe_fog_gy = (1/coe_fog_gxyz_factor);
const double coe_fog_gz = (1/coe_fog_gxyz_factor);
#endif

navcanin_t		canin;

#ifdef CMPL_CODE_EDWOY
#include "sensor_misc.h"

#ifdef DEVICE_ACC_TYPE_hd6089
#define FACTOR_ACC        	Factor_Acc_HD6089    //Edwoy,exp 400000
#endif
#ifdef DEVICE_ACC_TYPE_DACC
#define FACTOR_ACC  		Factor_DAcc_xian
#endif
#ifdef DEVICE_ACC_TYPE_ADLX355
#define FACTOR_ACC        	Factor_Acc_ADXL355    //Edwoy,exp 400000
#endif
#ifdef DEVICE_TYPE_600_21A
#define FACTOR_ACC  		Factor_Acc_SCHA63X
#endif

//#define FACTOR_FOG_GYRO   Factor_Fog_Type70    //Edwoy,exp 755024.395768
#include "fpgad.h"
extern fpgadata_t	gpagedata;

//int32_t r_fog[3];
int32_t r_acc[3];
MYUNION r_acc_u[3];
uint32_t r_fog[3];

#endif

//传递陀螺，加速度计数据
ANGRATE r_Gyro[3] = {0.0};
ANGRATE r_LastGyro[3] = {0.0};

ACCELER Acc[3] = {0.0};
ACCELER LastAcc[3] = {0.0};

//临时补偿参数
ftype Gw[3][3] = {0};
V3_ST gw  = {0};
V3_ST gWm  = {0};
V3_ST gW_real = {0};

ftype Aw[3][3] = {0};
V3_ST aw  = {0};
V3_ST aWm  = {0};
V3_ST aW_real = {0};	


typedef struct _sttbigvlu{
  uint32_t    fog_raw[3];
  float      fog_step1[3];
  uint32_t    acc_raw[3];
  float      acc_step1[3];  
}sttBigVlu_t ;

typedef union _unbigvlu{
  sttBigVlu_t member;
  uint8_t     datas[16];
}unBigVlu_t ;  

unBigVlu_t r_pkt_bigvlu; 

#define	UPPER_InsUart_Hd0x66AA  		4	   
#define	UPPER_InsUart_Hd0x00BB  		5	   

extern int	gins912outputmode  ;				 //产品 ins422/ins232 输出数据类型选择

#define FPGA_CFRDpart_16bitQty    108 
#define FRAME_CFRDpart_16bitQty   (FPGA_CFRDpart_16bitQty + 4)
uint16_t r_cfrdbuf16bit[FRAME_CFRDpart_16bitQty] = {
  [0] = 0x66AA, 
  [1] = 0x0000, 
  [2] = 2 * FRAME_CFRDpart_16bitQty,
  
};


#define TAFEAG_DataSz16bit    140//123 // 108 
#define TAFEAG_FrameSz16bit   (TAFEAG_DataSz16bit + 4) //127
uint32_t  r_fmsz_912info , r_fmsz_paoche;

uint8_t r_TAFEAG8_buf[2 + 2*2 *TAFEAG_FrameSz16bit] ;

uint16_t r_TAFEAG16_buf[2 + 2 *TAFEAG_FrameSz16bit] = {
  [0] = 0x00BB , [1] = 0, [2] = 2 * TAFEAG_FrameSz16bit,
};


typedef union _un_tafeag{
  sttIns370Trk_t  wcdata;
  uint16_t        dtlist[TAFEAG_FrameSz16bit];
}unTAFEAG_t;

unTAFEAG_t r_datas_tafeag;

char Count1 = 0;	

void FPGATo422_00BB_send(navoutdata_t *pnavout);
	
//**************************************************************************************************


void AlgorithmAct(void)
//主工作流程在这里设置
{
  NaviCompute_do_count++;	//	处理帧计数
  switch(g_SysVar.WorkPhase)        //工作阶段
  {
		  case PHASE_STANDBY ://待机
			{					
					if((g_SysVar.Time>=TIME_START_DELAY)&&(paochedata.gnss_update==1)&&(g_GNSSData_In_Use.isPosEn==YES))//延迟一段时间，避开IMU启动过程的异常输出
					{
						if((g_SysVar.isRapidAlign == NO) && (g_InitBind.isBind == YES))
						{   //惯性凝固坐标系对准初始化
								InertialSysAlign_Init(&g_InitBind,&g_InertialSysAlign);   
								g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;    //粗对准
						}
						else if((g_SysVar.isRapidAlign == YES) && (g_InitBind.isBind == YES)&&(g_InitBind.isHeadBind == YES))
						{
								//动态惯性凝固坐标系对准初始化，对准30s不要拐弯或大机动
								double r_Pos[3];
								r_Pos[0] = g_GNSSData_In_Use.r_GNSSLati;
								r_Pos[1] = g_GNSSData_In_Use.r_GNSSLogi;
								r_Pos[2] = g_GNSSData_In_Use.GNSSHeight;
								DynamicInertialSysAlign_Init(r_Pos, g_GNSSData_In_Use.GNSSVn,&g_DynamicInertialSysAlign);
								g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;    //粗对准
						}
          }						
					break;
			 }               
			 case PHASE_COARSE_ALIGN : //粗对准
			 {   
					if(g_SysVar.isRapidAlign == NO)
					{
						//惯性凝固静基座粗对准计算
						InertialSysAlignCompute(r_Gyro,r_LastGyro, Acc,LastAcc,&g_InertialSysAlign);
						
						if(g_InertialSysAlign.AlignTime >= TIME_COARSE_ALIGN)
						{
								//计算对准参数
								FinishInertialSysAlign(&g_InertialSysAlign);
								//导航初始化
								Navi_Init(&g_InitBind, &g_InertialSysAlign, &g_Navi);
                if(g_SysVar.isFineAlign ==YES)
                {																	
									g_SysVar.WorkPhase = PHASE_FINE_ALIGN;																	
								}
                else
                {
									if(g_SysVar.isGNSSValid == YES)
										g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;	
									else
										g_SysVar.WorkPhase = PHASE_INS_NAVI;								
								}									
						}
					}
					else
					{
						  //GNSS辅助动基座快速对准，需要卫导位置信息及航向信息的辅助
						  double r_Pos[3];
							r_Pos[0] = g_GNSSData_In_Use.r_GNSSLati;
							r_Pos[1] = g_GNSSData_In_Use.r_GNSSLogi;
							r_Pos[2] = g_GNSSData_In_Use.GNSSHeight;
							DynamicInertialSysAlignCompute(r_Gyro,r_LastGyro,Acc,LastAcc,r_Pos,g_GNSSData_In_Use.GNSSVn, &g_DynamicInertialSysAlign);
							if(g_DynamicInertialSysAlign.AlignTime >= TIME_FAST_ALIGN)
							{
									//计算对准参数
									FinishDynamicInertialSysAlign(&g_DynamicInertialSysAlign);
									//导航初始化
									DynamicNavi_Init(&g_GNSSData_In_Use, &g_DynamicInertialSysAlign, &g_Navi);
								  	
									if(g_SysVar.isGNSSValid == YES)
										g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;	
									else
										g_SysVar.WorkPhase = PHASE_INS_NAVI;																			
							}
					}
					break;
			}
			case PHASE_FINE_ALIGN://精对准
			{
				  //精对准，快速kalman滤波或罗经对准
			    //导航计算
          NaviCompute(r_Gyro,r_LastGyro,Acc,LastAcc,&g_Navi);
          //滤波用矩阵的累积
          ComputeFn(&g_Navi, g_Kalman.Fn);
          g_SysVar.Time_FineAlign += TIME_NAVI;
          if(g_SysVar.Time_Integrated_Navi >= 20.0)
          {
             g_SysVar.isDampedOK = YES;
          }

          
          if(g_Kalman.isKalmanStart == YES)
          {
         			//进行Kalman滤波主体部分的计算，该部分计算量较大,切分到4个IMU周期完成全部计算
						 if(g_Kalman.Work_Mode==FILTER)
						 {
								KalCompute(&g_GNSSData_In_Use, &g_Kalman);		
						 }
						 else
						 {
								KalPredict(&g_Kalman);
                g_Kalman.State = 5;							 
						 }								
										
						 if(g_Kalman.State == 5)
						 {
							 g_Kalman.isKalmanStart  = NO;   //信号清除
							 g_Kalman.isCorrectError = YES;  //开启修正误差信号
							 g_Kalman.State = 0;
						 }
         
         }
				 if(g_Kalman.isCorrectError == YES)
         {
              //误差校正
              ErrStore_1s(&g_Navi, &g_Kalman,&g_GNSSData_In_Use);

              g_Kalman.isCorrectError = NO;
              //Gnss_ins_float.other_State &= ~STATE_GPS_REFRESH;
         }
				 if(g_SysVar.Time_FineAlign >= TIME_FINE_ALIGN)
				 {
						if(g_SysVar.isGNSSValid == YES)
								g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;	
						else
								g_SysVar.WorkPhase = PHASE_INS_NAVI;	
						
						for(int i = 0;i<3;i++)
							g_Navi.Sn[i] = 0.0;
				 }				  
				 break;
			}
		  case PHASE_INS_NAVI ://纯惯
			{
          //导航计算
          NaviCompute(r_Gyro,r_LastGyro,Acc,LastAcc,&g_Navi);
          //滤波用矩阵的累积
          ComputeFn(&g_Navi, g_Kalman.Fn);
          
          g_SysVar.Time_INS_Alone += TIME_NAVI;
		  g_SysVar.Time_Integrated_Navi = 0;	
				
#ifndef DEVICE_TYPE_370_25J_6089		
          if(g_SysVar.isDampedOK == YES)		//陈调20240722：370-6089取消
          {
              ComputeFk(g_Kalman.Fn, g_Kalman.Fk);//							
              KalPredict(&g_Kalman);
              ErrCorrect_1_Navi_Time_For_INS(&g_Navi, &g_Kalman);                                
          }
#endif				
				
          if(g_SysVar.isGNSSValid == YES)
					{
						g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;
						g_SysVar.Time_INS_Alone = 0.0;
						for(int i = 0;i<3;i++)
								g_Navi.Sn[i] = 0.0;
					}				          
			    break;
			}
      case PHASE_INTEGRATED_NAVI ://卫惯组合
      {
          //导航计算
          NaviCompute(r_Gyro,r_LastGyro,Acc,LastAcc,&g_Navi);
			
          //滤波用矩阵的累积
          ComputeFn(&g_Navi, g_Kalman.Fn);
			
          
          g_SysVar.Time_Integrated_Navi += TIME_NAVI;
		  
		  g_SysVar.Time_INS_Alone = 0.0;
            
          if(g_SysVar.Time_Integrated_Navi >= 20.0)
          {
             g_SysVar.isDampedOK = YES;
          }

          if(g_Kalman.isKalmanStart == YES)
          {
         			//进行Kalman滤波主体部分的计算，该部分计算量较大,切分到4个IMU周期完成全部计算
						 if(g_Kalman.Work_Mode==FILTER)
						 {
								KalCompute(&g_GNSSData_In_Use, &g_Kalman);		
						 }
						 else
						 {
								KalPredict(&g_Kalman);
                                g_Kalman.State = 5;							 
						 }								
										
					     if(g_Kalman.State == 5)
						 {
							 g_Kalman.isKalmanStart  = NO;   //信号清除
							 g_Kalman.isCorrectError = YES;  //开启修正误差信号
							 g_Kalman.State = 0;
						 }
         
         }
		 if(g_Kalman.isCorrectError == YES)
         {
              //误差校正
              ErrStore_1s(&g_Navi, &g_Kalman,&g_GNSSData_In_Use);

              g_Kalman.isCorrectError = NO;
              //Gnss_ins_float.other_State &= ~STATE_GPS_REFRESH;
         }
				 if(g_SysVar.isGNSSValid == NO)
				 {
						g_SysVar.WorkPhase = PHASE_INS_NAVI;
						g_SysVar.Time_Integrated_Navi = 0.0;
						for(int i = 0;i<3;i++)
							g_Navi.Sn[i] = 0.0;
				 }		
				 break;
			}
      default:
      {
          g_SysVar.WorkPhase = PHASE_FAIL;    //故障状态
      }
  }
}

void GNSS_Lost_Time(void)
{
  //GNSS数据接收不到或一段时间无效后的处理
  if(g_SysVar.Time_Since_Last_GNSS > TIME_GNSS_LOST)//卫导超过TIME_GNSS_LOST时间
	{
		g_SysVar.isGNSSValid = NO;
		g_SysVar.isGNSS_Update = NO;
		g_GNSSData_In_Use.isHeadingEn = NO;
	}			
}


void gnss_check_bind(void)
{
	//GNSS数据接收及有效性判定及初始装订设置
	if(paochedata.gnss_update==1)
	{
		//g_SysVar.Time_Since_Last_GNSS = 0.0;
		//读取并判断GNSS数据有效性
	  //Read_And_Check_GNSS_Data(&g_GNSSData_In_Use,&paochedata,&g_Navi);
          Read_And_Check_GNSS_Data(&g_GNSSData_In_Use,&paochedata,&g_Navi,&g_SysVar);
		
#ifdef code_test_gyc_gao_chen_20240708
		if(g_SysVar.Time >= 660.0)
		{
#ifdef code_test_gyc_chen_gnsscheck_660sDisable_20240711			
			g_GNSSData_In_Use.isPosEn = NO;		
#endif			
		}
#endif		
		
		if(g_GNSSData_In_Use.isPosEn ==YES)
		{
			g_SysVar.Time_Since_Last_GNSS = 0.0;
		}
	  //初始装订设置
	  if((g_SysVar.WorkPhase == PHASE_STANDBY)&&(g_GNSSData_In_Use.isPosEn==YES))
	  {
			if(((g_InitBind.isBind != YES)||(g_InitBind.isHeadBind !=YES))&&(g_GNSSData_In_Use.isGNSS_Valid_3s == YES))
			{
				BindDefaultSet_by_GNSS(&g_InitBind,&g_GNSSData_In_Use);
				if(g_InitBind.isHeadBind==YES)
				{
					//双天线航向有效，进入快速对准
					g_SysVar.isRapidAlign = YES;				
				}
				else
				{
					//进入自寻北对准，是否精对准可选
					g_SysVar.isRapidAlign = NO;
				}
			}
		 }			
		 //滤波模式调节
		 Kalman_StartUp(&g_Kalman,&g_GNSSData_In_Use,&g_SysVar,&g_Navi);		
		 	
	}
}


void Virtual_PPS_insert_5hz(void)
{
		//内插的虚拟PPS，频率5Hz
  if(g_SysVar.isPPSStart == YES)
  {
			g_SysVar.Virtual_PPS_Count++;
			if(g_SysVar.Virtual_PPS_Count >= (COUNT)(TIME_FILTER / TIME_NAVI))
			{				
				g_SysVar.Virtual_PPS_Count = 0;
				if((g_SysVar.WorkPhase == PHASE_INS_NAVI)||(g_SysVar.WorkPhase == PHASE_INTEGRATED_NAVI))
				{
					SaveINSData(&g_Navi, &g_Kalman);
					ComputeFk(g_Kalman.Fn, g_Kalman.Fk);
					ComputeXkk_1(g_Kalman.Fk, g_Kalman.Xk, g_Kalman.Xkk_1);
					g_Kalman.isInsRecord = YES;	
				}					
			}
	}
}


void GNSS_Valid_PPSStart(void)
{	
		//PPS时刻的判定，使用卫导定位有效3s数据后
	if(g_GNSSData_In_Use.isGNSS_Valid_3s == YES)
	{		
		if(paochedata.num_clk==0)
		{
			if((g_SysVar.WorkPhase == PHASE_INS_NAVI)||(g_SysVar.WorkPhase == PHASE_INTEGRATED_NAVI))
			{
				SaveINSData(&g_Navi, &g_Kalman);
				g_Kalman.isInsRecord = YES;
				ComputeFk(g_Kalman.Fn, g_Kalman.Fk);
				ComputeXkk_1(g_Kalman.Fk, g_Kalman.Xk, g_Kalman.Xkk_1);
			}
			g_SysVar.Virtual_PPS_Count = 0;
			if(g_SysVar.isPPSStart != YES)
			{
				g_SysVar.isPPSStart = YES;	
			}	
		}
	}	
}


void GNSS_Last_TIME(void)
{
	//系统时间基准
	g_SysVar.Time += TIME_NAVI;
	//上次收到GNSS的时间，用于判定GNSS数据是否中断
	g_SysVar.Time_Since_Last_GNSS += TIME_NAVI;
	//GNSS_Last_TIME();
}

void ACC_gyroreset_r_TAFEAG16_buf( navoutdata_t *pnavout)
{
#ifdef test_gyc_FPGATo422_0x66AA_20240716
	pnavout->gyroX = gins912data.fogx*(1.0/stSetPara.FactorDyroX); 
	pnavout->gyroY = gins912data.fogy*(1.0/stSetPara.FactorDyroY); 
	pnavout->gyroZ = gins912data.fogz*(1.0/stSetPara.FactorDyroZ);
	
	gins912outputmode = UPPER_InsUart_Hd0x66AA ; // 对比 fgpa acc fog 的raw数据和alg1数据
	
    r_cfrdbuf16bit[3] = gfpgadata[1];
    r_cfrdbuf16bit[3] >>= 8;

    memcpy(&r_cfrdbuf16bit[4],&ginputdata.gddata[0x0E],30);

    memcpy(&r_cfrdbuf16bit[0x13],&pnavout->gyroX,4);
    memcpy(&r_cfrdbuf16bit[0x15],&pnavout->gyroY,4);
    memcpy(&r_cfrdbuf16bit[0x17],&pnavout->gyroZ,4);

    memcpy(&r_cfrdbuf16bit[0x19],&pnavout->accelX,4);
    memcpy(&r_cfrdbuf16bit[0x1B],&pnavout->accelY,4);
    memcpy(&r_cfrdbuf16bit[0x1D],&pnavout->accelZ,4);

    r_cfrdbuf16bit[0x1F] =  app_accum_verify_16bit(r_cfrdbuf16bit,FRAME_CFRDpart_16bitQty - 1 );
    
    uart4sendmsg((char*)r_cfrdbuf16bit, r_cfrdbuf16bit[2]);	
#endif
	
//#ifdef test_gyc_FPGATo422_0x00BB_20240716	
    //if(stSetPara.SetDataOutType==2)
    //{
    //    if(g_StartUpdateFirm != 1)//不在升级状态时，从才能发送数据
    //    {
    //        FPGATo422_00BB_send(pnavout);
    //    }
    //}
    FPGATo422_00BB_send(pnavout);
//#endif	
}

void fpgadata_Predo_chen_OutDataSet(navoutdata_t *pnavout)
{

///----------------------------原始代码里面的输出信息--------------------------------------------
	//pnavout->gyroX = gins912data.fogx*coe_fog_gx;
	//pnavout->gyroY = gins912data.fogy*coe_fog_gy; 
	//pnavout->gyroZ = gins912data.fogz*coe_fog_gz;

        pnavout->gyroX = gins912data.fogx;
	pnavout->gyroY = gins912data.fogy; 
	pnavout->gyroZ = gins912data.fogz;
	
	pnavout->accelX = gins912data.accelerometerx;
	pnavout->accelY = gins912data.accelerometery;
	pnavout->accelZ = gins912data.accelerometerz;

	pnavout->status = gins912data.rtkstatus >> 8;	//debug...
	pnavout->gps_week = gins912data.gnssweek;
	//pnavout->gps_time = gins912data.secondofweek;
        pnavout->gps_time = gins912data.millisecondofweek;
	
	ggpsorgdata.rtkStatus = gins912data.rtkstatus >> 8;
	ggpsorgdata.gpsweek = gins912data.gnssweek;
	ggpsorgdata.gpssecond = gins912data.secondofweek;
	ggpsorgdata.baseline = gpagedata.baselinelength;// gins912data.baselength * 1E-9;
	ggpsorgdata.StarNum = gins912data.gpsstarnumber;
	
	memset(&paochedata,0,sizeof(paochedata));
	fmc2sinsraw(gfpgadata,&paochedata);	
}

#if 1
//解析坐标轴(可通过上位机设置坐标系)
void AnalyticCoordinateAxis(void)
{
    unsigned char Axial=0;	//表示坐标系轴向

    Axial = stSetPara.SetCoord;

    switch(Axial)
    {
        case 0:	//X,Y,Z
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_x*(SetDir[stSetPara.SetDir][0]/stSetPara.FactorDyroX);
            gins912data.fogy = gpagedata.fog_y*(SetDir[stSetPara.SetDir][1]/stSetPara.FactorDyroY);
            gins912data.fogz = gpagedata.fog_z*(SetDir[stSetPara.SetDir][2]/stSetPara.FactorDyroZ);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[0] / (stSetPara.FactorAccX *SetDir[stSetPara.SetDir][0]* 256.0);
            gins912data.accelerometery = r_acc[1] / (stSetPara.FactorAccY *SetDir[stSetPara.SetDir][1]* 256.0);
            gins912data.accelerometerz = r_acc[2] / (stSetPara.FactorAccZ *SetDir[stSetPara.SetDir][2]* 256.0);
            break;
        case 1:	//X,Z,Y
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_x*(SetDir[stSetPara.SetDir][0]/stSetPara.FactorDyroX);
            gins912data.fogy = gpagedata.fog_z*(SetDir[stSetPara.SetDir][1]/stSetPara.FactorDyroZ);
            gins912data.fogz = gpagedata.fog_y*(SetDir[stSetPara.SetDir][2]/stSetPara.FactorDyroY);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[0] / (stSetPara.FactorAccX *SetDir[stSetPara.SetDir][0]* 256.0);
            gins912data.accelerometery = r_acc[2] / (stSetPara.FactorAccZ *SetDir[stSetPara.SetDir][1]* 256.0);
            gins912data.accelerometerz = r_acc[1] / (stSetPara.FactorAccY *SetDir[stSetPara.SetDir][2]* 256.0);
            break;
        case 2:	//Y,X,Z leishen-20250313
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_z*(SetDir[stSetPara.SetDir][0]/stSetPara.FactorDyroZ);
            gins912data.fogy = gpagedata.fog_x*(SetDir[stSetPara.SetDir][1]/stSetPara.FactorDyroX);
            gins912data.fogz = gpagedata.fog_y*(SetDir[stSetPara.SetDir][2]/stSetPara.FactorDyroY);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[2] / (stSetPara.FactorAccZ *SetDir[stSetPara.SetDir][0]* 256.0);
            gins912data.accelerometery = r_acc[0] / (stSetPara.FactorAccX *SetDir[stSetPara.SetDir][1]* 256.0);
            gins912data.accelerometerz = r_acc[1] / (stSetPara.FactorAccY *SetDir[stSetPara.SetDir][2]* 256.0);
            break;
        case 3:	//Y,Z,X
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_y*(SetDir[stSetPara.SetDir][0]/stSetPara.FactorDyroY);
            gins912data.fogy = gpagedata.fog_z*(SetDir[stSetPara.SetDir][1]/stSetPara.FactorDyroZ);
            gins912data.fogz = gpagedata.fog_x*(SetDir[stSetPara.SetDir][2]/stSetPara.FactorDyroX);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[1] / (stSetPara.FactorAccY *SetDir[stSetPara.SetDir][0]* 256.0);
            gins912data.accelerometery = r_acc[2] / (stSetPara.FactorAccZ *SetDir[stSetPara.SetDir][1]* 256.0);
            gins912data.accelerometerz = r_acc[0] / (stSetPara.FactorAccX *SetDir[stSetPara.SetDir][2]* 256.0);
            break;
        case 4:	//Z,X,Y
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_z*(SetDir[stSetPara.SetDir][0]/stSetPara.FactorDyroZ);
            gins912data.fogy = gpagedata.fog_x*(SetDir[stSetPara.SetDir][1]/stSetPara.FactorDyroX);
            gins912data.fogz = gpagedata.fog_y*(SetDir[stSetPara.SetDir][2]/stSetPara.FactorDyroY);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[2] / (stSetPara.FactorAccZ *SetDir[stSetPara.SetDir][0]* 256.0);
            gins912data.accelerometery = r_acc[0] / (stSetPara.FactorAccX *SetDir[stSetPara.SetDir][1]* 256.0);
            gins912data.accelerometerz = r_acc[1] / (stSetPara.FactorAccY *SetDir[stSetPara.SetDir][2]* 256.0);
            break;
        case 5:	//Z,Y,Z
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_z*(SetDir[stSetPara.SetDir][0]/stSetPara.FactorDyroZ);
            gins912data.fogy = gpagedata.fog_y*(SetDir[stSetPara.SetDir][1]/stSetPara.FactorDyroY);
            gins912data.fogz = gpagedata.fog_x*(SetDir[stSetPara.SetDir][2]/stSetPara.FactorDyroX);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[2] / (stSetPara.FactorAccZ *SetDir[stSetPara.SetDir][0]* 256.0);
            gins912data.accelerometery = r_acc[1] / (stSetPara.FactorAccY *SetDir[stSetPara.SetDir][1]* 256.0);
            gins912data.accelerometerz = r_acc[0] / (stSetPara.FactorAccX *SetDir[stSetPara.SetDir][2]* 256.0);
            break;

        default:
            break;
    }
}

#else
//解析坐标轴(不可通过上位机设置坐标系)
void AnalyticCoordinateAxis(void)
{
    unsigned char Axial=0;	//表示坐标系轴向

    Axial = 0;//stSetPara.SetCoord;

    switch(Axial)
    {
        case 0:	//X,Y,Z
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
            gins912data.fogy = gpagedata.fog_y*(1.0/stSetPara.FactorDyroY);
            gins912data.fogz = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[0] / (stSetPara.FactorAccX * -256.0);
            gins912data.accelerometery = r_acc[1] / (stSetPara.FactorAccY * 256.0);
            gins912data.accelerometerz = r_acc[2] / (stSetPara.FactorAccZ * -256.0);
            break;
        case 1:	//X,Z,Y
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
            gins912data.fogy = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
            gins912data.fogz = gpagedata.fog_y*(1.0/stSetPara.FactorDyroY);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[0] / (stSetPara.FactorAccX * -256.0);
            gins912data.accelerometery = r_acc[2] / (stSetPara.FactorAccZ * -256.0);
            gins912data.accelerometerz = r_acc[1] / (stSetPara.FactorAccY * 256.0);
            break;
        case 2:	//Y,X,Z leishen-20250313
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
            gins912data.fogy = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
            gins912data.fogz = gpagedata.fog_y*(-1.0/stSetPara.FactorDyroY);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[2] / (stSetPara.FactorAccZ * -256.0);
            gins912data.accelerometery = r_acc[0] / (stSetPara.FactorAccX * 256.0);
            gins912data.accelerometerz = r_acc[1] / (stSetPara.FactorAccY * -256.0);
            break;
        case 3:	//Y,Z,X
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_y*(1.0/stSetPara.FactorDyroY);
            gins912data.fogy = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
            gins912data.fogz = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[1] / (stSetPara.FactorAccY * 256.0);
            gins912data.accelerometery = r_acc[2] / (stSetPara.FactorAccZ * -256.0);
            gins912data.accelerometerz = r_acc[0] / (stSetPara.FactorAccX * -256.0);
            break;
        case 4:	//Z,X,Y
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
            gins912data.fogy = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
            gins912data.fogz = gpagedata.fog_y*(1.0/stSetPara.FactorDyroY);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[2] / (stSetPara.FactorAccZ * -256.0);
            gins912data.accelerometery = r_acc[0] / (stSetPara.FactorAccX * -256.0);
            gins912data.accelerometerz = r_acc[1] / (stSetPara.FactorAccY * 256.0);
            break;
        case 5:	//Z,Y,Z
            //陀螺坐标轴
            gins912data.fogx = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
            gins912data.fogy = gpagedata.fog_y*(1.0/stSetPara.FactorDyroY);
            gins912data.fogz = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
            
            //加计坐标轴
            gins912data.accelerometerx = r_acc[2] / (stSetPara.FactorAccZ * -256.0);
            gins912data.accelerometery = r_acc[1] / (stSetPara.FactorAccY * 256.0);
            gins912data.accelerometerz = r_acc[0] / (stSetPara.FactorAccX * -256.0);
            break;

        default:
            break;
    }
}
#endif

void fpgadata_Predo_chen_preAlgParm_370()
	//前期准备算法参数，370采用
{
  memcpy((void*)&r_fog[0],(uc8_t*)&gpagedata.fog_x,4);
  memcpy((void*)&r_fog[1],(uc8_t*)&gpagedata.fog_y,4);
  memcpy((void*)&r_fog[2],(uc8_t*)&gpagedata.fog_z,4);   
  memcpy((void*)&r_acc[0],(uc8_t*)&gpagedata.axis3_accx,4);
  memcpy((void*)&r_acc[1],(uc8_t*)&gpagedata.axis3_accy,4);
  memcpy((void*)&r_acc[2],(uc8_t*)&gpagedata.axis3_accz,4);  

  for(u8_t t = 0 ; t < 3; t++)
  {
    r_acc[t] <<= 8;
#ifdef DEVICE_ACC_TYPE_ADLX355	  
    r_acc[t] *= G0; 
#endif	  
  } 
#if 1
    //解析坐标轴
    AnalyticCoordinateAxis();
#else
  //陀螺正常版本
  //原始数据
  gins912data.fogx = gpagedata.fog_x*(1.0/stSetPara.FactorDyroX);
  gins912data.fogy = gpagedata.fog_z*(1.0/stSetPara.FactorDyroZ);
  gins912data.fogz = gpagedata.fog_y*(1.0/stSetPara.FactorDyroY);
  //更新中间件数据，用于客户坐标输出（组合导航输出）
  StrMiddleWare.fogx = gins912data.fogx;
  StrMiddleWare.fogy = gins912data.fogy;
  StrMiddleWare.fogz = gins912data.fogz;

  //加计正常版本
  //原始数据
  gins912data.accelerometerx = r_acc[0] / (stSetPara.FactorAccX * 256.0);
  gins912data.accelerometery = r_acc[1] / (stSetPara.FactorAccY * 256.0);
  gins912data.accelerometerz = r_acc[2] / (stSetPara.FactorAccZ * 256.0);
  //更新中间件数据，用于客户坐标输出（组合导航输出）
  StrMiddleWare.accelerometerx = gins912data.accelerometerx;
  StrMiddleWare.accelerometery = gins912data.accelerometery;
  StrMiddleWare.accelerometerz = gins912data.accelerometerz;
#endif

#ifdef param_gyc_26j_ACCY_fu
	gins912data.accelerometery = -1*gins912data.accelerometery;
#endif	
#ifdef param_gyc_6089_AccX_fu        //20240807 检测x加表数值反，6089，调整
        gins912data.accelerometerx = -1.0*gins912data.accelerometerx;
#endif        
}

void fpgadata_Predo_chen_preAlgParm_imu634()
	//前期准备算法参数，600-21A采用从IMU634获取
{
        stSetPara.FactorAccX = 4095;
        stSetPara.FactorDyroX = 160;

	gins912data.accelerometerx = gpagedata.accX_data / fabs(stSetPara.FactorAccX);
	gins912data.accelerometery = gpagedata.accY_data / fabs(stSetPara.FactorAccY);
	gins912data.accelerometerz = gpagedata.accZ_data / fabs(stSetPara.FactorAccZ);  

	//
	gins912data.fogx = gpagedata.rateX_data *(-1.0/stSetPara.FactorDyroX);
	gins912data.fogy = gpagedata.rateY_data *(-1.0/stSetPara.FactorDyroY);
	gins912data.fogz = gpagedata.rateZ_data *(-1.0/stSetPara.FactorDyroZ);
}

void fpgadata_Predo_chen_algParmCash()
	//算法参数缓存
{
	 //陀螺与加表数据转存
	 for(int i = 0; i < 3;i++)
	 {
		r_LastGyro[i] = r_Gyro[i];
		LastAcc[i] = Acc[i];
	 }	
}


void fpgadata_Predo_chen_SetAlgParm_setRParm_gyro()
	//设置校正参数-陀螺
{
	 //沿用原来的程序的补偿参数，后续温补后再调整
	 #ifdef DEVICE_TYPE_370_25J_355
                Gw[0][0] =  stSetPara.GyroCalibrate[0];	Gw[0][1] = stSetPara.GyroCalibrate[1]; Gw[0][2] = stSetPara.GyroCalibrate[2];
                Gw[1][0] =  stSetPara.GyroCalibrate[3];	Gw[1][1] = stSetPara.GyroCalibrate[4]; Gw[1][2] = stSetPara.GyroCalibrate[5];
                Gw[2][0] =  stSetPara.GyroCalibrate[6];	Gw[2][1] = stSetPara.GyroCalibrate[7]; Gw[2][2] = stSetPara.GyroCalibrate[8];

                gw.x = stSetPara.GyroCalibrate[9];
                gw.y = stSetPara.GyroCalibrate[10];
                gw.z = stSetPara.GyroCalibrate[11];	 
	 #endif
	 #ifdef DEVICE_TYPE_370_25J_6089		//19位小数 本组参数20240807重标定
                Gw[0][0] =  stSetPara.GyroCalibrate[0];	Gw[0][1] = stSetPara.GyroCalibrate[1]; Gw[0][2] = stSetPara.GyroCalibrate[2];
                Gw[1][0] =  stSetPara.GyroCalibrate[3];	Gw[1][1] = stSetPara.GyroCalibrate[4]; Gw[1][2] = stSetPara.GyroCalibrate[5];
                Gw[2][0] =  stSetPara.GyroCalibrate[6];	Gw[2][1] = stSetPara.GyroCalibrate[7]; Gw[2][2] = stSetPara.GyroCalibrate[8];

                gw.x = stSetPara.GyroCalibrate[9];
                gw.y = stSetPara.GyroCalibrate[10];
                gw.z = stSetPara.GyroCalibrate[11];	 
	 #endif
	 #ifdef DEVICE_TYPE_600_21A				//标定参数编号：15
		 Gw[0][0] =  1.0052583619045200000  ;	Gw[0][1] = 0.0045986860810939200  ; 	Gw[0][2] =  -0.0017705027973012300 ;
		 Gw[1][0] = -0.0018943749313386900  ;	Gw[1][1] = 1.0041746575154300000  ; 	Gw[1][2] =   0.0044352517835375400 ;
		 Gw[2][0] =  0.0014373439402182100  ;	Gw[2][1] = 0.0007789151926621660  ; 	Gw[2][2] =   0.9994695623986620000 ;

		 gw.x = -0.047009293999078700000  ;
		 gw.y =  0.088167640788083100000  ;
		 gw.z = -0.077492226635589900000  ;		 
	 #endif	
}

void fpgadata_Predo_chen_SetAlgParm_setRParm_acc()
	//设置校正参数-加加速度计
{
	#ifdef DEVICE_TYPE_370_25J_355
		Aw[0][0] = stSetPara.AccCalibrate[0]; Aw[0][1] = stSetPara.AccCalibrate[1];  Aw[0][2] = stSetPara.AccCalibrate[2];
		Aw[1][0] = stSetPara.AccCalibrate[3]; Aw[1][1] = stSetPara.AccCalibrate[4];  Aw[1][2] = stSetPara.AccCalibrate[5];
		Aw[2][0] = stSetPara.AccCalibrate[6]; Aw[2][1] = stSetPara.AccCalibrate[7];  Aw[2][2] = stSetPara.AccCalibrate[8];

		aw.x = stSetPara.AccCalibrate[9] ;
		aw.y = stSetPara.AccCalibrate[10];
		aw.z = stSetPara.AccCalibrate[11];	
	#endif
	#ifdef DEVICE_TYPE_370_25J_6089		//21位小数  本组参数20240807重标定
		Aw[0][0] = stSetPara.AccCalibrate[0]; Aw[0][1] = stSetPara.AccCalibrate[1];  Aw[0][2] = stSetPara.AccCalibrate[2];
		Aw[1][0] = stSetPara.AccCalibrate[3]; Aw[1][1] = stSetPara.AccCalibrate[4];  Aw[1][2] = stSetPara.AccCalibrate[5];
		Aw[2][0] = stSetPara.AccCalibrate[6]; Aw[2][1] = stSetPara.AccCalibrate[7];  Aw[2][2] = stSetPara.AccCalibrate[8];

		aw.x = stSetPara.AccCalibrate[9] ;
		aw.y = stSetPara.AccCalibrate[10];
		aw.z = stSetPara.AccCalibrate[11];		
	#endif
	#ifdef DEVICE_TYPE_600_21A				//标定参数编号：15
		Aw[0][0] =  1.00147511971598000000  ;	Aw[0][1] = 0.00584122940679086000 ;  Aw[0][2] =  0.00396441426980547000 ;
		Aw[1][0] = -0.00581546612653079000  ;	Aw[1][1] = 1.00105728895763000000 ;  Aw[1][2] = -0.00911193215983421000 ;
		Aw[2][0] = -0.00568708410427381000  ;	Aw[2][1] = 0.00586191030724166000 ;  Aw[2][2] =  1.00161512956674000000 ;

		aw.x = -0.004717392614018650000 ;
		aw.y =  0.002075697867229430000 ;
		aw.z = -0.003490087476048890000 ;		
	#endif
}
	
void fpgadata_Predo_chen_SetAlgParm_gyro()
	//矩阵计算校正参数并设置-陀螺
{
	//gWm.x =   gins912data.fogx*coe_fog_gx;//陀螺输出值
	//gWm.y =   gins912data.fogy*coe_fog_gy;
	//gWm.z =   gins912data.fogz*coe_fog_gz;

        gWm.x =   gins912data.fogx;//陀螺输出值
	gWm.y =   gins912data.fogy;
	gWm.z =   gins912data.fogz;

	gW_real.x = Gw[0][0]*(gWm.x - gw.x) + Gw[0][1]*(gWm.y - gw.y) + Gw[0][2]*(gWm.z - gw.z);
	gW_real.y = Gw[1][0]*(gWm.x - gw.x) + Gw[1][1]*(gWm.y - gw.y) + Gw[1][2]*(gWm.z - gw.z);
	gW_real.z = Gw[2][0]*(gWm.x - gw.x) + Gw[2][1]*(gWm.y - gw.y) + Gw[2][2]*(gWm.z - gw.z);

	//算法内部轴系转换为XYZ对应北天东
	r_Gyro[0] = gW_real.x * D2R;//调查局陀螺
	r_Gyro[1] = gW_real.y * D2R;
	r_Gyro[2] = gW_real.z * D2R;	

        //更新中间件数据，用于客户坐标输出（组合导航输出）
        StrMiddleWare.fogx = gW_real.z;
        StrMiddleWare.fogy = gW_real.x;
        StrMiddleWare.fogz = gW_real.y;
}

void fpgadata_Predo_chen_SetAlgParm_acc()
	//矩阵计算校正参数并设置-加速度计
{
	aWm.x =   gins912data.accelerometerx;//陀螺输出值		//20240718修改，替换成实际的加表来源
	aWm.y =   gins912data.accelerometery;
	aWm.z =   gins912data.accelerometerz;

	aW_real.x = Aw[0][0]*(aWm.x - aw.x) + Aw[0][1]*(aWm.y - aw.y) + Aw[0][2]*(aWm.z - aw.z);
	aW_real.y = Aw[1][0]*(aWm.x - aw.x) + Aw[1][1]*(aWm.y - aw.y) + Aw[1][2]*(aWm.z - aw.z);
	aW_real.z = Aw[2][0]*(aWm.x - aw.x) + Aw[2][1]*(aWm.y - aw.y) + Aw[2][2]*(aWm.z - aw.z);

	Acc[0] = aW_real.x * G;//HD6089加表
	Acc[1] = aW_real.y * G;
	Acc[2] = aW_real.z * G;	

      //更新中间件数据，用于客户坐标输出（组合导航输出）
      StrMiddleWare.accelerometerx = aW_real.z;
      StrMiddleWare.accelerometery = aW_real.x;
      StrMiddleWare.accelerometerz = aW_real.y;
}

void fpgadata_Predo_chen(navoutdata_t *pnavout)
//高给陈算法预处理代码20240708
{
#ifdef 	DEVICE_TYPE_370
	fpgadata_Predo_chen_preAlgParm_370();			//前期准备算法参数，370采用
#endif
#ifdef 	DEVICE_GYRO_TYPE_IMU_SCHA63X	
	fpgadata_Predo_chen_preAlgParm_imu634();		//前期准备算法参数，600-21A采用从IMU634获取
#endif		

	fpgadata_Predo_chen_OutDataSet(pnavout);		//原始代码使用，输出数据设置
	//-------------------------------------------------
	fpgadata_Predo_chen_algParmCash();			//算法参数缓存
	//设置算法函数使用的参数
	fpgadata_Predo_chen_SetAlgParm_setRParm_gyro();		//设置校正参数-陀螺
	fpgadata_Predo_chen_SetAlgParm_gyro();				//矩阵计算校正参数并设置-陀螺
	//-------------------------------------------------
	fpgadata_Predo_chen_SetAlgParm_setRParm_acc();		//设置校正参数-加加速度计
	fpgadata_Predo_chen_SetAlgParm_acc();				//矩阵计算校正参数并设置-加速度计
}

void test_gyc_chen_algdataTo422(void)
	//测试：//将压入算法数据直接通过422发送出去，20240709
{
	memcpy(&gins912data_cash, &gins912data, sizeof(gins912data));		//
	uart4sendmsg(gins912data_cash, sizeof(gins912data));
}


//void data_predo_useadlx355(navoutdata_t *pnavout)
//{

////--Update 2024.5.31-----
////[0] -- axis_x ; [1] -- axis_y ; [2] -- axis_z
//#ifdef CMPL_CODE_EDWOY

//  memcpy((void*)&r_fog[0],(uc8_t*)&gpagedata.fog_x,4);
//  memcpy((void*)&r_fog[1],(uc8_t*)&gpagedata.fog_y,4);
//  memcpy((void*)&r_fog[2],(uc8_t*)&gpagedata.fog_z,4);   
//  memcpy((void*)&r_acc[0],(uc8_t*)&gpagedata.axis3_accx,4);
//  memcpy((void*)&r_acc[1],(uc8_t*)&gpagedata.axis3_accy,4);
//  memcpy((void*)&r_acc[2],(uc8_t*)&gpagedata.axis3_accz,4);  

//  for(u8_t t = 0 ; t < 3; t++)
//  {
//    r_acc[t] <<= 8;
//    r_acc[t] *= G0; 
//  } //<for(u8_t t = 0 ; t < 3; t++)>
//  
//  gins912data.accelerometerx = r_acc[0] / (FACTOR_ACC * 256.0);
//  gins912data.accelerometery = r_acc[1] / (FACTOR_ACC * 256.0);
//  gins912data.accelerometerz = r_acc[2] / (FACTOR_ACC * 256.0);  
//	
//#ifdef param_gyc_26j_ACCY_fu
//	gins912data.accelerometery = -1 * gins912data.accelerometery ;
//#endif	

//#endif //<#ifdef CMPL_CODE_EDWOY>

/////----------------------------原始代码里面的输出信息--------------------------------------------
//	pnavout->gyroX = gins912data.fogx*coe_fog_gx;
//	pnavout->gyroY = gins912data.fogy*coe_fog_gy; 
//	pnavout->gyroZ = gins912data.fogz*coe_fog_gz;
//	
//	pnavout->accelX = gins912data.accelerometerx;
//	pnavout->accelY = gins912data.accelerometery;
//	pnavout->accelZ = gins912data.accelerometerz;

//	
//	pnavout->status = gins912data.rtkstatus >> 8;	//debug...
//	pnavout->gps_week = gins912data.gnssweek;
//	pnavout->gps_time = gins912data.secondofweek;

//	ggpsorgdata.rtkStatus = gins912data.rtkstatus >> 8;
//	ggpsorgdata.gpsweek 	= gins912data.gnssweek;
//	ggpsorgdata.gpssecond = gins912data.secondofweek;
//	ggpsorgdata.baseline 	= gpagedata.baselinelength;// gins912data.baselength * 1E-9;
//	ggpsorgdata.StarNum 	= gins912data.gpsstarnumber;


//	memset(&paochedata,0,sizeof(paochedata));
//	fmc2sinsraw(gfpgadata,&paochedata);
//	
//	{
//	  //替换了原来的ins_update()
//	
//	 //陀螺与加表数据转存
//	 for(int i = 0; i < 3;i++)
//	 {
//		r_LastGyro[i] = r_Gyro[i];
//		LastAcc[i] = Acc[i];
//	 }
//	//沿用原来的程序的补偿参数，后续温补后再调整		//20240720晚标定21J已经改变OK
//	 Gw[0][0] = 0.9998279011310630000  ;Gw[0][1] = 0.0149634414427812000 ; Gw[0][2] = 0.0004076646720536870 ;
//	 Gw[1][0] = -0.0140767813378351000 ;Gw[1][1] = 0.9999667444673410000 ; Gw[1][2] = -0.0025190191641069100;
//	 Gw[2][0] = 0.0038489683813327900  ;Gw[2][1] = 0.0045622586155020200 ; Gw[2][2] = 0.9998523783653160000 ;

//	 gw.x = -0.00000968241013037682000 ;
//	 gw.y = 0.00003385447078838340000 ;
//	 gw.z = -0.00000008903640261340770 ;		
//	//------------------------------以上是INS9123A调查局小型新机------------------------------------------------------------

//	gWm.x =   gins912data.fogx*coe_fog_gx;//陀螺输出值
//	gWm.y =   gins912data.fogy*coe_fog_gy;
//	gWm.z =   gins912data.fogz*coe_fog_gz;

//	gW_real.x = Gw[0][0]*(gWm.x - gw.x) + Gw[0][1]*(gWm.y - gw.y) + Gw[0][2]*(gWm.z - gw.z);
//	gW_real.y = Gw[1][0]*(gWm.x - gw.x) + Gw[1][1]*(gWm.y - gw.y) + Gw[1][2]*(gWm.z - gw.z);
//	gW_real.z = Gw[2][0]*(gWm.x - gw.x) + Gw[2][1]*(gWm.y - gw.y) + Gw[2][2]*(gWm.z - gw.z);

//	//算法内部轴系转换为XYZ对应北天东
//	#ifdef DEVICE_TYPE_370_25J_355	 
//		r_Gyro[0] = gW_real.x * D2R;//调查局陀螺
//		r_Gyro[2] = gW_real.y * D2R;
//		r_Gyro[1] = gW_real.z * D2R;
//	#endif
//	#ifdef DEVICE_TYPE_370_25J_6089
//		 //算法内部轴系转换为XYZ对应北天东，前上右
//		r_Gyro[0] = gW_real.x * D2R;//调查局陀螺//平放置
//		r_Gyro[2] = gW_real.y * D2R *(-1);
//		r_Gyro[1] = gW_real.z * D2R;
//	//	r_Gyro[0] = gW_real.y * D2R *(-1);//调查局陀螺//平放置，旋转90度
//	//	r_Gyro[2] = gW_real.x * D2R;
//	//	r_Gyro[1] = gW_real.z * D2R;
//	//	r_Gyro[0] = -gW_real.z * D2R;//调查局陀螺//挂壁放置
//	//	r_Gyro[2] = gW_real.y * D2R;
//	//	r_Gyro[1] = gW_real.x * D2R;//012前上右	
//	#endif

//	//----------------------------HD6089加计----------------------------------------
//   //--Update 2024.5.31 19:33-----
////[0] -- axis_x ; [1] -- axis_y ; [2] -- axis_z   
//#ifndef CMPL_CODE_EDWOY   
//  LastAcc[0] = paochedata.fog_ax * G;//HD6089加表
//  LastAcc[2] = paochedata.fog_ay * G;
//  LastAcc[1] = paochedata.fog_az * G;
//#else
//	Aw[0][0] = -1.00079981064368000000 ;Aw[0][1] = 0.02056110171772880000; Aw[0][2] = 0.00008164978645765910;	//好像与前面的设置冲突，要核实20240726
//	Aw[1][0] = 0.00815399369036132000 ;Aw[1][1] = 0.99983581050423100000 ; Aw[1][2] = -0.00385001964580666000;
//	Aw[2][0] = -0.00374613580495824000 ;Aw[2][1] = 0.00820153363407936000 ; Aw[2][2] = 1.00056631951804000000;

//	aw.x = 0.001403842369529830000 ;
//	aw.y = 0.000456213421119055000 ;
//	aw.z = -0.002520716252313810000 ;	
//	//------------------------------以上是INS9123A调查局小型新机------------------------------------------------------------

//	aWm.x =   paochedata.fog_ax;//陀螺输出值
//	aWm.y =   paochedata.fog_ay;
//	aWm.z =   paochedata.fog_az;

//	aW_real.x = Aw[0][0]*(aWm.x - aw.x) + Aw[0][1]*(aWm.y - aw.y) + Aw[0][2]*(aWm.z - aw.z);
//	aW_real.y = Aw[1][0]*(aWm.x - aw.x) + Aw[1][1]*(aWm.y - aw.y) + Aw[1][2]*(aWm.z - aw.z);
//	aW_real.z = Aw[2][0]*(aWm.x - aw.x) + Aw[2][1]*(aWm.y - aw.y) + Aw[2][2]*(aWm.z - aw.z);
//	
//	#ifdef DEVICE_TYPE_370_25J_355	 
//		Acc[0] = aW_real.x * G;//HD6089加表
//		Acc[2] = aW_real.y * G;
//		Acc[1] = aW_real.z * G;
//	#endif
//	#ifdef DEVICE_TYPE_370_25J_6089
//		Acc[0] = aW_real.x * G;//HD6089加表//平放置
//		Acc[2] = aW_real.y * G *(-1);
//		Acc[1] = aW_real.z * G;
//	//	Acc[0] = aW_real.y * G*(-1);//HD6089加表//平放置，旋转90度
//	//	Acc[2] = aW_real.x * G;
//	//	Acc[1] = aW_real.z * G;	
//	//  Acc[0] = -aW_real.z * G;//HD6089加表//挂壁放置
//	//	Acc[2] = aW_real.y * G;
//	//	Acc[1] = aW_real.x * G;	
//	#endif
//#endif
//  }
//}


void fpgadata_Predo(navoutdata_t *pnavout)
{
	fpgadata_Predo_chen(pnavout);

	GNSS_Last_TIME();
	
	GNSS_Valid_PPSStart();
	 
	Virtual_PPS_insert_5hz();
	
	gnss_check_bind();	

	GNSS_Lost_Time();	
	#ifdef test_gyc_chen_algdataTo422_20240709	//将压入算法数据直接通过422发送出去
		test_gyc_chen_algdataTo422();
	#endif
	ACC_gyroreset_r_TAFEAG16_buf(pnavout);
}

void pnavout_set(navoutdata_t *pnavout)
{
	//协议测试数据...
	#if 1
	//输出导航信息	pnavout传给上位机
	pnavout->roll     	= (float)g_Navi.d_Atti[2];
	pnavout->pitch    	= (float)g_Navi.d_Atti[1];
	pnavout->azimuth  	= (float)g_Navi.d_Atti[0];
	
	pnavout->vn     	= (float)g_Navi.Vn[0];
	pnavout->ve    		= (float)g_Navi.Vn[2];
	pnavout->vu  		= (float)g_Navi.Vn[1];
	
	pnavout->latitude   = g_Navi.d_Lati;
	pnavout->longitude  = g_Navi.d_Logi;
	pnavout->altitude   = g_Navi.Height;
	#endif
}

void FPGATo422_11BB_send(void)
	//通过串口将FPGA原始数据发送出去
{
    //输出频率设置
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    if(tCnt >= freq)
    {
        tCnt = 0;
	//包头
	int len = sizeof(gfpgadataSend);
	gfpgadataSend.head1    = 0x11BB;
	gfpgadataSend.head2    = 0xBDDB;
	gfpgadataSend.dataLen  = len;
	//FPGA原始数据
	gfpgadataSend.fpgadata = gpagedata;
	//包尾
	gfpgadataSend.fpgaItrCount = fpga_syn_count;
	gfpgadataSend.fpgaLoopCount= fpga_loop_count;	
	gfpgadataSend.Status       = g_SysVar.WorkPhase;

	memcpy(&r_TAFEAG8_buf[0],&gfpgadataSend, len);	
	gfpgadataSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf,len - 2);
	
        uart4sendmsg((char*)&gfpgadataSend, len);	
    }
}

void FPGATo422_00BB_send(navoutdata_t *pnavout)
	//通过串口将FPGA原始数据（预处理后的）发送出去
{
    //输出频率设置
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    if(tCnt >= freq)
    {
        tCnt = 0;
	//包头
	int len = sizeof(gfpgadataPredoSend);
	gfpgadataPredoSend.head1    = 0x00BB;
	gfpgadataPredoSend.head2    = 0xBDDB;
	gfpgadataPredoSend.dataLen  = len;
	//FPGA原始数据	
	gfpgadataPredoSend.fpgaPreDodata = gins912data;
        
        //gfpgadataPredoSend.fpgaPreDodata.selftestingcode = gpagedata.NUM_CLK;
	
	//pnavout->gyroX = gins912data.fogx * coe_fog_gx; 
	//pnavout->gyroY = gins912data.fogy * coe_fog_gy; 
	//pnavout->gyroZ = gins912data.fogz * coe_fog_gz;	
        //标定之前的陀螺
        pnavout->gyroX = gins912data.fogx; 
	pnavout->gyroY = gins912data.fogy; 
	pnavout->gyroZ = gins912data.fogz;	
        memcpy(&gfpgadataPredoSend.fpgaPreDodata.fogx ,&pnavout->gyroX,4);
        memcpy(&gfpgadataPredoSend.fpgaPreDodata.fogy ,&pnavout->gyroY,4);    
        memcpy(&gfpgadataPredoSend.fpgaPreDodata.fogz ,&pnavout->gyroZ,4);  	
	//标定之前的加计
	gfpgadataPredoSend.fpgaPreDodata.accelerometerx = pnavout->accelX; 
	gfpgadataPredoSend.fpgaPreDodata.accelerometery = pnavout->accelY; 
	gfpgadataPredoSend.fpgaPreDodata.accelerometerz = pnavout->accelZ; 
        //原始数据纬经高
        gfpgadataPredoSend.fpgaPreDodata.latitude = gins912data.latitude;//纬度
        gfpgadataPredoSend.fpgaPreDodata.longitude = gins912data.longitude;//经度
        gfpgadataPredoSend.fpgaPreDodata.altitude = gins912data.altitude;//高度


        //标定之后陀螺
	gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[0] = r_Gyro[0]/D2R;//x
	gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[1] = r_Gyro[1]/D2R;//y
	gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[2] = r_Gyro[2]/D2R;//z
	//标定之后的加计
	gfpgadataPredoSend.fpgaPreDodata_ex.Acc[0] = Acc[0]/G;//x
	gfpgadataPredoSend.fpgaPreDodata_ex.Acc[1] = Acc[1]/G;//y
	gfpgadataPredoSend.fpgaPreDodata_ex.Acc[2] = Acc[2]/G;//z

        //算法结算之后纬经高
        gfpgadataPredoSend.fpgaPreDodata.Alongitude = pnavout->latitude;//纬度
        gfpgadataPredoSend.fpgaPreDodata.Alatitude = pnavout->longitude;//经度
        gfpgadataPredoSend.fpgaPreDodata.Aaltitude = pnavout->altitude;//高度

        gfpgadataPredoSend.fpgaPreDodata.Ave = pnavout->ve;//东向速度
        gfpgadataPredoSend.fpgaPreDodata.Avn = pnavout->vn;//北向速度
        gfpgadataPredoSend.fpgaPreDodata.Avu = pnavout->vu;//天向速度

        gfpgadataPredoSend.fpgaPreDodata.Apitch = pnavout->pitch;//俯仰角
        gfpgadataPredoSend.fpgaPreDodata.Aroll = pnavout->roll;//横滚角
        gfpgadataPredoSend.fpgaPreDodata.Aheading = pnavout->azimuth;//航向角
	

	//包尾
	gfpgadataPredoSend.fpgaItrCount = fpga_syn_count;
	gfpgadataPredoSend.fpgaLoopCount= fpga_loop_count;	
	gfpgadataPredoSend.Status       = g_SysVar.WorkPhase;

	memcpy(&r_TAFEAG8_buf[0],&gfpgadataPredoSend, len);	
	gfpgadataPredoSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf,len - 2);

        //gSdDataSave.NUM_CLK = gfpgadataPredoSend.fpgaPreDodata.selftestingcode;
        //gSdDataSave.millisecondofweek = gfpgadataPredoSend.fpgaPreDodata.millisecondofweek;
        //gSdDataSave.r_Gyro[0] = gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[0];
        //gSdDataSave.r_Gyro[1] = gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[1];
        //gSdDataSave.r_Gyro[2] = gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[2];
        //gSdDataSave.Acc[0] = gfpgadataPredoSend.fpgaPreDodata_ex.Acc[0];
        //gSdDataSave.Acc[1] = gfpgadataPredoSend.fpgaPreDodata_ex.Acc[1];
        //gSdDataSave.Acc[2] = gfpgadataPredoSend.fpgaPreDodata_ex.Acc[2];
        g_BB00WriteSdFlag=1;
        memcpy(BB00SdData,&gfpgadataPredoSend, sizeof(gfpgadataPredoSend));

        if(stSetPara.SetDataOutType==2)
        {
            if((g_StartUpdateFirm != 1)&&(SetSdOperateType !=0x03))//不在升级状态时，从才能发送数据
            {
                uart4sendmsg((char*)&gfpgadataPredoSend, len);		
            }
        } 
    }
}

//读取SD卡并发送
void SDTo422_00BB_send(navoutdata_t *pnavout)
{
	//包头
	int len = sizeof(gfpgadataPredoSend);
	gfpgadataPredoSend.head1    = 0x00BB;
	gfpgadataPredoSend.head2    = 0xBDDB;
	gfpgadataPredoSend.dataLen  = len;
	//FPGA原始数据	
	gfpgadataPredoSend.fpgaPreDodata = gins912data;	
	
        gfpgadataPredoSend.fpgaPreDodata.selftestingcode = gSdDataSave.NUM_CLK;
        gfpgadataPredoSend.fpgaPreDodata.millisecondofweek = gSdDataSave.millisecondofweek;

        //标定之前的陀螺
        pnavout->gyroX = gins912data.fogx; 
	pnavout->gyroY = gins912data.fogy; 
	pnavout->gyroZ = gins912data.fogz;	
        memcpy(&gfpgadataPredoSend.fpgaPreDodata.fogx ,&pnavout->gyroX,4);
        memcpy(&gfpgadataPredoSend.fpgaPreDodata.fogy ,&pnavout->gyroY,4);    
        memcpy(&gfpgadataPredoSend.fpgaPreDodata.fogz ,&pnavout->gyroZ,4);  	
	//标定之前的加计
	gfpgadataPredoSend.fpgaPreDodata.accelerometerx = pnavout->accelX; 
	gfpgadataPredoSend.fpgaPreDodata.accelerometery = pnavout->accelY; 
	gfpgadataPredoSend.fpgaPreDodata.accelerometerz = pnavout->accelZ; 
        //原始数据纬经高
        gfpgadataPredoSend.fpgaPreDodata.latitude = gins912data.latitude;//纬度
        gfpgadataPredoSend.fpgaPreDodata.longitude = gins912data.longitude;//经度
        gfpgadataPredoSend.fpgaPreDodata.altitude = gins912data.altitude;//高度


 //       //标定之后陀螺
	//gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[0] = r_Gyro[0]/D2R;//x
	//gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[1] = r_Gyro[1]/D2R;//y
	//gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[2] = r_Gyro[2]/D2R;//z
	////标定之后的加计
	//gfpgadataPredoSend.fpgaPreDodata_ex.Acc[0] = Acc[0]/G;//x
	//gfpgadataPredoSend.fpgaPreDodata_ex.Acc[1] = Acc[1]/G;//y
	//gfpgadataPredoSend.fpgaPreDodata_ex.Acc[2] = Acc[2]/G;//z

        gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[0] = gSdDataSave.r_Gyro[0];
        gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[1] = gSdDataSave.r_Gyro[1];
        gfpgadataPredoSend.fpgaPreDodata_ex.r_Gyro[2] = gSdDataSave.r_Gyro[2];

        gfpgadataPredoSend.fpgaPreDodata_ex.Acc[0] = gSdDataSave.Acc[0];
        gfpgadataPredoSend.fpgaPreDodata_ex.Acc[1] = gSdDataSave.Acc[1];
        gfpgadataPredoSend.fpgaPreDodata_ex.Acc[2] = gSdDataSave.Acc[2];

        //算法结算之后纬经高
        gfpgadataPredoSend.fpgaPreDodata.Alongitude = pnavout->latitude;//纬度
        gfpgadataPredoSend.fpgaPreDodata.Alatitude = pnavout->longitude;//经度
        gfpgadataPredoSend.fpgaPreDodata.Aaltitude = pnavout->altitude;//高度

        gfpgadataPredoSend.fpgaPreDodata.Ave = pnavout->ve;//东向速度
        gfpgadataPredoSend.fpgaPreDodata.Avn = pnavout->vn;//北向速度
        gfpgadataPredoSend.fpgaPreDodata.Avu = pnavout->vu;//天向速度

        gfpgadataPredoSend.fpgaPreDodata.Apitch = pnavout->pitch;//俯仰角
        gfpgadataPredoSend.fpgaPreDodata.Aroll = pnavout->roll;//横滚角
        gfpgadataPredoSend.fpgaPreDodata.Aheading = pnavout->azimuth;//航向角
	

	//包尾
	gfpgadataPredoSend.fpgaItrCount = fpga_syn_count;
	gfpgadataPredoSend.fpgaLoopCount= fpga_loop_count;	
	gfpgadataPredoSend.Status       = g_SysVar.WorkPhase;

	memcpy(&r_TAFEAG8_buf[0],&gfpgadataPredoSend, len);	
	gfpgadataPredoSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf,len - 2);

        uart4sendmsg((char*)&gfpgadataPredoSend, len);	 
}

void INS912AlgorithmEntry(unsigned short *pfpgadata, navcanin_t *pcanin, navoutdata_t *pnavout)
{
	//Count1++;

	memcpy(ginputdata.gddata, pfpgadata, sizeof(ginputdata.gddata));
	memcpy(&gins912data, &ginputdata.wcdata, sizeof(gins912data));		//
        if((gins912data.Pos_Type == 69)&&((gins912data.rtkstatus >> 8 )!= 0x4))
        {
            gins912data.rtkstatus = (0x06<<8)|(gins912data.rtkstatus&0x00FF);//设置RTK状态为6
        }

#ifdef test_gyc_FPGATo422_0x11BB_20240722
	FPGATo422_11BB_send();
#endif	
	
#ifdef param_gyc_26j_fogz_fu	
	gins912data.fogz = -1 * gins912data.fogz ;
#endif	
	
	fpgadata_Predo(pnavout);		//数据预转换
	//if (Count1==2)
	{
		AlgorithmAct();							//实际算法处理
		//Count1=0;
		paochedata.gnss_update = 0;
	}

	pnavout_set(pnavout);				//结果输出设置

}

void AlgorithmDo()
	//算法总入口函数
{
        //FPGATo422_00BB_send(&gnavout);
	Algorithm_before_otherDataDo(canin);
	
	INS912AlgorithmEntry(gfpgadata, &canin, &gnavout);            
}
