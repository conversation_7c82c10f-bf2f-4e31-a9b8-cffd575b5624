/*!
    \file  systick.h
    \brief the header file of systick
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef SYS_TICK_H
#define SYS_TICK_H

#include <stdint.h>

/* delay a time in milliseconds */
void delay_1ms(uint32_t count);

void delay_init(uint8_t SYSCLK);


void delay_us(uint32_t nus);
void delay_ms(uint32_t nms);
void delay_xms(uint32_t nms);

#endif /* SYS_TICK_H */
