/*!
    \file  nav_kf.h
    \brief Navigation Kalman filter header file
*/

#ifndef __NAV_KF_H
#define __NAV_KF_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "nav.h"

/* Kalman filter status */
typedef enum {
    NAV_KF_STATUS_OK = 0,
    NAV_KF_STATUS_ERROR
} nav_kf_status_t;

/* <PERSON>lman filter state dimension */
#define NAV_KF_STATE_DIM    15

/* Kalman filter state structure */
typedef struct {
    float x[NAV_KF_STATE_DIM];  /* State vector */
    bool valid;                 /* State validity */
    uint32_t count;             /* Update count */
} nav_kf_state_t;

/* <PERSON>lman filter parameters */
typedef struct {
    float process_noise;        /* Process noise variance */
    float measurement_noise;    /* Measurement noise variance */
    float initial_uncertainty;  /* Initial state uncertainty */
} nav_kf_params_t;

/* Function prototypes */
nav_kf_status_t nav_kf_init(void);
nav_kf_status_t nav_kf_update(const nav_gnss_data_t *gnss_data);
nav_kf_status_t nav_kf_get_state(nav_kf_state_t *state);
nav_kf_status_t nav_kf_reset(void);
nav_kf_status_t nav_kf_set_params(const nav_kf_params_t *params);
nav_kf_status_t nav_kf_get_params(nav_kf_params_t *params);

#ifdef __cplusplus
}
#endif

#endif /* __NAV_KF_H */
