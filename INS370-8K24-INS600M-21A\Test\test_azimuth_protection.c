/***********************************************************************************
航向角保护功能测试程序
All rights reserved I-NAV 2023 2033
***********************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "nav_includes.h"
#include "SetParaBao.h"

// 全局变量定义
_NAV_Data_Full_t NAV_Data_Full;
SetParaTypeDef stSetPara;

// 简化的测试函数
void test_azimuth_protection(void)
{
    printf("=== 航向角保护功能测试 ===\n\n");
    
    // 1. 初始化
    printf("步骤1: 初始化系统\n");
    memset(&NAV_Data_Full, 0, sizeof(_NAV_Data_Full_t));
    memset(&stSetPara, 0, sizeof(SetParaTypeDef));
    
    // 初始化航向角保存系统
    InitAzimuthSaveSystem();
    
    // 2. 保存一个测试航向角
    double test_azimuth = M_PI / 4.0; // 45度
    printf("步骤2: 保存测试航向角 %.2f 度\n", test_azimuth * 180.0 / M_PI);
    SaveAzimuthToFlash(test_azimuth);
    
    if (!IsAzimuthSaveValid()) {
        printf("错误: 航向角保存失败!\n");
        return;
    }
    
    // 3. 设置基本的系统状态
    printf("步骤3: 设置系统状态\n");
    NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;
    NAV_Data_Full.UseCase = E_USE_CASE_In_Vehicle;
    NAV_Data_Full.Nav_Standard_flag = E_NAV_STANDARD_NO_PROCCSS;
    NAV_Data_Full.SINS.ts = 0.005;
    NAV_Data_Full.SINS.nts = 0.005;
    
    // 设置IMU数据
    NAV_Data_Full.Macc[0] = 0.0;
    NAV_Data_Full.Macc[1] = 0.0;
    NAV_Data_Full.Macc[2] = 9.8;
    NAV_Data_Full.SINS.db[0] = 0.0;
    NAV_Data_Full.SINS.db[1] = 0.0;
    NAV_Data_Full.SINS.db[2] = 0.0;
    NAV_Data_Full.SINS.eb[0] = 0.0;
    NAV_Data_Full.SINS.eb[1] = 0.0;
    NAV_Data_Full.SINS.eb[2] = 0.0;
    
    // 设置IMU使用数据
    NAV_Data_Full.IMU.gyro_use[0] = 0.001;
    NAV_Data_Full.IMU.gyro_use[1] = 0.001;
    NAV_Data_Full.IMU.gyro_use[2] = 0.001;
    NAV_Data_Full.IMU.acc_use[0] = 0.0;
    NAV_Data_Full.IMU.acc_use[1] = 0.0;
    NAV_Data_Full.IMU.acc_use[2] = 9.8;
    
    // 复制到previous数据
    for (int i = 0; i < 3; i++) {
        NAV_Data_Full.IMU.gyro_use_pre[i] = NAV_Data_Full.IMU.gyro_use[i];
        NAV_Data_Full.IMU.acc_use_pre[i] = NAV_Data_Full.IMU.acc_use[i];
    }
    
    // 设置地球参数
    NAV_Data_Full.EARTH.wnin[0] = 0.0;
    NAV_Data_Full.EARTH.wnin[1] = 0.0;
    NAV_Data_Full.EARTH.wnin[2] = 0.0;
    NAV_Data_Full.EARTH.gcc[0] = 0.0;
    NAV_Data_Full.EARTH.gcc[1] = 0.0;
    NAV_Data_Full.EARTH.gcc[2] = -9.8;
    
    // 4. 调用SINS_Init
    printf("步骤4: 调用SINS_Init\n");
    SINS_Init(&NAV_Data_Full);
    
    printf("SINS_Init后:\n");
    printf("  Pre_att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.Pre_att[2], NAV_Data_Full.Pre_att[2] * 180.0 / M_PI);
    printf("  SINS.att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("  Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);
    
    // 检查SINS_Init的结果
    double diff_init = fabs(NAV_Data_Full.SINS.att[2] - test_azimuth);
    if (diff_init < 0.001) {
        printf("✓ SINS_Init成功: 航向角正确设置\n");
    } else {
        printf("✗ SINS_Init失败: 航向角未正确设置\n");
        printf("  期望: %.6f 弧度, 实际: %.6f 弧度, 差值: %.6f 弧度\n", 
               test_azimuth, NAV_Data_Full.SINS.att[2], diff_init);
    }
    
    // 5. 测试SINS_Update保护功能
    printf("\n步骤5: 测试SINS_Update保护功能\n");
    
    printf("SINS_Update前:\n");
    printf("  SINS.att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("  Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);
    
    // 调用SINS_Update
    SINS_Update(&NAV_Data_Full);
    
    printf("SINS_Update后:\n");
    printf("  SINS.att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("  Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);
    
    // 检查SINS_Update的结果
    double diff_update = fabs(NAV_Data_Full.SINS.att[2] - test_azimuth);
    printf("  差值: %.6f 弧度 (%.2f 度)\n", diff_update, diff_update * 180.0 / M_PI);
    
    if (diff_update < 0.001) {
        printf("✓ SINS_Update成功: 航向角被正确保护\n");
    } else {
        printf("✗ SINS_Update失败: 航向角被错误覆盖\n");
    }
    
    // 6. 测试多次SINS_Update调用
    printf("\n步骤6: 测试多次SINS_Update调用\n");
    
    for (int i = 0; i < 5; i++) {
        printf("第%d次SINS_Update调用:\n", i + 2);
        SINS_Update(&NAV_Data_Full);
        printf("  SINS.att[2]: %.6f 弧度 (%.2f 度)\n", 
               NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
        printf("  Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);
        
        double diff_multi = fabs(NAV_Data_Full.SINS.att[2] - test_azimuth);
        printf("  差值: %.6f 弧度 (%.2f 度)\n", diff_multi, diff_multi * 180.0 / M_PI);
        
        if (diff_multi < 0.001) {
            printf("  ✓ 航向角保持正确\n");
        } else {
            printf("  ✗ 航向角发生偏移\n");
        }
        printf("\n");
    }
    
    printf("=== 航向角保护功能测试完成 ===\n");
}

int main(void)
{
    printf("航向角保护功能测试程序\n");
    printf("=====================================\n\n");
    
    test_azimuth_protection();
    
    printf("\n按任意键退出...\n");
    getchar();
    
    return 0;
}
