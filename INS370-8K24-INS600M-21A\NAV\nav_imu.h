/*!
    \file  nav_imu.h
    \brief Navigation IMU interface header file
*/

#ifndef __NAV_IMU_H
#define __NAV_IMU_H

#include "nav.h"

typedef enum {
    NAV_IMU_STATUS_OK = 0,
    NAV_IMU_STATUS_ERROR
} nav_imu_status_t;

typedef struct {
    float gyro[3];
    float acc[3];
    uint32_t timestamp;
    bool valid;
} nav_imu_raw_t;

typedef struct {
    float bias[6];      /* Gyro and acc bias */
    float scale[6];     /* Gyro and acc scale */
    float misalign[9];  /* Misalignment matrix */
} nav_imu_calib_t;

nav_imu_status_t nav_imu_init(void);
nav_imu_status_t nav_imu_calibrate(nav_imu_calib_t *calib);
nav_imu_status_t nav_imu_process(const nav_imu_raw_t *raw, nav_imu_data_t *processed);

#endif /* __NAV_IMU_H */
