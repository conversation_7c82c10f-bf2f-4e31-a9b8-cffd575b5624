/*!
    \file  gd32f4xx_it.h
    \brief the header file of the ISR
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef GD32F4XX_IT_H
#define GD32F4XX_IT_H

#include "appmain.h"

void EXTI5_9_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(void);
void EXTI3_IRQHandler(void);
void EXTI10_15_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(void);

void TIMER0_UP_TIMER9_IRQHandler(void);
void TIMER2_IRQHandler(void);
void TIMER1_IRQHandler(void);


void DMA0_Channel7_IRQHandler(void);
/* DMA0 channel2 handle function */
void DMA0_Channel2_IRQHandler(void);
/* DMA0 channel0 handle function */
void DMA0_Channel0_IRQHandler(void);
/* DMA0 channel4 handle function */
void DMA0_Channel4_IRQHandler(void);

#endif /* GD32F4XX_IT_H */
