/***********************************************************************************
航向角掉电保存问题调试程序
All rights reserved I-NAV 2023 2033
***********************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "nav_includes.h"
#include "SetParaBao.h"

// 全局变量定义
_NAV_Data_Full_t NAV_Data_Full;
SetParaTypeDef stSetPara;

// 调试函数：打印系统状态
void print_system_state(const char* stage)
{
    printf("\n=== %s ===\n", stage);
    printf("系统状态:\n");
    printf("  UseCase: %u\n", NAV_Data_Full.UseCase);
    printf("  ins_buffer_full_flag: %u\n", NAV_Data_Full.ins_buffer_full_flag);
    printf("  ins_buffer_full_cnt: %u\n", NAV_Data_Full.ins_buffer_full_cnt);
    printf("  Nav_Standard_flag: %u\n", NAV_Data_Full.Nav_Standard_flag);
    
    printf("GPS状态:\n");
    printf("  gps_up_flag: %u\n", NAV_Data_Full.GPS.gps_up_flag);
    printf("  rtkStatus: %u\n", NAV_Data_Full.GPS.rtkStatus);
    printf("  headingStatus: %u\n", NAV_Data_Full.GPS.headingStatus);
    printf("  Heading_cor: %.2f 度\n", NAV_Data_Full.GPS.Heading_cor);
    
    printf("航向角状态:\n");
    printf("  Pre_att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.Pre_att[2], NAV_Data_Full.Pre_att[2] * 180.0 / M_PI);
    printf("  SINS.att[2]: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    
    printf("保存状态:\n");
    printf("  SavedAzimuth: %.6f 弧度 (%.2f 度)\n", 
           stSetPara.SavedAzimuth, stSetPara.SavedAzimuth * 180.0 / M_PI);
    printf("  AzimuthValidFlag: 0x%08X (%s)\n", 
           stSetPara.AzimuthValidFlag, 
           IsAzimuthSaveValid() ? "有效" : "无效");
    printf("  AzimuthSaveCount: %u\n", stSetPara.AzimuthSaveCount);
    
    printf("IMU数据:\n");
    printf("  Macc: [%.3f, %.3f, %.3f]\n", 
           NAV_Data_Full.Macc[0], NAV_Data_Full.Macc[1], NAV_Data_Full.Macc[2]);
    printf("  SINS.db: [%.6f, %.6f, %.6f]\n", 
           NAV_Data_Full.SINS.db[0], NAV_Data_Full.SINS.db[1], NAV_Data_Full.SINS.db[2]);
}

// 模拟系统启动过程
void simulate_system_startup(void)
{
    printf("=== 模拟系统启动过程 ===\n\n");
    
    // 1. 初始化全局变量（模拟main函数中的初始化）
    printf("步骤1: 初始化全局变量\n");
    memset(&NAV_Data_Full, 0, sizeof(_NAV_Data_Full_t));
    memset(&stSetPara, 0, sizeof(SetParaTypeDef));
    
    // 设置main函数中的初始值
    NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;
    NAV_Data_Full.ZUPT_flag = RETURN_FAIL;
    NAV_Data_Full.acc_gyr_Cnt = 0;
    NAV_Data_Full.Nav_Status = E_NAV_STATUS_START;
    NAV_Data_Full.ins_buffer_full_cnt = 0;
    NAV_Data_Full.Modacc = 0.0;
    NAV_Data_Full.Modgyr = 0.0;
    NAV_Data_Full.Modacc_std2 = 0.0;
    NAV_Data_Full.Modgyr_std2 = 0.0;
    NAV_Data_Full.Macc[0] = 0.0;
    NAV_Data_Full.Macc[1] = 0.0;
    NAV_Data_Full.Macc[2] = 9.8; // 设置重力加速度
    NAV_Data_Full.Mgyr[0] = 0.0;
    NAV_Data_Full.Mgyr[1] = 0.0;
    NAV_Data_Full.Mgyr[2] = 0.0;
    NAV_Data_Full.SINS.Init_flag = RETURN_FAIL;
    NAV_Data_Full.Pre_att_flag = RETURN_FAIL;
    NAV_Data_Full.GPSlastnum = 0;
    
    print_system_state("初始化后");
    
    // 2. 模拟ReadParaFromFlash（包含InitAzimuthSaveSystem）
    printf("\n步骤2: 模拟ReadParaFromFlash\n");
    InitAzimuthSaveSystem();
    
    // 模拟之前保存的航向角
    double saved_azimuth = M_PI / 3.0; // 60度
    printf("模拟保存航向角: %.6f 弧度 (%.2f 度)\n", saved_azimuth, saved_azimuth * 180.0 / M_PI);
    SaveAzimuthToFlash(saved_azimuth);
    
    print_system_state("保存航向角后");
    
    // 3. 模拟导航状态机的执行
    printf("\n步骤3: 模拟导航状态机\n");
    
    // 模拟E_NAV_STATUS_START状态
    printf("当前状态: E_NAV_STATUS_START\n");
    // 在实际系统中，这里会调用Param_Data_Init和Load_Standard_Data
    
    // 模拟状态转换到E_NAV_STATUS_SINS_KF_INITIAL
    NAV_Data_Full.Nav_Status = E_NAV_STATUS_SINS_KF_INITIAL;
    printf("状态转换到: E_NAV_STATUS_SINS_KF_INITIAL\n");
    
    print_system_state("状态转换后");
    
    // 4. 调用SINS_Init
    printf("\n步骤4: 调用SINS_Init\n");
    SINS_Init(&NAV_Data_Full);
    
    print_system_state("SINS_Init后");
    
    // 5. 检查SINS_Init后的结果
    printf("\n步骤5: 检查SINS_Init后的结果\n");
    double expected_azimuth = saved_azimuth;
    double actual_azimuth = NAV_Data_Full.SINS.att[2];
    double diff = fabs(actual_azimuth - expected_azimuth);

    printf("期望航向角: %.6f 弧度 (%.2f 度)\n", expected_azimuth, expected_azimuth * 180.0 / M_PI);
    printf("实际航向角: %.6f 弧度 (%.2f 度)\n", actual_azimuth, actual_azimuth * 180.0 / M_PI);
    printf("差值: %.6f 弧度 (%.2f 度)\n", diff, diff * 180.0 / M_PI);
    printf("Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);

    if (diff < 0.001) {
        printf("✓ SINS_Init成功: 航向角正确恢复\n");
    } else {
        printf("✗ SINS_Init失败: 航向角未正确恢复\n");
    }

    // 6. 模拟SINS_Update调用（这是关键测试）
    printf("\n步骤6: 模拟SINS_Update调用\n");

    // 设置基本的IMU数据用于SINS_Update
    NAV_Data_Full.IMU.gyro_use[0] = 0.001; // 小的角速度
    NAV_Data_Full.IMU.gyro_use[1] = 0.001;
    NAV_Data_Full.IMU.gyro_use[2] = 0.001;
    NAV_Data_Full.IMU.acc_use[0] = 0.0;
    NAV_Data_Full.IMU.acc_use[1] = 0.0;
    NAV_Data_Full.IMU.acc_use[2] = 9.8;

    // 复制到previous数据
    for (int i = 0; i < 3; i++) {
        NAV_Data_Full.IMU.gyro_use_pre[i] = NAV_Data_Full.IMU.gyro_use[i];
        NAV_Data_Full.IMU.acc_use_pre[i] = NAV_Data_Full.IMU.acc_use[i];
    }

    // 设置地球参数
    NAV_Data_Full.EARTH.wnin[0] = 0.0;
    NAV_Data_Full.EARTH.wnin[1] = 0.0;
    NAV_Data_Full.EARTH.wnin[2] = 0.0;
    NAV_Data_Full.EARTH.gcc[0] = 0.0;
    NAV_Data_Full.EARTH.gcc[1] = 0.0;
    NAV_Data_Full.EARTH.gcc[2] = -9.8;

    printf("调用SINS_Update前的航向角: %.6f 弧度 (%.2f 度)\n",
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("调用SINS_Update前的Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);

    // 调用SINS_Update
    SINS_Update(&NAV_Data_Full);

    printf("调用SINS_Update后的航向角: %.6f 弧度 (%.2f 度)\n",
           NAV_Data_Full.SINS.att[2], NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("调用SINS_Update后的Pre_att_flag: %u\n", NAV_Data_Full.Pre_att_flag);

    // 检查航向角是否被保护
    double diff_after_update = fabs(NAV_Data_Full.SINS.att[2] - expected_azimuth);
    printf("SINS_Update后的差值: %.6f 弧度 (%.2f 度)\n", diff_after_update, diff_after_update * 180.0 / M_PI);

    if (diff_after_update < 0.001) {
        printf("✓ SINS_Update成功: 航向角被正确保护\n");
    } else {
        printf("✗ SINS_Update失败: 航向角被错误覆盖\n");
    }
    
    // 7. 分析StartCoarseAlign的执行路径
    printf("\n步骤7: 分析StartCoarseAlign执行路径\n");
    
    // 检查第一个if条件
    if ((E_USE_CASE_In_UAV == NAV_Data_Full.UseCase) && 0) {
        printf("执行路径: UAV分支（不会执行，因为有&&0）\n");
    }
    // 检查第二个else if条件
    else if ((E_USE_CASE_In_Vehicle == NAV_Data_Full.UseCase)
             && (NAV_Data_Full.GPS.gps_up_flag == E_GPS_IS_UPDATE)
             && ((NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED) || (NAV_Data_Full.GPS.NO_RTK_heading_flag))
             && (NAV_Data_Full.GPS.headingStatus == E_GPS_RTK_FIXED)
             && (NAV_Data_Full.ins_buffer_full_flag)) {
        printf("执行路径: GPS分支\n");
    }
    // 默认分支
    else {
        printf("执行路径: 默认分支\n");
        printf("  UseCase == E_USE_CASE_In_Vehicle: %s\n", 
               (E_USE_CASE_In_Vehicle == NAV_Data_Full.UseCase) ? "是" : "否");
        printf("  GPS.gps_up_flag == E_GPS_IS_UPDATE: %s\n", 
               (NAV_Data_Full.GPS.gps_up_flag == E_GPS_IS_UPDATE) ? "是" : "否");
        printf("  GPS.rtkStatus == E_GPS_RTK_FIXED: %s\n", 
               (NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED) ? "是" : "否");
        printf("  GPS.headingStatus == E_GPS_RTK_FIXED: %s\n", 
               (NAV_Data_Full.GPS.headingStatus == E_GPS_RTK_FIXED) ? "是" : "否");
        printf("  ins_buffer_full_flag: %s\n", 
               NAV_Data_Full.ins_buffer_full_flag ? "是" : "否");
        printf("  IsAzimuthSaveValid(): %s\n", 
               IsAzimuthSaveValid() ? "是" : "否");
    }
}

int main(void)
{
    printf("航向角掉电保存问题调试程序\n");
    printf("=====================================\n\n");
    
    simulate_system_startup();
    
    printf("\n按任意键退出...\n");
    getchar();
    
    return 0;
}
