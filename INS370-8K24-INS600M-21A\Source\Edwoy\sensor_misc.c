#include "appmain.h"
#include "sensor_misc.h"

/*****************************************************************
//243  324
//
 * \brief 
 * \param 
 * \param 
 * \return 
******************************************************************/
int complement2original(int num)
{
    int original = 0;
    int i = 0;

    while ((num >> i) != 0) {
        original |= (~(num >> i) & 1) << i;
        i++;
    }

    return original;
}

int app_hd6089data_covert(int orix)
{
  int newx;
    //
  if((orix & 0x800000) >> 23){
          //
      newx = -1 * complement2original(orix & 0x00FFFFFF);
  }else{
          //
      newx = orix & 0x00FFFFFF;
  }
  return newx;
}

///*****************************************************************
//
// * \brief 24bit 
//          verified at 2024.7.1
// * \param src
// * \return int24_t
//******************************************************************/
//int app_int24(uint32_t src)
//{
//  static uint32_t res;
//  static int32_t  rev;
//  //+
//  if(0 == (src & (1 << 23)))
//  {
//    rev = src & 0x00FFFFFF;     
//  }
//  //- 
//  else
//  {
//    res = src & 0x007fffff;
//    res -= 1;
//    res = ~res;
//    res &= 0x007fffff;
//    rev = -1 * res;
//  }
//  return rev;  
//}