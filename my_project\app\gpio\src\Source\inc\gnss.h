#ifndef ____GNSS_H____
#define ____GNSS_H____

#undef COMMON_EXT
#ifdef  __GOL_GNSS_C__
    #define COMMON_EXT
#else
    #define COMMON_EXT extern
#endif

#include "string.h"
#include "stdio.h"
#include "stdlib.h"
#include "stdint.h"
#include "data_convert.h"
//#include "frame_analysis.h"
#include "tlhtype.h"



typedef struct
{
    uint8_t index;
    char *string;
} RESOLV_Status;

typedef struct
{
    char sync[10];			//同步字符
    uint8_t CPUIDle;  		//处理器空闲时间的最小百分比，每秒计算 1 次
    char TimeRef[10];		//接收机工作的时间系统
    char TimeQuality[10];	//GPS 时间质量
    uint16_t gpsWn;			//GPS 周数
    uint32_t gpsMs;			//GPS 周内秒，精确到 ms
    uint8_t  leapSec; 		//闰秒
} Log_Header_TypeDef;

typedef struct
{
    Log_Header_TypeDef header;
    uint8_t  utc_year;	//utc年
    uint8_t  utc_month;	//utc月
    uint8_t  utc_day;	//utc日
    uint8_t  utc_hour;	//utc时
    uint8_t  utc_min;	//utc分
    uint8_t  utc_sec;	//utc秒
    uint8_t  rtk_status;//流动站定位状态 0:无效解 1：单点定位解 2：伪距差分 4：固定解 5：浮动解
    uint8_t  heading_status;//主从天线 Heading 解状态 0:无效解           4：固定解 5：浮动解
    uint8_t  num_gps_star;//参与解算 GPS 卫星数
    uint8_t  num_bd_star;//参与解算 BD 卫星数
    uint8_t  num_glo_star;//参与解算 GLO卫星数
    uint8_t  num_gal_star;//Galileo 卫星数
    float    Heading;  //航向角
    float    pitch;		//俯仰角
    float    roll;  //横滚角
    float    vN;	//北向速度
    float    vE;	//东向速度
    float    vZ;	//天顶速度
    float    sigma_vn;
    float    sigma_ve;
    float    sigma_vz;
    double   lat;//纬度
    double   lon;//经度
    double   alt;//高度
    float    sigma_lat;
    float    sigma_lon;
    float    sigma_alt;
    uint32_t GPS_Sec;	//周内秒(毫秒)
} GPS_AGRIC_TypeDef;


COMMON_EXT GPSDataTypeDef hGPSData;						//GPS数据
COMMON_EXT uint8_t gCmdIndex;
COMMON_EXT uint8_t gCmdTypeIndex;
COMMON_EXT uint8_t gnssSynFlg;

int GNSS_Cmd_Kind_Parser(char* pData, uint16_t dataLen);
int GNSS_Cmd_Parser(char* pData);
void GNSS_Buff_Parser(char* pData, uint16_t nCmdIndex);
int gnss_RMC_Buff_Parser(char* pData);
int gnss_GGA_Buff_Parser(char* pData);
int gnss_VTG_Buff_Parser(char* pData);
int gnss_ZDA_Buff_Parser(char* pData);
int gnss_Heading_Buff_Parser(char* pData);
int gnss_BESTPOS_Buff_Parser(char * pData);
int gnss_BESTVEL_Buff_Parser(char * pData);
int gnss_TRA_Buff_Parser(char * pData);
int gnss_AGRIC_Buff_Parser(char * pData);
void gnss_Fetch_Data(void);
void gnss_fill_data(uint8_t* pData, uint16_t dataLen);
uint8_t gnss_isLocation(void);
uint8_t gnss_time_is_valid(void);
float gnss_get_baseline(void);

void gnss_config_movingbase(void);
void gnss_request_GGA(void);
void gnss_request_GSA(void);
void gnss_request_RMC(void);
void gnss_request_HDT(void);
void gnss_request_HEADING(void);
void gnss_request_OTG(void);
void gnss_request_ZDA(void);
void gnss_request_saveconfig(void);
uint8_t gnss_parse(uint8_t* pData, uint16_t dataLen);
//void gnss_fill_rs422(RS422_FRAME_DEF* rs422);
GPS_AGRIC_TypeDef*  gnss_get_algorithm_dataPtr(void);

//INS 设置姿态和标准差
void gnss_set_posture(double pitch, double roll, double azimuth,
                      double pitchOffset, double rollOffset, double azimuthOffset);
//IMU 至从天线杆臂参数配置
void gnss_set_leverArm(double x, double y, double z,
                       double a, double b, double c);

//INS 输出位置偏移配置
void gnss_set_ins_offset(double xoffset, double yoffset, double zoffset);

#endif //____GNSS_H____
