/*
 * Copyright (c) 2022-2028, INS Development Team
 *
 *
 * Change Logs:
 * Date           Author       Notes
 * 2023-06-04     Bill     	   the first version
 *
 */
#include <stdio.h>
#include "board.h"
#include "appmain.h"
#include "hpm_sysctl_drv.h"
#include "hpm_gptmr_drv.h"
#include "hpm_debug_console.h"

#ifndef __GDTYPEDEFINE_H__
#define __GDTYPEDEFINE_H__
//#include "pcsimulator.h"

#pragma pack(1)
//#define c_txmsgdatasize			8
//typedef struct _txmsgserialport {
//	unsigned char head0;
//	unsigned char head1;
//	short len;				//sizeof(txmsgserialport_t)
//	unsigned char cmd;
//	int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
//	int parameter1;
//	int msglistindex;
//	int msgindex;
//	unsigned char data[c_txmsgdatasize];
//	unsigned char checksum;
//	unsigned char tail;
//} txmsgserialport_t;


//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct gdwrxdatainfo {
    unsigned short  navi_test_info_counter;
    unsigned int	gnssInfo_gpsweek;
    unsigned int 	gnssInfo_gpssecond;
    unsigned char   navi_test_info_pps_en;
    unsigned char   navi_test_info_gps_valid;

    unsigned char   ppsDelay;
    
    float   navi_test_info_gyroX;
    float   navi_test_info_gyroY;
    float   navi_test_info_gyroZ;
    float   navi_test_info_sensor_temp;
    float   navi_test_info_accelX;
    float   navi_test_info_accelY;
    float   navi_test_info_accelZ;
    double  gnssInfo_Lon;
    double  gnssInfo_Lat;
    float   gnssInfo_Altitude;
    float   gnssInfo_ve;
    float   gnssInfo_vn;
    float   gnssInfo_vu;
    float   gnssInfo_Pitch;
    float   gnssInfo_Roll;
    float   gnssInfo_Heading;
    unsigned char   gnssInfo_PositioningState;
    unsigned int    gnssInfo_rtkStatus;
    unsigned char   gnssInfo_StarNum;
    unsigned int    canInfo_counter;
    float   canInfo_data_WheelSpeed_Front_Left;
    float   canInfo_data_WheelSpeed_Front_Right;
    float   canInfo_data_WheelSpeed_Back_Left;
    float   canInfo_data_WheelSpeed_Back_Right;
    float   canInfo_data_WheelSteer;
    float   canInfo_data_OdoPulse_1;
    float   canInfo_data_OdoPulse_2;
    float   canInfo_data_Gear;
    unsigned char Adj_Nav_Standard_flag;
    double  Adj_gnssAtt_from_vehicle2_0;
    double  Adj_gnssAtt_from_vehicle2_1;
    double  Adj_gnssAtt_from_vehicle2_2;
    double  Adj_acc_off_0;
    double  Adj_acc_off_1;
    double  Adj_acc_off_2;
    double  Adj_gyro_off_0;
    double  Adj_gyro_off_1;
    double  Adj_gyro_off_2;

    float   gnss_trackTrue;

    unsigned int    result_Nav_Status;
    unsigned char   result_Nav_Standard_flag;
    unsigned char   result_imuSelect;
    unsigned char   result_memsType;
    unsigned char   result_use_gps_flag;
    unsigned char   result_fusion_source;

    unsigned int	gnssInfo_headingStatus;
    unsigned int    gnssInfo_gpssecond982;
	float fog0;
} gdwrxdata_t;

typedef struct _gdalgrithomresult {
	double	longitude;	//算法结果，经纬高
	double	latitude;	//算法结果，经纬高
	float	altitude;	//算法结果，经纬高
	float	ve;		//算法结果，东向速度
	float	vn;		//算法结果，北向速度
	float	vu;		//算法结果，天向速度
	float	pitch;		//算法结果，俯仰角
	float	roll;		//算法结果，横滚角
	float	heading;	//算法结果，偏航角
} gdalgrithomresult_t;



typedef struct _gdwrxdatainfoTX {
    unsigned short head;
    unsigned short len;         //packet all length
    unsigned short type;        //data type  1: imu     2: cacv     3: pps      4:gnss      5:magnetometer  6:gd watch
    gdwrxdata_t  gdwdata;
    float tt;
    int packet;
    int packetT;
    unsigned short checksum;
} gdwrxdataTX_t;

//以下各项请参考FPGA, "惯导FMC通讯协议 V1.23.docx"文件说明
typedef struct gdwrxdata912info {
	uint16_t      datalength;     // //1 1	
	uint16_t     selftestingcode; // //2 //2
	uint16_t     fpgaversion;     // //3 //3

	uint16_t      watchversion;    //     //4 4
	uint16_t      Xgears;          //5 5
	float         Xflwheelspeed;   //6 6
	float         Xfrwheelspeed;   //7 8
	float         Xblwheelspeed;   //8 A
	float         Xbrwheelspeed;   //9 C
	uint16_t      Xcaninfocounter; //10 E

	//int32_t     fogx;            //11 F
	//int32_t     fogy;            //12 11
	//int32_t     fogz;            //13 13
	float     fogx;                //11 F
	float     fogy;                //12 11
	float     fogz;                //13 13
		
	int16_t     fogtemperaturex;   //14 15 X轴光纤陀螺温度
	int16_t     fogtemperaturey;   //15 16
	int16_t     fogtemperaturez;   //16 17 

	float   accelerometerx;          //17 18 X轴加速度
	float   accelerometery;          //18 1A
	float   accelerometerz;          //19 1C
	int16_t accelerometertemp;       //20 1E 石英加速度计温度

	int16_t     reserve1e;           //21 1F
	int16_t     reserve1f;           //22 20
	int16_t     Reserve20;           //23 21

	int16_t      gnssweek;           //24 22 GNSS周
	uint32_t     millisecondofweek;  //25 23 GNSS秒-G			   
	uint32_t     secondofweek;       //26 25 GNSS秒
	uint32_t     ppsdelay10ns;       //27 27 Times_dy

	int16_t     gpsstarnumber;       //28  29 GNSS搜星数
	int16_t     rtkstatus;           //29  2A
	int16_t     speedstatus;         //30  2B GNSS速度状态
	int16_t     truenorthtrack[3];   //31  2C 真北航迹方向

	float	northvelocity;   	//32 2F 北向速度
	float   eastvelocity;           //33 31 东向速度
	float   upvelocity;             //34 33 天向速度

	//int16_t     positionstatus;      //35 35 位置状态 ---
	//int16_t     directionoflat;      //36 36 纬度方向 ---
        int16_t     GnssStaDirLat;//53 高8位位置状态,低8位纬度方向

	double  latitude;                //37 37 

	//int16_t directionoflon;          //38 4A 经度方向---
        int16_t DirLonHeadingSta; //58 高8位经度方向,低8位航向状态

	double  longitude;               //39 4B
	double  altitude;                //40 4F

	//int16_t     Headingstate;        //41 53 航向状态---

	uint32_t     baselength;         //42 54

	float   roll;                    //43 56
	float   pitch;                   //44 58
	float   yaw;                     //45 5A
	#if 0
	float   ECEF_X;                  //46
	float	ECEF_Y;                  //47
	float	ECEF_Z;                  //48
	float   geometricprecZ;          //49
	float	positionprecZ;           //50
	#else
	int16_t  gears;                  //    5B
	int16_t caninfocounter;          //    5C
	float   flwheelspeed;            //    5D
	float   frwheelspeed;            //    5F
	float   blwheelspeed;            //    61
	float   brwheelspeed;            //    63
	#endif
	float	timeprecisionZ;          //51   65
	float	verticalprecZ;           //52   67
	float	horizontalprecZ;         //53   69
	float	northprecisionZ;         //54   6B
	float	eastprecisionZ;          //55   6D 东向精度因子，Z轴
	float	endheightangleZ;         //56   6F 截至高度角，Z轴


        float StanDeviat_Lat;    //59 纬度标准差
        float StanDeviat_Lon;    //5B 经度标准差
        float StanDeviat_Alt;    //5D 高度标准差
        float StanDeviat_Heading;//5F 航向标准差
        float StanDeviat_Pitch;  //61 俯仰标准差

        int   Sol_Status;        //解状态
        int   Pos_Type;          //位置类型


	int16_t		checksum;        //57   71
        //int16_t	checksumA0;       //58 
	int16_t		frameindex;      //59   72
	
	//The following are the results of the algorithm
	double	Alongitude;	//算法结果，经纬高
	double	Alatitude;	//算法结果，经纬高
	float	Aaltitude;	//算法结果，经纬高
	float	Ave;		//算法结果，东向速度
	float	Avn;		//算法结果，北向速度
	float	Avu;		//算法结果，天向速度
	float	Apitch;		//算法结果，俯仰角
	float	Aroll;		//算法结果，横滚角
	float	Aheading;	//算法结果，偏航角
	int16_t	checksumA;               //58
	//	
} gdwrxdata912_t; 

typedef struct gdwrxdata912info_ex {
	double     r_Gyro[3];                    
	double     Acc[3];                    
}gdwrxdata912_t_ex;

//加计和陀螺中间件，用于坐标轴调整
typedef struct _MiddleWare
{
	float     fogx;                    
	float     fogy;                    
	float     fogz;                    
		
	float   accelerometerx;          
	float   accelerometery;          
	float   accelerometerz;          
}__attribute__((packed)) MiddleWare_t;

typedef struct
{
	unsigned short	head1;			//帧头BB00
	unsigned short	head2;			//帧头DBBD
	unsigned short  dataLen;		//后面数据包长度，不包含帧头、本字节和校验
    gdwrxdata912_t fpgaPreDodata;			//FPGA原始数据包-预处理后数据
	gdwrxdata912_t_ex fpgaPreDodata_ex;
    unsigned int fpgaItrCount;		//FPGA中断计数
    unsigned int fpgaLoopCount;		//FPGA处理循环计数
	unsigned short Status;			//当前处理状态
	unsigned short CheckSum;		//检验
} FpgadataPreDoSend_t;

typedef struct
{
        uint16_t     NUM_CLK;	//2//
        uint32_t     millisecondofweek;  //25 23 GNSS秒-G	
        double     r_Gyro[3];                    
	double     Acc[3];   
} SdDataSave_t;

typedef struct gdwrxdatainfo912TX {	//GD watch data...   .gdw 文件格式数据的输出
    unsigned short head;
    unsigned short len;         //packet all length
    unsigned short type;        //data type  1: imu     2: cacv     3: pps      4:gnss      5:magnetometer  6:gd watch		
    gdwrxdata912_t  gdwdata;
    float tt;
    int packet;
    int packetT;
    unsigned short checksum;
} gdwrxdata912TX_t;

typedef struct								//从FPGA数据预处理后，进入算法的数据
{
	unsigned int fpgaCount;
	unsigned int loopCount;
	unsigned short rtkstatus;		//RTK状态
	float fogX;										//陀螺x
	float fogY;										//陀螺y
	float fogZ;										//陀螺z
	float accX;										//加速度x
	float accY;										//加速度y
	float accZ;										//加速度z
	char Avgtemp;									//平均温度
}fpga2ins_t;


#define	c_msg_ask_scha63xcacv		0x12	//上传村田IMU的校准参数，芯驰方案和Infineon方案使用
#define	c_msg_ask_retransmission	0x26	//表示信息帧为上位机反馈回的丢帧信息
#define	c_msg_ask_driversettings	0x39	//上传给上位机的驱动设置信息

#define GD_DRIVERSDATA_MAXCOUNT     200		//最近发送信息缓冲区大小
#define DRIVERSDATATYPE_GNSS        1		//GNSS信息
#define DRIVERSDATATYPE_IMU         2		//IMU信息
#define DRIVERSDATATYPE_CAN         3		//CAN信息
#define DRIVERSDATATYPE_PPS         4		//PPS信息
#define DRIVERSDATATYPE_GDW         6		//GD方案观测量信息
#define DRIVERSDATATYPE_GDW912      9		//GD INS912方案观测量信息
#define DRIVERSDATATYPE_FPGA912	    10		//INS912 FPGA传给GD的数据

typedef struct _driversdatatosimulator {
    int driversdatatype;    //0: null   1:-gnss   2-imu   	  3-can     	4-pps   	5-      6-gd watch
							//7: driver settings  8-imu data  9-912 watchD
    int driverspacket;
    union {
        //rxonecaninfo_t  can;
        //imuorgdatasendtopc_t    imu;
        //gnessrxdataTX_t gnss;
        //gnessppsTX_t    pps;
        gdwrxdata912TX_t   gdw;
    } data;
} driversdatatosimulator_t;

//驱动数据缓冲区
typedef struct _driversdatatosimulatorlist {
    int st;
    int size;
    driversdatatosimulator_t    driversdata[GD_DRIVERSDATA_MAXCOUNT];
} driversdatatosimulatorlist_t;


#define c_txmsgdatasize			8
typedef struct _txmsgserialport {
	unsigned char head0;
	unsigned char head1;
	short len;				//sizeof(txmsgserialport_t)
	unsigned char cmd;
	int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
	int parameter1;
	int msglistindex;
	int msgindex;
	unsigned char data[c_txmsgdatasize];	
	unsigned char checksum;
	unsigned char tail;
} txmsgserialport_t;


typedef struct _driversettings {
	char projectname[16];	//项目名称
	char mcutype;			//mcu主控型号
	char datatype;			//数据类型
	int imuframe;		//imu数据大小
	int gnssframe;		//gnss数据大小
	int canframe;		//can数据大小
	int ppsframe;		//pps数据大小
	int gdwframe;		//GD方案观测量数据大小
	int magnetframe;	//磁力计数据大小
	int pressureframe;	//气压计数据大小
	int opticalgryoframe;		//单个光纤数据大小
	int imu2600hzframe;			//imu 2600Hz数据大小
	int ins912fpgaframe;		//INS912 FPGA数据大小
	int turntableaccgyroframe;	//转台上测试加计和陀螺数据大小
	int gdwframe912;			//INS912观测量数据大小
	int reserved[16];			//保留
} driversettings_t;

typedef struct _driversettingstopc {
	unsigned short head;		//协议头 0xaa55
	unsigned short len;         //packet all length
	unsigned short type;    	//data type  1: imu     2: cacv		6: GD watch		7: driversettings
	driversettings_t settings;	//驱动设置
	unsigned short checksum;	//数据校验值
} driversettingstopc_t;


//---Track-AllFpgaDatas-----------------------------------------------------------------------------------
#if defined(CMPL_CODE_EDWOY)
//2024.6.28
//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct _ins370info {
	uint16_t		  datalength;       		 //1 
	uint16_t     selftestingcode;         //2 
	uint16_t     fpgaversion;             //3 
	uint16_t		  watchversion;            //4 
	uint16_t     Xgears;                   //5 
	float   Xflwheelspeed;            //6 
	float   Xfrwheelspeed;            //7 
	float   Xblwheelspeed;            //8 
	float   Xbrwheelspeed;            //9 
	uint16_t     Xcaninfocounter;          //10
  
	float     fogx;                    //11
	float     fogy;                    //12
	float     fogz;                    //13

	int16_t     fogtemperaturex;         //14
	int16_t     fogtemperaturey;         //15
	int16_t     fogtemperaturez;         //26
	float   accelerometerx;          //17
	float   accelerometery;          //18
	float   accelerometerz;          //19
	int16_t     accelerometertemp;       //20
	uint16_t     reserve1e;               //21
	uint16_t     reserve1f;               //22
	uint16_t     Reserve20;               //23
	uint16_t     gnssweek;                //24
	uint32_t     millisecondofweek;       //25
					   
	uint32_t     secondofweek;            //26
	uint32_t     ppsdelay10ns;            //27
	uint16_t     gpsstarnumber;           //28
	uint16_t     rtkstatus;               //29
	uint16_t     speedstatus;             //30
	uint16_t     truenorthtrack[3];       //31
	float	  northvelocity;   		 //32
	float   eastvelocity;            //33
	float   upvelocity;              //34
	uint16_t     positionstatus;          //35
	uint16_t     directionoflat;          //36
	double  latitude;                //37
	uint16_t     directionoflon;          //38
	double  longitude;               //39
	double  altitude;                //40
	uint16_t     Headingstate;            //41
	uint32_t     baselength;              //42
	float   roll;                    //43
	float   pitch;                   //44
	float   yaw;                     //45
#if 0
	float   ECEF_X;                  //46
	float	ECEF_Y;                  //47
	float	ECEF_Z;                  //48
	float   geometricprecZ;          //49
	float	positionprecZ;           //50
#else
	uint16_t     gears;                   // 
	uint16_t     caninfocounter;          //
	float   flwheelspeed;            // 
	float   frwheelspeed;            // 
	float   blwheelspeed;            // 
	float   brwheelspeed;            // 
#endif
	float	timeprecisionZ;          //51
	float	verticalprecZ;           //52
	float	horizontalprecZ;         //53
	float	northprecisionZ;         //54
	float	eastprecisionZ;          //55
	float	endheightangleZ;         //56
	uint16_t		checksum;                //57
	//uint16_t		checksumA;               //58
	uint16_t		frameindex;              //59
	
	//The following are the results of the algorithm
	double	Alongitude;	//算法结果，经纬高
	double	Alatitude;	//算法结果，经纬高
	float	Aaltitude;	//算法结果，经纬高
	float	Ave;		//算法结果，东向速度
	float	Avn;		//算法结果，北向速度
	float	Avu;		//算法结果，天向速度
	float	Apitch;		//算法结果，俯仰角
	float	Aroll;		//算法结果，横滚角
	float	Aheading;	//算法结果，偏航角
	uint16_t		checksumA;               //58
} sttIns370Trk_t;  //size = 246bytes

#endif 
//---End Track-AllFpgaDatas-----------------------------------------------------------------------------------*/


#pragma pack()

extern  int gdriverspacket;
extern  int ggdworgdata_packet;
extern  gdwrxdata912TX_t	gtmpgdrx;
extern	gdalgrithomresult_t	galgrithomresultTx;
extern  driversdatatosimulatorlist_t	gdriverdatalist;
extern  driversettingstopc_t	gdriversettings;
extern	float gfog0;
extern	MiddleWare_t StrMiddleWare;//加计和陀螺中间件，用于坐标轴调整



extern	int mcusendtopcdriversdata(int cmd, int listindex, int driverindex);
extern	int initializationdriversettings(void);


#endif  //__GDTYPEDEFINE_H__
