/*!
    \file  nav_uwb.h
    \brief Navigation UWB interface header file
*/

#ifndef __NAV_UWB_H
#define __NAV_UWB_H

#include <stdint.h>
#include <stdbool.h>

typedef enum {
    NAV_UWB_STATUS_OK = 0,
    NAV_UWB_STATUS_ERROR
} nav_uwb_status_t;

typedef struct {
    float range;
    float position[3];
    uint32_t timestamp;
    bool valid;
} nav_uwb_data_t;

nav_uwb_status_t nav_uwb_init(void);
nav_uwb_status_t nav_uwb_update(const nav_uwb_data_t *uwb_data);

#endif /* __NAV_UWB_H */
