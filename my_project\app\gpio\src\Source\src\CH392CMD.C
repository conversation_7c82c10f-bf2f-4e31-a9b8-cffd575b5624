/********************************** (C) COPYRIGHT *******************************
* File Name          : CH392CMD.C
* Author             : WCH
* Version            : V1.0
* Date               : 2020/02/19
* Description        : CH392                     
*******************************************************************************/
#include "CH392INC.H"
#include "ch392cmd.h"

#define	delay_ms	mDelaymS
#define	delay_us	mDelayuS

/********************************************************************************
* Function Name  : CH392CMDReset
* Description    : CH392
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDReset(void)
{
    xWriteCH392Cmd(CMD00_RESET_ALL);  
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392CMDSleep
* Description    : CH392
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSleep(void)
{
    xWriteCH392Cmd(CMD00_ENTER_SLEEP);  
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDSleep
* Description    : 1
                   
* Input          : None
* Output         : None
* Return         : 1
*******************************************************************************/
u8 CH392CMDGetVer(void)
{
    u8 i;
	
    xWriteCH392Cmd(CMD01_GET_IC_VER);
    i = xReadCH392Data();
	
//    xWriteCH392Cmd(0x12);
//    xWriteCH392Cmd(0x34);
//    xWriteCH392Cmd(0x28);
//    i = xReadCH392Data();
	
    xEndCH392Cmd();
    return i;
}

/********************************************************************************
* Function Name  : CH392CMDCheckExist
* Description    : 
* Input          : testdata 1
* Output         : None
* Return         : OK testdata
*******************************************************************************/
u8 CH392CMDCheckExist(u8 testdata)
{
    u8 i;
	
    xWriteCH392Cmd(CMD11_CHECK_EXIST);
    xWriteCH392Data(testdata);
    i = xReadCH392Data(); 	
    xEndCH392Cmd();
    return i;
}

/********************************************************************************
* Function Name  : CH392CMDSetPHY
* Description    : PHYCH392 PHY100/10M CH392
                    
* Input          : phystat PHY /
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetPHY(u8 phystat)
{
    xWriteCH392Cmd(CMD10_SET_PHY);
    xWriteCH392Data(phystat);
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392CMDGetPHYStatus
* Description    : PHY
* Input          : None
* Output         : None
* Return         : CH392PHYPHY/
*******************************************************************************/
u8 CH392CMDGetPHYStatus(void)
{
    u8 i;

    xWriteCH392Cmd(CMD01_GET_PHY_STATUS);
    i = xReadCH392Data();	
    xEndCH392Cmd();
    return i;
}

/*******************************************************************************
* Function Name  : CH392CMDGetGlobIntStatus
* Description    : CH3920x43
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392CMDGetGlobIntStatus(void)
{
    u8 init_status;

    xWriteCH392Cmd(CMD01_GET_GLOB_INT_STATUS);
    init_status = xReadCH392Data();	  
    xEndCH392Cmd();
    return  init_status;
}

/********************************************************************************
* Function Name  : CH392CMDInitCH392
* Description    : CH392
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392CMDInitCH392(void)
{
    u8 i = 0;
    u8 s = 0;

    xWriteCH392Cmd(CMD0W_INIT_CH392);	  
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(10);                                                /* 2MS*/
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* ,500MS */
    }
    return s;
}

/********************************************************************************
* Function Name  : CH392CMDSetUartBaudRate
* Description    : CH392
* Input          : baudrate 
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetUartBaudRate(UINT32 baudrate)
{

    xWriteCH392Cmd(CMD31_SET_BAUDRATE);
    xWriteCH392Data((u8)baudrate);
    xWriteCH392Data((u8)((u16)baudrate >> 8));
	  xWriteCH392Data((u8)(baudrate >> 16)); 
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392GetCmdStatus
* Description    : 
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392GetCmdStatus(void)
{
    u8 i;

    xWriteCH392Cmd(CMD01_GET_CMD_STATUS);
    i = xReadCH392Data();	  
    xEndCH392Cmd();
    return i;
}

/********************************************************************************
* Function Name  : CH392CMDSetIPAddr
* Description    : CH392IP
* Input          : ipaddr IP
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetIPAddr(u8 *ipaddr)
{
    u8 i;

    xWriteCH392Cmd(CMD40_SET_IP_ADDR);
    for(i = 0; i < 4;i++)xWriteCH392Data(*ipaddr++);  
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDSetGWIPAddr
* Description    : CH392IP
* Input          : ipaddr IP
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetGWIPAddr(u8 *gwipaddr)
{
    u8 i;

    xWriteCH392Cmd(CMD40_SET_GWIP_ADDR);
    for(i = 0; i < 4;i++)xWriteCH392Data(*gwipaddr++);	  
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDSetMASKAddr
* Description    : CH392255.255.255.0
* Input          : maskaddr 
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetMASKAddr(u8 *maskaddr)
{
    u8 i;

    xWriteCH392Cmd(CMD40_SET_MASK_ADDR);
    for(i = 0; i < 4;i++)xWriteCH392Data(*maskaddr++);  
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDSetMACAddr
* Description    : CH392MAC
* Input          : amcaddr MAC
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetMACAddr(u8 *amcaddr)
{
    u8 i;

    xWriteCH392Cmd(CMD60_SET_MAC_ADDR);
    for(i = 0; i < 6;i++)xWriteCH392Data(*amcaddr++);	 
    xEndCH392Cmd();
    delay_ms(100); 
}

/********************************************************************************
* Function Name  : CH392CMDGetMACAddr
* Description    : CH392MAC
* Input          : amcaddr MAC
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDGetMACAddr(u8 *amcaddr)
{
    u8 i;

    xWriteCH392Cmd(CMD06_GET_MAC_ADDR);
    for(i = 0; i < 6;i++)*amcaddr++ = xReadCH392Data();	  
    xEndCH392Cmd();
 }

/*******************************************************************************
* Function Name  : CH392CMDSetMACFilt
* Description    : MAC
* Input          : filtype  MAC
                   table0 Hash0
                   table1 Hash1
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetMACFilt(u8 filtype,UINT32 table0,UINT32 table1)
{
    xWriteCH392Cmd(CMD90_SET_MAC_FILT);
    xWriteCH392Data(filtype);
    xWriteCH392Data((u8)table0);
    xWriteCH392Data((u8)((u16)table0 >> 8));
    xWriteCH392Data((u8)(table0 >> 16));
    xWriteCH392Data((u8)(table0 >> 24));

    xWriteCH392Data((u8)table1);
    xWriteCH392Data((u8)((u16)table1 >> 8));
    xWriteCH392Data((u8)(table1 >> 16));
    xWriteCH392Data((u8)(table1 >> 24));	  
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDGetUnreachIPPT
* Description    :  (IP,Port,Protocol Type)
* Input          : list 
                        1 (CH392INC.H)
                        2IP
                        3-4
                        4-8IP
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDGetUnreachIPPT(u8 *list)
{
    u8 i;

    xWriteCH392Cmd(CMD08_GET_UNREACH_IPPORT);
    for(i = 0; i < 8; i++)
    {
        *list++ = xReadCH392Data();
    }   		
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDGetRemoteIPP
* Description    : IPTCP Server
* Input          : sockindex Socket
                   list IP
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDGetRemoteIPP(u8 sockindex,u8 *list)
{
    u8 i;

    xWriteCH392Cmd(CMD06_GET_REMOT_IPP_SN);
    xWriteCH392Data(sockindex);
    for(i = 0; i < 6; i++)
    {
        *list++ = xReadCH392Data();
    } 
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392SetSocketDesIP
* Description    : socket nIP
* Input          : sockindex Socket
                   ipaddr IP
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketDesIP(u8 sockindex,u8 *ipaddr)
{
    xWriteCH392Cmd(CMD50_SET_IP_ADDR_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(*ipaddr++);
    xWriteCH392Data(*ipaddr++);
    xWriteCH392Data(*ipaddr++);
    xWriteCH392Data(*ipaddr++);
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392SetSocketProtType
* Description    : socket 
* Input          : sockindex Socket
                   prottype  socket(CH392INC.H)
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketProtType(u8 sockindex,u8 prottype)
{
    xWriteCH392Cmd(CMD20_SET_PROTO_TYPE_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(prottype);
    xEndCH392Cmd();
}

/*******************************************************************************

* Function Name  : CH392SetSocketDesPort
* Description    : socket n
* Input          : sockindex Socket
                   desprot 2
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketDesPort(u8 sockindex,u16 desprot)
{
    xWriteCH392Cmd(CMD30_SET_DES_PORT_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data((u8)desprot);
    xWriteCH392Data((u8)(desprot >> 8));
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392SetSocketSourPort
* Description    : socket n
* Input          : sockindex Socket
                   desprot 2
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketSourPort(u8 sockindex,u16 surprot)
{
    xWriteCH392Cmd(CMD30_SET_SOUR_PORT_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data((u8)surprot);
    xWriteCH392Data((u8)(surprot>>8));
    xEndCH392Cmd();
}

/******************************************************************************
* Function Name  : CH392SetSocketIPRAWProto
* Description    : IPsocket IP
* Input          : sockindex Socket
                   prototype IPRAW1
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketIPRAWProto(u8 sockindex,u8 prototype)
{
    xWriteCH392Cmd(CMD20_SET_IPRAW_PRO_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(prototype);
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392EnablePing
* Description    : / PING
* Input          : enable : 1  PING
                          0  PING
* Output         : None
* Return         : None
*******************************************************************************/
void CH392EnablePing(u8 enable)
{
    xWriteCH392Cmd(CMD01_PING_ENABLE);
    xWriteCH392Data(enable);
    xEndCH392Cmd();
}


/********************************************************************************
* Function Name  : CH392SendData
* Description    : 
* Input          : sockindex Socket
                   databuf  
                   len   
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SendData(u8 sockindex,u8 *databuf,u16 len)
{
    u16 i;

    xWriteCH392Cmd(CMD30_WRITE_SEND_BUF_SN);
    xWriteCH392Data((u8)sockindex);
    xWriteCH392Data((u8)len);
    xWriteCH392Data((u8)(len>>8));  
    for(i = 0; i < len; i++)
    {
        xWriteCH392Data(*databuf++);
    }
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392GetRecvLength
* Description    : 
* Input          : sockindex Socket
* Output         : None
* Return         : 
*******************************************************************************/
u16 CH392GetRecvLength(u8 sockindex)
{
    u16 i;

    xWriteCH392Cmd(CMD12_GET_RECV_LEN_SN);
    xWriteCH392Data((u8)sockindex);
    i = xReadCH392Data();
    i = (u16)(xReadCH392Data()<<8) + i;
    xEndCH392Cmd();
    return i;
}

/*******************************************************************************
* Function Name  : CH392ClearRecvBuf
* Description    : 
* Input          : sockindex Socket
* Output         : None
* Return         : None
*******************************************************************************/
void CH392ClearRecvBuf(u8 sockindex)
{
    xWriteCH392Cmd(CMD10_CLEAR_RECV_BUF_SN);
    xWriteCH392Data((u8)sockindex);
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392GetRecvLength
* Description    : 
* Input          : sockindex Socket
                   len   
                   pbuf  
* Output         : None
* Return         : None
*******************************************************************************/
void CH392GetRecvData(u8 sockindex,u16 len,unsigned char *pbuf)
{
    u16 i;
    if(!len)return;
    xWriteCH392Cmd(CMD30_READ_RECV_BUF_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data((u8)len);
    xWriteCH392Data((u8)(len>>8));
    delay_us(1);
    for(i = 0; i < len; i++)
    {
       *pbuf = xReadCH392Data();
       pbuf++;
    }   
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDSetRetryCount
* Description    : 
* Input          : count 20
* Output         : None
* Return         : None
********************************************************************************/
void CH392CMDSetRetryCount(u8 count)
{
    xWriteCH392Cmd(CMD10_SET_RETRAN_COUNT);
    xWriteCH392Data(count);
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDSetRetryPeriod
* Description    : 
* Input          : period 1000ms
* Output         : None
* Return         : None
*******************************************************************************/
void CH392CMDSetRetryPeriod(u16 period)
{
    xWriteCH392Cmd(CMD10_SET_RETRAN_COUNT);
    xWriteCH392Data((u8)period);
    xWriteCH392Data((u8)(period>>8));
    xEndCH392Cmd();
}

/********************************************************************************
* Function Name  : CH392CMDGetSocketStatus
* Description    : socket
* Input          : None
* Output         : socket n1socket 
                   2TCP
* Return         : None
*******************************************************************************/
void CH392CMDGetSocketStatus(u8 sockindex,u8 *status)
{
    xWriteCH392Cmd(CMD12_GET_SOCKET_STATUS_SN);
    xWriteCH392Data(sockindex);
    *status++ = xReadCH392Data();
    *status++ = xReadCH392Data();
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392OpenSocket
* Description    : socket
* Input          : sockindex Socket
* Output         : None
* Return         : 
*******************************************************************************/
u8  CH392OpenSocket(u8 sockindex)
{
    u8 i = 0;
    u8 s = 0;
    xWriteCH392Cmd(CMD1W_OPEN_SOCKET_SN);
    xWriteCH392Data(sockindex);
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(5);                                                 /* 2MS*/
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* */
    }
    return s;
}

/*******************************************************************************
* Function Name  : CH392CloseSocket
* Description    : socket
* Input          : sockindex Socket
* Output         : None
* Return         : 
*******************************************************************************/
u8  CH392CloseSocket(u8 sockindex)
{
    u8 i = 0;
    u8 s = 0;
    xWriteCH392Cmd(CMD1W_CLOSE_SOCKET_SN);
    xWriteCH392Data(sockindex);
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(5);                                                 /* 2MS*/
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* */
    }
    return s;
}

/********************************************************************************
* Function Name  : CH392TCPConnect
* Description    : TCPTCP
* Input          : sockindex Socket
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392TCPConnect(u8 sockindex)
{
    u8 i = 0;
    u8 s = 0;
    xWriteCH392Cmd(CMD1W_TCP_CONNECT_SN);
    xWriteCH392Data(sockindex);
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(5);                                                 /* 2MS*/
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* */
    }
    return s;
}

/******************************************************************************
* Function Name  : CH392TCPListen
* Description    : TCPTCP
* Input          : sockindex Socket
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392TCPListen(u8 sockindex)
{
    u8 i = 0;
    u8 s = 0;
    xWriteCH392Cmd(CMD1W_TCP_LISTEN_SN);
    xWriteCH392Data(sockindex);
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(5);                                                 /* 2MS*/
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* */
    }
    return s;
}

/********************************************************************************
* Function Name  : CH392TCPDisconnect
* Description    : TCPTCP
* Input          : sockindex Socket
* Output         : None
* Return         : None
*******************************************************************************/
u8 CH392TCPDisconnect(u8 sockindex)
{
    u8 i = 0;
    u8 s = 0;
    xWriteCH392Cmd(CMD1W_TCP_DISNCONNECT_SN);
    xWriteCH392Data(sockindex);
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(5);                                                 /* 2MS*/
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* */
    }
    return s;
}

/*******************************************************************************
* Function Name  : CH392GetSocketInt
* Description    : socket n
* Input          : sockindex   socket
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392GetSocketInt(u8 sockindex)
{
    u8 intstatus;
    xWriteCH392Cmd(CMD11_GET_INT_STATUS_SN);
    xWriteCH392Data(sockindex);
    delay_us(2);
    intstatus = xReadCH392Data();
    xEndCH392Cmd();
    return intstatus;
}

/*******************************************************************************
* Function Name  : CH392CRCRet6Bit
* Description    : CRC6
* Input          : mac_addr   MAC
* Output         : None
* Return         : CRC326
*******************************************************************************/
u8 CH392CRCRet6Bit(u8 *mac_addr)
{
    INT32 perByte;
    INT32 perBit;
    const UINT32 poly = 0x04C11DB7;
    UINT32 crc_value = 0xFFFFFFFF;
    u8 c;
    for ( perByte = 0; perByte < 6; perByte ++ ) 
    {
        c = *(mac_addr++);
        for ( perBit = 0; perBit < 8; perBit++ ) 
        {
            crc_value = (crc_value<<1)^((((crc_value>>31)^c)&0x01)?poly:0);
            c >>= 1;
        }
    }
    crc_value=crc_value>>26;                                      
    return ((u8)crc_value);
}

/******************************************************************************
* Function Name  : CH392DHCPEnable
* Description    : /DHCP
* Input          : flag   1:DHCP;0DHCP
* Output         : None
* Return         : 
*******************************************************************************/
u8  CH392DHCPEnable(u8 flag)
{
    u8 i = 0;
    u8 s;
    xWriteCH392Cmd(CMD10_DHCP_ENABLE);
    xWriteCH392Data(flag);
    xEndCH392Cmd();
    while(1)
    {
        delay_ms(20);
        s = CH392GetCmdStatus();                                     /* */
        if(s !=CH392_ERR_BUSY)break;                                 /* CH392*/
        if(i++ > 200)return CH392_ERR_UNKNOW;                        /* */
    }
    return s;
}

/******************************************************************************
* Function Name  : CH392GetDHCPStatus
* Description    : DHCP
* Input          : None
* Output         : None
* Return         : DHCP0
*******************************************************************************/
u8 CH392GetDHCPStatus(void)
{
    u8 status;
    xWriteCH392Cmd(CMD01_GET_DHCP_STATUS);
    status = xReadCH392Data();
    xEndCH392Cmd();
    return status;
}

/*******************************************************************************
* Function Name  : CH392GetIPInf
* Description    : IP
* Input          : None
* Output         : 12IP,
* Return         : None
*******************************************************************************/
void CH392GetIPInf(u8 *addr)
{
    u8 i;
    xWriteCH392Cmd(CMD014_GET_IP_INF);
    for(i = 0; i < 20; i++)
    {
     *addr++ = xReadCH392Data();
    }
		
    xEndCH392Cmd();
}

/*******************************************************************************
* Function Name  : CH392WriteGPIOAddr
* Description    : GPIO
* Input          : regadd   
*                regval   
* Output         : None
* Return         : None
*******************************************************************************/
void CH392WriteGPIOAddr(u8 regadd,u8 regval)
{
    xWriteCH392Cmd(CMD20_WRITE_GPIO_REG);
    xWriteCH392Data(regadd);
    xWriteCH392Data(regval);
}

/*******************************************************************************
* Function Name  : CH392ReadGPIOAddr
* Description    : GPIO
* Input          : regadd   
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392ReadGPIOAddr(u8 regadd)
{
    u8 i;
    xWriteCH392Cmd(CMD10_READ_GPIO_REG);
    xWriteCH392Data(regadd);
    delay_ms(1);
    i = xReadCH392Data();
    return i;
}

/*******************************************************************************
* Function Name  : CH392EEPROMErase
* Description    : EEPROM
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392EEPROMErase(void)
{
    u8 i;    
    xWriteCH392Cmd(CMD00_EEPROM_ERASE);
    while(1)
    {
        delay_ms(20);
       i = CH392GetCmdStatus();
       if(i == CH392_ERR_BUSY)continue;
       break;
    }
    return i;
}

/*******************************************************************************
* Function Name  : CH392EEPROMWrite
* Description    : EEPROM
* Input          : eepaddr  EEPROM
*                buf      
*                len      
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392EEPROMWrite(u16 eepaddr,u8 *buf,u8 len)
{
    u8 i;
    xWriteCH392Cmd(CMD30_EEPROM_WRITE);
    xWriteCH392Data((u8)(eepaddr));
    xWriteCH392Data((u8)(eepaddr >> 8));
    xWriteCH392Data(len);  
    while(len--)xWriteCH392Data(*buf++);
    while(1)
    {
       delay_ms(20);
       i = CH392GetCmdStatus();
       if(i == CH392_ERR_BUSY)continue;
       break;
    }
    return i;
}
  
/*******************************************************************************
* Function Name  : CH392EEPROMRead
* Description    : EEPROM
* Input          : eepaddr  EEPROM
*                buf      
*                len      
* Output         : None
* Return         : None
*******************************************************************************/
void CH392EEPROMRead(u16 eepaddr,u8 *buf,u8 len)
{
    xWriteCH392Cmd(CMD30_EEPROM_READ);
    xWriteCH392Data((u8)(eepaddr));
    xWriteCH392Data((u8)(eepaddr >> 8));
    xWriteCH392Data(len);  
    delay_ms(1);
    while(len--)*buf++ = xReadCH392Data();
}

/*******************************************************************************
* Function Name  : CH392SetTCPMss
* Description    : TCP MSS
* Input          : tcpmss 
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetTCPMss(u16 tcpmss)
{
    xWriteCH392Cmd(CMD20_SET_TCP_MSS);
    xWriteCH392Data((u8)(tcpmss));
    xWriteCH392Data((u8)(tcpmss >> 8));
}

/*******************************************************************************
* Function Name  : CH392SetSocketRecvBuf
* Description    : Socket
* Input          : sockindex  socket
                 startblk   
                 blknum      512
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketRecvBuf(u8 sockindex,u8 startblk,u8 blknum)
{
    xWriteCH392Cmd(CMD30_SET_RECV_BUF);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(startblk);
    xWriteCH392Data(blknum);
}

/*******************************************************************************
* Function Name  : CH392SetSocketSendBuf
* Description    : Socket
* Input          : sockindex  socket
                 startblk   
                 blknum     
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetSocketSendBuf(u8 sockindex,u8 startblk,u8 blknum)
{
    xWriteCH392Cmd(CMD30_SET_SEND_BUF);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(startblk);
    xWriteCH392Data(blknum);
}

/*******************************************************************************
* Function Name  : CH392UDPSendTo
* Description    : UDPIP
* Input          : buf     : 
                   len     : 
				           ip      : IP
				           port    : 
				   sockeid : socket
* Output         : None
* Return         : None
*******************************************************************************/
void CH392UDPSendTo(u8 *buf,UINT32 len,u8 *ip,u16 port,u8 sockindex)
{
    CH392SetSocketDesIP(sockindex,ip);                            /* socket 0IP */         
    CH392SetSocketDesPort(sockindex,port);
    CH392SendData(sockindex,buf,len);    
}

/*******************************************************************************
* Function Name  : CH392SetStartPara
* Description    : CH392
* Input          : mdata
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetStartPara(UINT32 mdata)
{
    xWriteCH392Cmd(CMD40_SET_FUN_PARA);
    xWriteCH392Data((u8)mdata);
    xWriteCH392Data((u8)((u16)mdata>>8));
    xWriteCH392Data((u8)(mdata >> 16));
    xWriteCH392Data((u8)(mdata >> 24));
}

/*******************************************************************************
* Function Name  : CH392CMDGetGlobIntStatus
* Description    : CH392,0x44
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
u8 CH392CMDGetGlobIntStatus_ALL(void)
{
		u8 init_status;
		xWriteCH392Cmd(CMD01_GET_GLOB_INT_STATUS);
		delay_us(2);
		init_status = xReadCH392Data();
		xEndCH392Cmd();
		return 	init_status;
}

/*******************************************************************************
* Function Name  : CH392SetKeepLive
* Description    : keepalive
* Input          : sockindex Socket
*                  cmd 0 1
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetKeepLive(u8 sockindex,u8 cmd)
{
    xWriteCH392Cmd(CMD20_SET_KEEP_LIVE_SN);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(cmd);
}

/*******************************************************************************
* Function Name  : CH392KeepLiveCNT
* Description    : keepalive
* Input          : cnt 
* Output         : None
* Return         : None
*******************************************************************************/
void CH392KeepLiveCNT(u8 cnt)
{
    xWriteCH392Cmd(CMD10_SET_KEEP_LIVE_CNT);
    xWriteCH392Data(cnt);
}

/*******************************************************************************
* Function Name  : CH392KeepLiveIDLE
* Description    : KEEPLIVE
* Input          : idle ms
* Output         : None
* Return         : None
*******************************************************************************/
void CH392KeepLiveIDLE(UINT32 idle)
{
    xWriteCH392Cmd(CMD40_SET_KEEP_LIVE_IDLE);
    xWriteCH392Data((u8)idle);
    xWriteCH392Data((u8)((u16)idle>>8));
    xWriteCH392Data((u8)(idle >> 16));
    xWriteCH392Data((u8)(idle >> 24));
}

/*******************************************************************************
* Function Name  : CH392KeepLiveINTVL
* Description    : KeepLive 
* Input          : intvl ms
* Output         : None
* Return         : None
*******************************************************************************/
void CH392KeepLiveINTVL(UINT32 intvl)
{
    xWriteCH392Cmd(CMD40_SET_KEEP_LIVE_INTVL);
    xWriteCH392Data((u8)intvl);
    xWriteCH392Data((u8)((u16)intvl>>8));
    xWriteCH392Data((u8)(intvl >> 16));
    xWriteCH392Data((u8)(intvl >> 24));
}

/*******************************************************************************
* Function Name  : CH392SetTTLNum
* Description    : TTL
* Input          : sockindex Socket
*                  TTLnum:TTL
* Output         : None
* Return         : None
*******************************************************************************/
void CH392SetTTLNum(u8 sockindex,u8 TTLnum)
{
    xWriteCH392Cmd(CMD20_SET_TTL);
    xWriteCH392Data(sockindex);
    xWriteCH392Data(TTLnum);
}




