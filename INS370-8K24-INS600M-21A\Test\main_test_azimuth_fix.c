/***********************************************************************************
航向角掉电保存修复测试主程序
All rights reserved I-NAV 2023 2033
***********************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include "test_azimuth_fix.h"
#include "nav_includes.h"
#include "SetParaBao.h"

// 全局变量定义
_NAV_Data_Full_t NAV_Data_Full;
SetParaTypeDef stSetPara;

int main(void)
{
    printf("航向角掉电保存修复功能测试程序\n");
    printf("=====================================\n\n");
    
    // 初始化全局变量
    memset(&NAV_Data_Full, 0, sizeof(_NAV_Data_Full_t));
    memset(&stSetPara, 0, sizeof(SetParaTypeDef));
    
    // 运行测试
    test_azimuth_save_fix();
    
    printf("\n按任意键退出...\n");
    getchar();
    
    return 0;
}
