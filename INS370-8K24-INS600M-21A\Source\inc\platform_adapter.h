/*!
    \file  platform_adapter.h
    \brief Platform adaptation layer for migrating HPM6750 code to GD32F4xx
*/

#ifndef __PLATFORM_ADAPTER_H
#define __PLATFORM_ADAPTER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Include GD32F4xx standard library instead of HPM */
#include "gd32f4xx.h"
#include "systick.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* Platform-specific type definitions */
typedef uint8_t  hpm_stat_t;
typedef uint32_t clock_name_t;

/* HPM to GD32 GPIO mapping */
#define HPM_GPIO0           GPIOA
#define HPM_GPIOM           GPIOA
#define GPIO_OE_GPIOB       GPIOB
#define GPIO_OE_GPIOY       GPIOY
#define GPIO_DO_GPIOY       GPIOY
#define GPIO_IE_GPIOB       GPIOB
#define GPIO_DI_GPIOB       GPIOB

/* HPM to GD32 IOC mapping */
#define HPM_IOC             ((void*)0)
#define IOC_PAD_PB09        GPIO_PIN_9
#define IOC_PB09_FUNC_CTL_GPIO_B_09  0
#define IOC_PAD_PAD_CTL_PE_SET(x)    0
#define IOC_PAD_PAD_CTL_PS_SET(x)    0

/* HPM to GD32 interrupt mapping */
#define BOARD_APP_GPIO_IRQ  EXTI5_9_IRQn
#define intc_m_enable_irq_with_priority(irq, priority) \
    do { \
        nvic_irq_enable(irq, priority, 0); \
    } while(0)

/* HPM to GD32 timer mapping */
#define MCHTMR_CLK_NAME     clock_mchtmr0
#define clock_mchtmr0       0

/* HPM to GD32 SDMMC mapping */
typedef struct {
    uint32_t dummy;
} sd_card_t;

typedef enum {
    status_success = 0,
    status_fail = 1
} hpm_status_t;

/* HPM to GD32 GPIO function mappings */
typedef enum {
    gpiom_soc_gpio0 = 0
} gpiom_assign_t;

typedef enum {
    gpio_interrupt_trigger_edge_rising = 0,
    gpio_interrupt_trigger_edge_falling = 1
} gpio_interrupt_trigger_t;

/* Function mappings from HPM to GD32 */
#define gpiom_set_pin_controller(gpiom, assign, pin, controller) \
    do { /* GD32 equivalent implementation */ } while(0)

#define gpio_set_pin_input(gpio, oe, pin) \
    gpio_mode_set(gpio, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, GPIO_PIN_##pin)

#define gpio_set_pin_output(gpio, oe, pin) \
    gpio_mode_set(gpio, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_##pin)

#define gpio_write_pin(gpio, do_reg, pin, value) \
    do { \
        if(value) gpio_bit_set(gpio, GPIO_PIN_##pin); \
        else gpio_bit_reset(gpio, GPIO_PIN_##pin); \
    } while(0)

#define gpio_toggle_pin(gpio, port, pin) \
    gpio_bit_toggle(gpio, pin)

#define gpio_enable_pin_interrupt(gpio, ie, pin) \
    do { \
        exti_init(EXTI_##pin, EXTI_INTERRUPT, EXTI_TRIG_RISING); \
        exti_interrupt_enable(EXTI_##pin); \
    } while(0)

#define gpio_config_pin_interrupt(gpio, di, pin, trigger) \
    do { \
        if(trigger == gpio_interrupt_trigger_edge_rising) \
            exti_init(EXTI_##pin, EXTI_INTERRUPT, EXTI_TRIG_RISING); \
        else \
            exti_init(EXTI_##pin, EXTI_INTERRUPT, EXTI_TRIG_FALLING); \
    } while(0)

/* HPM common.h equivalent definitions */
#define HPM_ALIGN(n)        __attribute__((aligned(n)))
#define HPM_PACKED          __attribute__((packed))

/* HPM SDK macros */
#define SDK_DECLARE_EXT_ISR_M(irq, isr) \
    void isr(void)

/* Board-specific definitions */
#define BOARD_LED_GPIO_CTRL     GPIOC
#define BOARD_LED_GPIO_INDEX    GPIO_PIN_13
#define BOARD_LED_GPIO_PIN      13

/* LED control macros */
#define LED_SRAM_IO_PORT        GPIOC
#define LED_SRAM_IO_PIN         GPIO_PIN_13

/* HPM PPOR driver equivalent */
#define hpm_ppor_drv_h          1
static inline void ppor_sw_reset(uint32_t mask) {
    /* GD32 equivalent: software reset */
    NVIC_SystemReset();
}

/* Clock management */
static inline uint32_t clock_get_frequency(clock_name_t clock) {
    return SystemCoreClock;
}

/* Memory management */
#define HPM_L1C_CACHELINE_SIZE  32

/* FatFS and SD card support */
#define CONFIG_FATFS            1
#define CONFIG_SDMMC            1

/* Device configuration */
#ifndef DEVICE_ACC_TYPE_ADLX355
#define DEVICE_ACC_TYPE_ADLX355 1
#endif

/* Function prototypes for platform adaptation */
void platform_init(void);
void platform_gpio_init(void);
void platform_timer_init(void);
void platform_uart_init(void);

#ifdef __cplusplus
}
#endif

#endif /* __PLATFORM_ADAPTER_H */
