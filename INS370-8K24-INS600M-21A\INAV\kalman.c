/*****************************************************文件说明******************************************************************************/
/*文件名称：Kalman.C                                                                                                                       */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                                          */
/*编写人：                                                                                                                                 */
/*包含文件：const.h、typedefine.h、math.h、DATASTRUCT.h、EXTERNGLOBAL.h、memory.h                                                          */
/*测试用例：由GNSSlocusGen.m文件生成整套软件系统测试用例，单个模块测试用例暂缺                                                              */
/*说明：本文件包含了程序中用到的卡尔曼滤波解算函数。（此文件为初始测试版本，文件中所定义函数仅供程序设计人员参考选用）                     */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include <math.h>
#include "DATASTRUCT.h"
#include "GLOBALDATA.h"
#include "FUNCTION.h"
#include <string.h>

/*********************************函数说明***************************************/
/*函数名称：Kalman_StartUp                                                      */
/*函数功能描述：根据GNSS数据情况调节滤波模式                                    */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:lp_Kalman,指针:lp_GNSSData,指针:lp_SysVar,指针:lp_Navi   */
/*测试用例：暂缺                                                                */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化         */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递*/
/*备注3：该函数调用了C语言标准库函数memset（函数声明包含于memory.h中）          */
/*返回值：无                                                                    */
/********************************************************************************/
void Kalman_StartUp(p_Kalman lp_Kalman,p_GNSSData lp_GNSSData,p_SysVar lp_SysVar,p_Navi lp_Navi)
{
	if(lp_GNSSData -> isPosEn == YES)
	{
		//GNSS更新及有效状态字设置，同时将卫导收到时间清0
		lp_SysVar->isGNSS_Update = YES;
		lp_SysVar->isGNSSValid = YES;			  
		//从失锁纯惯状态下恢复组合的处理
		if((lp_SysVar->WorkPhase == PHASE_INS_NAVI)&&(lp_Kalman->isInsRecord == YES))
			{
						
				if(lp_SysVar->Time_INS_Alone > 180.0)
				{
						//失锁超过180s，重置导航经纬高和航向
						DPARA Logi_Err;
						lp_Navi->r_Lati -= lp_Kalman->r_InsLati - lp_GNSSData->r_GNSSLati;
							
						Logi_Err = lp_Kalman->r_InsLogi - lp_GNSSData->r_GNSSLogi;
						if(Logi_Err < -PI)
						{
								Logi_Err += 2 * PI;
						}
						else if(Logi_Err > PI)
						{
								Logi_Err -= 2 * PI;
						}
						lp_Navi->r_Logi -= Logi_Err;
						//东西经正负180度切换的判断
						if(lp_Navi->r_Logi > PI)
						{
								lp_Navi->r_Logi = lp_Navi->r_Logi - 2 * PI; //
						}
							
						if(lp_Navi->r_Logi < -PI)
						{
								lp_Navi->r_Logi = lp_Navi->r_Logi + 2 * PI; //
						}
							lp_Navi->Height -= lp_Kalman->InsHeight - lp_GNSSData->GNSSHeight;
							
						for(int i = 0;i<3;i++)
						{
							lp_Navi->Vn[i] -=  lp_Kalman->InsVn[i] - lp_GNSSData->GNSSVn[i];
						}
						//
						if((lp_Navi->isHeadingchange == NO)&&(lp_GNSSData->isHeadingEn == YES))
						{
							lp_Navi->r_Atti[0] = lp_GNSSData->r_GPSHead;
							AttiToCnb(lp_Navi->r_Atti,lp_Navi->Cnb);
							CnbToQ(lp_Navi->Cnb,lp_Navi->Q);
						}
				}				
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_SysVar.Xk_M[i] = g_Kalman.Xk[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_SysVar.Pk_M[i] = g_Kalman.Pk[i * DIM_STATE + i];
				}
        Kalman_Init(&g_Kalman);
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_Kalman.Xk[i] = g_SysVar.Xk_M[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_Kalman.Pk[i * DIM_STATE + i] = g_SysVar.Pk_M[i];
				}
				lp_Kalman->isInsRecord = NO;
			}
			
		//组合模式的调整
		if((lp_Kalman->isInsRecord == YES)&&(lp_SysVar->WorkPhase == PHASE_INTEGRATED_NAVI))
		{
			if((g_Kalman.Kal_Count >= COUNT_RESET_KALMAN_FILTER) && (g_Navi.isHeadingchange == NO))
			{
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_SysVar.Xk_M[i] = g_Kalman.Xk[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_SysVar.Pk_M[i] = g_Kalman.Pk[i * DIM_STATE + i];
				}
				Kalman_Init(&g_Kalman);
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_Kalman.Xk[i] = g_SysVar.Xk_M[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_Kalman.Pk[i * DIM_STATE + i] = g_SysVar.Pk_M[i];
				}
				lp_Kalman->isInsRecord = NO;
			}
			else
			{
				//触发滤波任务
				lp_Kalman->Work_Mode = FILTER;
				if(lp_GNSSData->isHeadingEn == YES)
				{
					lp_Kalman->Obv_Fit_Mode = POSVELHEAD_FIT;	
					lp_GNSSData->isHeadingEn = NO;
				}
				else
				{
					lp_Kalman->Obv_Fit_Mode = POSVEL_FIT;					
				}
				Rk_Init(g_Kalman.Rk,g_Kalman.Obv_Fit_Mode); 	
				Hk_Init(g_Kalman.Hk,g_Kalman.Obv_Fit_Mode);
				g_Kalman.isKalmanStart = YES;
				lp_Kalman->isInsRecord = NO;
					
			}
		}
	}
	else
	{
		lp_SysVar->isGNSS_Update = NO;
		lp_GNSSData->isHeadingEn = NO;
		//触发任务
		if((lp_Kalman->isInsRecord == YES)&&(lp_SysVar->WorkPhase == PHASE_INTEGRATED_NAVI))
		{
			lp_Kalman->Work_Mode = PREDICT;
			g_Kalman.isKalmanStart = YES;
			lp_Kalman->isInsRecord = NO;
		}
	}		
}

/*********************************函数说明***************************************/
/*函数名称：Kalman_Init                                                         */
/*函数功能描述：完成Kalman滤波结构体的初始化                                    */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:Kalman滤波结构体：lp_Kalman                              */
/*测试用例：暂缺                                                                */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化         */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递*/
/*备注3：该函数调用了C语言标准库函数memset（函数声明包含于memory.h中）          */
/*返回值：无                                                                    */
/********************************************************************************/
void Kalman_Init(p_Kalman lp_Kalman)
{
	  memset(lp_Kalman, 0, sizeof(Kalman));   //将g_Kalman结构体成员变量全部清零
	  lp_Kalman -> Obv_Fit_Mode = POSVEL_FIT;
	  Pk_Init(lp_Kalman -> Pk);               //状态误差协方差矩阵Pk的初始化
	  Qk_Init(lp_Kalman -> Qk);               //系统噪声协方差矩阵Qk的初始化
	  Rk_Init(lp_Kalman -> Rk,lp_Kalman -> Obv_Fit_Mode);               //量测噪声协方差矩阵Rk的初始化
	  Hk_Init(lp_Kalman -> Hk,lp_Kalman -> Obv_Fit_Mode);               //量测矩阵Hk的初始化
	  //滤波状态控制字
	  lp_Kalman -> isInsRecord = NO;
	  lp_Kalman -> isKalmanStart = NO;
	  lp_Kalman -> isCorrectError = NO;
	  
	  //误差修正默认设置
	  lp_Kalman -> isVnCorrect = YES;
	  lp_Kalman -> isPosCorrect = YES;
	  lp_Kalman -> isAttiCorrect = YES;
	  lp_Kalman -> isHeadingCorrect = YES;
	  lp_Kalman -> isGyroDriftCorrect = NO;
	  lp_Kalman -> isAccDriftCorrect = NO;
}

/*********************************************函数说明*******************************************************/
/*函数名称：Pk_Init                                                                                         */
/*函数功能描述：完成状态误差协方差矩阵Pk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入、输出变量：矩阵:Pk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Pk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Pk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Pk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Pk_Init(MATR Pk[DIM_STATE * DIM_STATE])
{
	  Pk[0 * DIM_STATE + 0] = 7.839279714436990e-07 * 7.839279714436990e-07;
	  Pk[1 * DIM_STATE + 1] = 7.839279714436990e-07 * 7.839279714436990e-07;
	  Pk[2 * DIM_STATE + 2] = 0.5 * 0.5;

	  Pk[3 * DIM_STATE + 3] = 0.35 * 0.35;
	  Pk[4 * DIM_STATE + 4] = 0.35 * 0.35;
	  Pk[5 * DIM_STATE + 5] = 0.35 * 0.35;

	  Pk[6 * DIM_STATE + 6] = (0.5 * D2R) * (0.5 * D2R);
	  Pk[7 * DIM_STATE + 7] = (0.5 * D2R) * (0.5 * D2R);
	  Pk[8 * DIM_STATE + 8] = (0.5 * D2R) * (0.5 * D2R);

	  Pk[9 * DIM_STATE + 9] = ((1.5 / 3600.0) * D2R) * ((1.5 / 3600.0) * D2R);
	  Pk[10 * DIM_STATE + 10] = ((1.5 / 3600.0) *D2R) *  ((1.5  / 3600.0) *D2R);
	  Pk[11 * DIM_STATE + 11] = ((1.5 / 3600.0) *D2R) *  ((1.5  / 3600.0) *D2R); 

	  Pk[12 * DIM_STATE + 12] = (0.00002 * G0) * (0.00002 * G0);
	  Pk[13 * DIM_STATE + 13] = (0.00002 * G0) * (0.00002 * G0);
	  Pk[14 * DIM_STATE + 14] = (0.00002 * G0) * (0.00002 * G0);
}

/*********************************************函数说明*******************************************************/
/*函数名称：Qk_Init                                                                                         */
/*函数功能描述：完成系统噪声协方差矩阵Qk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Qk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Qk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Qk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Qk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Qk_Init(MATR Qk[DIM_STATE * DIM_STATE])
{
		Qk[0 * DIM_STATE + 0] = 1.536357661029583e-15 * 2 * TIME_FILTER;
		Qk[1 * DIM_STATE + 1] = 1.536357661029583e-15 * 2 * TIME_FILTER;
		Qk[2 * DIM_STATE + 2] = 0.2506250000000000 * 2  * TIME_FILTER;

		Qk[3 * DIM_STATE + 3] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
		Qk[4 * DIM_STATE + 4] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
		Qk[5 * DIM_STATE + 5] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;

		Qk[6 * DIM_STATE + 6] = 1e-7  * 0.85 * TIME_FILTER;
		Qk[7 * DIM_STATE + 7] = 1e-7  * 0.85 * TIME_FILTER;
		Qk[8 * DIM_STATE + 8] = 1e-7  * 0.85 * TIME_FILTER;

		Qk[9 * DIM_STATE + 9] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[10 * DIM_STATE + 10] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[11 * DIM_STATE + 11] = 1e-21  * 0.85 / 100 * TIME_FILTER;

		Qk[12 * DIM_STATE + 12] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[13 * DIM_STATE + 13] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[14 * DIM_STATE + 14] = 1e-21  * 0.85 / 100 * TIME_FILTER;
}

/*********************************************函数说明*******************************************************/
/*函数名称：Rk_Init                                                                                         */
/*函数功能描述：完成系统噪声协方差矩阵Qk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Qk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Rk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Rk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Rk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Rk_Init(MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV],MODE Obv_Fit_Mode)
{
	IPARA i;

	for (i = 0; i < DIM_MAX_OBV * DIM_MAX_OBV; i++)
	{
		Rk[i] = 0;
	}
	switch(Obv_Fit_Mode)
	{
		case POS_FIT :
		{
			g_Kalman.Actual_Dim_Obv = 3;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			break;
		}
		case VEL_FIT :
		{
			g_Kalman.Actual_Dim_Obv = 3;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.25 * 0.25;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.25 * 0.25;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.25 * 0.25;
		  break;
		}
		case POSVEL_FIT :
		{
			g_Kalman.Actual_Dim_Obv = 6;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.25 * 0.25;
			Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.25 * 0.25;
			Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.25 * 0.25;
			break;
		}
		case POSVELHEAD_FIT :
		{
        g_Kalman.Actual_Dim_Obv = 7;
        Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
		  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.25 * 0.25;
			Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.25 * 0.25;
			Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.25 * 0.25;
        Rk[6 * g_Kalman.Actual_Dim_Obv + 6] = 1.5 * D2R * 1.5 * D2R;
			break;
		}
		case POS_HEAD_FIT :
		{
        g_Kalman.Actual_Dim_Obv = 4;
        Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
		  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
        Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 1.5 * D2R * 1.5 * D2R;
			break;
		}
		case VEL_HEAD_FIT :
		{
        g_Kalman.Actual_Dim_Obv = 4;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.25 * 0.25;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.25 * 0.25;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.25 * 0.25;
			Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 1.5 * D2R * 1.5 * D2R;
			break;
		}
		default:
		{
			g_Kalman.Actual_Dim_Obv = 3;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
		  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			break;
		}
   }
}

/*********************************************函数说明*******************************************************/
/*函数名称：Hk_Init                                                                                         */
/*函数功能描述：完成系统量测矩阵Hk的初始化                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Hk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Hk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：矩阵输入、输出变量Hk为地址传递                                                                     */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Hk_Init(MATR Hk[DIM_MAX_OBV * DIM_STATE],MODE Obv_Fit_Mode)
{
	register IPARA i;

    for (i = 0; i < DIM_MAX_OBV * DIM_STATE; i++)
	{
		Hk[i] = 0;
	}
	switch(Obv_Fit_Mode)
	{
		case POS_FIT :
		{
			for(i = 0; i < 3; i++)
			{
				Hk[i * DIM_STATE + i] = 1.0;
			}
			break;
		}
		case VEL_FIT :
		{
			Hk[0 * DIM_STATE + 3] = 1.0;
			Hk[1 * DIM_STATE + 4] = 1.0;
            Hk[2 * DIM_STATE + 5] = 1.0;
		    break;
		}
		case POSVEL_FIT :
		{
			for(i = 0; i < 6; i++)
			{
				Hk[i * DIM_STATE + i] = 1.0;
			}
			break;
		}
		case POSVELHEAD_FIT :
		{
            for(i = 0; i < 6; i++)
			{
				Hk[i * DIM_STATE + i] = 1.0;
			}
            Hk[6 * DIM_STATE + 2] = 1.0;
			break;
		}
		case POS_HEAD_FIT :
		{
            for(i = 0; i < 3; i++)
			{
				Hk[i * DIM_STATE + i] = 1.0;
			}
            Hk[3 * DIM_STATE + 2] = 1.0;
			break;
		}
		case VEL_HEAD_FIT :
		{
			Hk[0 * DIM_STATE + 3] = 1.0;
			Hk[1 * DIM_STATE + 4] = 1.0;
            Hk[2 * DIM_STATE + 5] = 1.0;
            Hk[3 * DIM_STATE + 2] = 1.0;
			break;
		}
		default:
		{
			for(i = 0; i < 3; i++)
			{
				Hk[i * DIM_STATE + i] = 1.0;
			}
			break;
		}
   }
}

/*********************************函数说明***************************************/
/*函数名称：KalCompute                                                          */
/*函数功能描述：完成一次Kalman滤波解算                                          */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:lp_GNSSData,指针:lp_Kalman                               */
/*测试用例：暂缺                                                                */
/*备注1：该函数为Kalman滤波解算全流程的封装函数，用于实时Kalman滤波解算        */
/*备注2：该函数输入变量、输出变量均为地址传递                                   */
/*返回值：无                                                                    */
/********************************************************************************/
void KalCompute(p_GNSSData lp_GNSSData, p_Kalman lp_Kalman)
{
	if(lp_Kalman->Work_Mode == FILTER)
	{
		//保存惯导数据
		SaveINSData(&g_Navi, lp_Kalman);
		//计算观测量
		ComputeZk(lp_GNSSData, lp_Kalman,lp_Kalman->Obv_Fit_Mode);
		//Kalman滤波预测
		KalPredict(lp_Kalman);
		//Kalman滤波更新
		ComputeKk(lp_Kalman->Pkk_1, lp_Kalman->Hk, lp_Kalman->Rk, lp_Kalman->Kk);
		ComputeXk(lp_Kalman->Xkk_1, lp_Kalman->Hk, lp_Kalman->Zk, lp_Kalman->Kk, lp_Kalman->Xk);
		ComputePk(lp_Kalman->Pkk_1, lp_Kalman->Hk, lp_Kalman->Kk, lp_Kalman->Pk);
		//误差修正
		ErrCorrect(&g_Navi, lp_Kalman, lp_GNSSData);
		lp_Kalman->Kal_Count++;
	}
	else if(lp_Kalman->Work_Mode == PREDICT)
	{
		//保存惯导数据
		SaveINSData(&g_Navi, lp_Kalman);
		//Kalman滤波预测
		KalPredict(lp_Kalman);
		//将预测结果作为当前估计
		for(int i = 0; i < DIM_STATE; i++)
		{
			lp_Kalman->Xk[i] = lp_Kalman->Xkk_1[i];
		}
		for(int i = 0; i < DIM_STATE * DIM_STATE; i++)
		{
			lp_Kalman->Pk[i] = lp_Kalman->Pkk_1[i];
		}
		lp_Kalman->Kal_Count++;
	}
}

/*********************************************函数说明*******************************************************/
/*函数名称：KalPredict                                                                                      */
/*函数功能描述：完成一次完整的Kalman预测计算                                                               */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                            */
/*编写人：                                                                                                   */
/*输入变量：指针:lp_GNSSData                                                                                 */
/*输出变量：指针：lp_Kalman                                                                                 */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数为Kalman滤波解算全流程的封装函数，用于实时滤波解算                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void KalPredict(p_Kalman lp_Kalman)
{
	INT32 i = 0;
	//预测更新
	ComputeFk(lp_Kalman->Fn, lp_Kalman->Fk);
	ComputeXkk_1(lp_Kalman->Fk, lp_Kalman->Xk, lp_Kalman->Xkk_1);
	ComputePkk_1_Step1(lp_Kalman);
	ComputePkk_1_Step2(lp_Kalman);

	for (i = 0; i < DIM_STATE * DIM_STATE; i++)
	{
		lp_Kalman->Pk[i] = lp_Kalman->Pkk_1[i];
	}
	for (i = 0; i < DIM_STATE; i++)
	{
		lp_Kalman->Xk[i] = lp_Kalman->Xkk_1[i];
	}
	lp_Kalman -> Kal_Predict_Count++;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeFn                                                                                       */
/*函数功能描述：完成一步状态转移矩阵累加部分的计算                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：指针:lp_Navi                                                                                    */
/*输出变量：矩阵：Fn                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数计算完成后，会将计算结果累加到Fn矩阵中，以便后续计算一步状态转移矩阵                         */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeFn(p_Navi const lp_Navi, MATR Fn[DIM_STATE * DIM_STATE])
{
	register DPARA SINLATI = sin(lp_Navi -> r_Lati);
	register DPARA COSLATI = cos(lp_Navi -> r_Lati);
	register DPARA INVCOSLATI = 1.0 / COSLATI;
	register DPARA TANLATI = tan(lp_Navi -> r_Lati);

	//位置误差方程系数
	Fn[0 * DIM_STATE + 3] += 1.0 * lp_Navi -> invRm;
	Fn[1 * DIM_STATE + 5] += 1.0 * lp_Navi -> invRn * INVCOSLATI;
	Fn[2 * DIM_STATE + 4] += -1.0;

	//速度误差方程系数
	Fn[3 * DIM_STATE + 0] += -lp_Navi -> Vn[1] * lp_Navi -> invRm * lp_Navi -> invRm;
	Fn[3 * DIM_STATE + 2] += lp_Navi -> Vn[1] * lp_Navi -> invRm;
	Fn[3 * DIM_STATE + 4] += -lp_Navi -> invRm;
	Fn[3 * DIM_STATE + 6] += lp_Navi -> r_Wien[1] + lp_Navi -> r_Wenn[1];
	Fn[3 * DIM_STATE + 8] += -1 * (lp_Navi -> r_Wien[0] + lp_Navi -> r_Wenn[0]);
	Fn[3 * DIM_STATE + 9] += -lp_Navi -> Cnb[0];
	Fn[3 * DIM_STATE + 10] += -lp_Navi -> Cnb[3];
	Fn[3 * DIM_STATE + 11] += -lp_Navi -> Cnb[6];

	Fn[4 * DIM_STATE + 0] += lp_Navi -> Vn[2] * lp_Navi -> invRn * TANLATI * INVCOSLATI;
	Fn[4 * DIM_STATE + 2] += -lp_Navi -> Vn[2] * lp_Navi -> invRn * INVCOSLATI * INVCOSLATI;
	Fn[4 * DIM_STATE + 3] += lp_Navi -> invRn * TANLATI;
	Fn[4 * DIM_STATE + 5] += -lp_Navi -> invRn;
	Fn[4 * DIM_STATE + 6] += -lp_Navi -> r_Wien[2];
	Fn[4 * DIM_STATE + 8] += lp_Navi -> r_Wien[2];
	Fn[4 * DIM_STATE + 12] += -lp_Navi -> Cnb[1];
	Fn[4 * DIM_STATE + 13] += -lp_Navi -> Cnb[4];
	Fn[4 * DIM_STATE + 14] += -lp_Navi -> Cnb[7];

	Fn[5 * DIM_STATE + 0] += -lp_Navi -> Vn[0] * lp_Navi -> invRm * lp_Navi -> invRm - lp_Navi -> Vn[2] * lp_Navi -> invRn * lp_Navi -> invRn;
	Fn[5 * DIM_STATE + 2] += lp_Navi -> Vn[0] * lp_Navi -> invRm + lp_Navi -> Vn[2] * lp_Navi -> invRn;
	Fn[5 * DIM_STATE + 3] += lp_Navi -> invRm;
	Fn[5 * DIM_STATE + 5] += lp_Navi -> invRn;
	Fn[5 * DIM_STATE + 7] += lp_Navi -> r_Wien[0] + lp_Navi -> r_Wenn[0];
	Fn[5 * DIM_STATE + 6] += -lp_Navi -> r_Wien[1] - lp_Navi -> r_Wenn[1];
	Fn[5 * DIM_STATE + 12] += -lp_Navi -> Cnb[2];
	Fn[5 * DIM_STATE + 13] += -lp_Navi -> Cnb[5];
	Fn[5 * DIM_STATE + 14] += -lp_Navi -> Cnb[8];

	//姿态误差方程系数
	Fn[6 * DIM_STATE + 0] += -lp_Navi -> Vn[2] * lp_Navi -> invRn * TANLATI * INVCOSLATI;
	Fn[6 * DIM_STATE + 2] += lp_Navi -> Vn[2] * lp_Navi -> invRn * INVCOSLATI * INVCOSLATI;
	Fn[6 * DIM_STATE + 5] += lp_Navi -> invRn;
	Fn[6 * DIM_STATE + 7] += lp_Navi -> r_Wenn[2];
	Fn[6 * DIM_STATE + 8] += -1 * (lp_Navi -> r_Wien[1] + lp_Navi -> r_Wenn[1]);
	Fn[6 * DIM_STATE + 9] += -lp_Navi -> Cnb[0];
	Fn[6 * DIM_STATE + 10] += -lp_Navi -> Cnb[3];
	Fn[6 * DIM_STATE + 11] += -lp_Navi -> Cnb[6];

	Fn[7 * DIM_STATE + 0] += lp_Navi ->r_Wien[0] + lp_Navi -> r_Wenn[0] * INVCOSLATI * INVCOSLATI;
	Fn[7 * DIM_STATE + 2] += -lp_Navi -> r_Wenn[1] * lp_Navi -> invRn;
	Fn[7 * DIM_STATE + 5] += lp_Navi ->invRn * SINLATI * INVCOSLATI;
	Fn[7 * DIM_STATE + 6] += -lp_Navi ->r_Wenn[2];
	Fn[7 * DIM_STATE + 8] += lp_Navi ->r_Wien[0] + lp_Navi ->r_Wenn[0];
	Fn[7 * DIM_STATE + 9] += -lp_Navi ->Cnb[1];
	Fn[7 * DIM_STATE + 10] += -lp_Navi -> Cnb[4];
	Fn[7 * DIM_STATE + 11] += -lp_Navi ->Cnb[7];

	Fn[8 * DIM_STATE + 2] += lp_Navi ->Vn[0] * lp_Navi -> invRm * lp_Navi -> invRm;
	Fn[8 * DIM_STATE + 3] += - lp_Navi ->invRm;
	Fn[8 * DIM_STATE + 6] += lp_Navi ->r_Wien[1] + lp_Navi ->r_Wenn[1];
	Fn[8 * DIM_STATE + 7] += -1 * (lp_Navi ->r_Wien[0] + lp_Navi ->r_Wenn[0]);
	Fn[8 * DIM_STATE + 9] += -lp_Navi ->Cnb[2];
	Fn[8 * DIM_STATE + 10] += -lp_Navi ->Cnb[5];
	Fn[8 * DIM_STATE + 11] += -lp_Navi ->Cnb[8];

	g_Kalman.ComputeFn_Count ++;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeFk                                                                                       */
/*函数功能描述：完成一步状态转移矩阵的计算                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Fn                                                                                         */
/*输出变量：矩阵：Fk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数计算完成后，会将一步状态转移矩阵的累加部分清零，以便重新开始累加计算                         */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeFk(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE])
{
	register IPARA i;
	register IPARA j;
	register DPARA Acc_E = 0.0;
	register DPARA Acc_N = 0.0;
	register DPARA Acc_U = 0.0;

    if(g_Kalman.ComputeFn_Count != 0)
	{
		 Acc_E = Fn[4 * DIM_STATE + 6] / g_Kalman.ComputeFn_Count;
	     Acc_N = Fn[4 * DIM_STATE + 8] / g_Kalman.ComputeFn_Count;
		 Acc_U = Fn[3 * DIM_STATE + 8] / g_Kalman.ComputeFn_Count;
		 g_Kalman.Acc_Horizontal = sqrt(Acc_E * Acc_E + Acc_N * Acc_N);
		 g_Kalman.Acc_All = sqrt(Acc_E * Acc_E + Acc_N * Acc_N + Acc_U * Acc_U);
	}
	else
	{
		 g_Kalman.Acc_Horizontal = 0.0;
		 g_Kalman.Acc_All = 0.0;
	}
	for(i = 0; i < DIM_STATE; i++)
	{
		for(j = 0; j < DIM_STATE; j++)
		{
			Fk[i * DIM_STATE + j] = Fn[i * DIM_STATE + j] * TIME_FILTER;
			if(i == j)
			{
				Fk[i * DIM_STATE + j] += 1.0;
			}
			Fn[i * DIM_STATE + j] = 0.0; //Fn对应元素被清零
		}
	}
	g_Kalman.ComputeFn_Count = 0;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeXkk_1                                                                                    */
/*函数功能描述：完成一步预测误差状态向量的计算                                                              */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Fk、向量：Xk                                                                               */
/*输出变量：向量：Xkk_1                                                                                     */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeXkk_1(MATR const Fk[DIM_STATE * DIM_STATE], VEC const Xk[DIM_STATE],VEC Xkk_1[DIM_STATE])
{
	register IPARA i,j = 0;

	for (i = 0; i < DIM_STATE; i++)
	{
			Xkk_1[i] = 0.0;     //对应向量元素先清零
			for (j = 0; j < DIM_STATE; j++)
			{
				Xkk_1[i] += Fk[i * DIM_STATE + j] * Xk[j];
			}
	}
}

void ComputePkk_1_Step1(p_Kalman lp_Kalman)
{
	Mat_Tr(lp_Kalman ->Fk , lp_Kalman ->TrFk,DIM_STATE,DIM_STATE);
	Mat_Mul(lp_Kalman ->Fk,lp_Kalman ->Pk,lp_Kalman ->Fk_Pk, DIM_STATE, DIM_STATE, DIM_STATE);
}

void ComputePkk_1_Step2(p_Kalman lp_Kalman)
{
	register IPARA i,j = 0;

	Mat_Mul(lp_Kalman ->Fk_Pk,lp_Kalman ->TrFk,lp_Kalman ->Pkk_1, DIM_STATE, DIM_STATE, DIM_STATE);

	for(i = 0; i < DIM_STATE; i++)
	{
		for(j = 0; j < DIM_STATE; j++)
		{
			lp_Kalman ->Pkk_1[i * DIM_STATE + j] += lp_Kalman ->Qk[i * DIM_STATE + j];
		}
	}
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeKk                                                                                       */
/*函数功能描述：完成预测误差修正矩阵的计算                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Pkk_1、矩阵：Hk、矩阵：Rk                                                                  */
/*输出变量：矩阵：Kk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeKk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV])
{
		register IPARA i,j = 0;
		MATR Temp1[DIM_STATE * DIM_MAX_OBV];
		MATR Temp2[DIM_MAX_OBV * DIM_MAX_OBV];
		MATR TrHk[DIM_STATE * DIM_MAX_OBV ];

		Mat_Tr(Hk,TrHk,g_Kalman.Actual_Dim_Obv,DIM_STATE);
		Mat_Mul(Pkk_1, TrHk,Temp1,DIM_STATE,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		Mat_Mul(Hk, Temp1,Temp2,g_Kalman.Actual_Dim_Obv,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		for(i = 0; i < g_Kalman.Actual_Dim_Obv ; i++)
		{
			for(j = 0; j < g_Kalman.Actual_Dim_Obv ; j++)
			{
				Temp2[i * g_Kalman.Actual_Dim_Obv + j] += Rk[i * g_Kalman.Actual_Dim_Obv + j];
			}
		}
		Mat_Inv(Temp2,Temp2,g_Kalman.Actual_Dim_Obv);
		Mat_Mul(Temp1,Temp2,Kk,DIM_STATE,g_Kalman.Actual_Dim_Obv,g_Kalman.Actual_Dim_Obv);
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputePk                                                                                       */
/*函数功能描述：完成状态误差协方差矩阵的计算                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Pkk_1、矩阵：Hk、矩阵：Kk                                                                  */
/*输出变量：矩阵：Pk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputePk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Kk[DIM_STATE * DIM_MAX_OBV], MATR Pk[DIM_STATE * DIM_STATE])
{
	register IPARA i,j = 0;
	MATR Temp[DIM_STATE * DIM_STATE];

	Mat_Mul(Kk,Hk,Temp,DIM_STATE,g_Kalman.Actual_Dim_Obv,DIM_STATE);

	for(i = 0; i < DIM_STATE ; i++)
	{
		for(j = 0; j < DIM_STATE ; j++)
		{
			Temp[i * DIM_STATE + j] = -1 * Temp[i * DIM_STATE + j];
			if(i == j)
			{
				Temp[i * DIM_STATE + j] += 1.0;
			}
		}
	}

	Mat_Mul(Temp,Pkk_1,Pk,DIM_STATE,DIM_STATE,DIM_STATE);
}
