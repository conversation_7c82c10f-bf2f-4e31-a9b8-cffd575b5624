/*****************************************************文件说明******************************************************************************/
/*文件名称：Kalman.C                                                                                                                       */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                                          */
/*编写人：                                                                                                                                 */
/*包含文件：const.h、typedefine.h、math.h、DATASTRUCT.h、EXTERNGLOBAL.h、memory.h                                                          */
/*测试用例：由GNSSlocusGen.m文件生成整套软件系统测试用例，单个模块测试用例暂缺                                                              */
/*说明：本文件包含了程序中用到的卡尔曼滤波解算函数。（此文件为初始测试版本，文件中所定义函数仅供程序设计人员参考选用）                     */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include <math.h>
#include "DATASTRUCT.h"
#include "GLOBALDATA.h"
#include "FUNCTION.h"
#include <string.h>

/*********************************函数说明***************************************/
/*函数名称：Kalman_StartUp                                                      */
/*函数功能描述：根据GNSS数据情况调节滤波模式                                    */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:lp_Kalman,指针:lp_GNSSData,指针:lp_SysVar,指针:lp_Navi   */
/*测试用例：暂缺                                                                */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化         */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递*/
/*备注3：该函数调用了C语言标准库函数memset（函数声明包含于memory.h中）          */
/*返回值：无                                                                    */
/********************************************************************************/
void Kalman_StartUp(p_Kalman lp_Kalman,p_GNSSData lp_GNSSData,p_SysVar lp_SysVar,p_Navi lp_Navi)
{
	if(lp_GNSSData -> isPosEn == YES)
	{
		//GNSS更新及有效状态字设置，同时将卫导收到时间清0
		lp_SysVar->isGNSS_Update = YES;
		lp_SysVar->isGNSSValid = YES;			  
		//从失锁纯惯状态下恢复组合的处理
		if((lp_SysVar->WorkPhase == PHASE_INS_NAVI)&&(lp_Kalman->isInsRecord == YES))
			{
						
				if(lp_SysVar->Time_INS_Alone > 180.0)
				{
						//失锁超过180s，重置导航经纬高和航向
						DPARA Logi_Err;
						lp_Navi->r_Lati -= lp_Kalman->r_InsLati - lp_GNSSData->r_GNSSLati;
							
						Logi_Err = lp_Kalman->r_InsLogi - lp_GNSSData->r_GNSSLogi;
						if(Logi_Err < -PI)
						{
								Logi_Err += 2 * PI;
						}
						else if(Logi_Err > PI)
						{
								Logi_Err -= 2 * PI;
						}
						lp_Navi->r_Logi -= Logi_Err;
						//东西经正负180度切换的判断
						if(lp_Navi->r_Logi > PI)
						{
								lp_Navi->r_Logi = lp_Navi->r_Logi - 2 * PI; //
						}
							
						if(lp_Navi->r_Logi < -PI)
						{
								lp_Navi->r_Logi = lp_Navi->r_Logi + 2 * PI; //
						}
							lp_Navi->Height -= lp_Kalman->InsHeight - lp_GNSSData->GNSSHeight;
							
						for(int i = 0;i<3;i++)
						{
							lp_Navi->Vn[i] -=  lp_Kalman->InsVn[i] - lp_GNSSData->GNSSVn[i];
						}
						//
						if((lp_Navi->isHeadingchange == NO)&&(lp_GNSSData->isHeadingEn == YES))
						{
							lp_Navi->r_Atti[0] = lp_GNSSData->r_GPSHead;
							AttiToCnb(lp_Navi->r_Atti,lp_Navi->Cnb);
							CnbToQ(lp_Navi->Cnb,lp_Navi->Q);
						}
				}				
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_SysVar.Xk_M[i] = g_Kalman.Xk[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_SysVar.Pk_M[i] = g_Kalman.Pk[i * DIM_STATE + i];
				}
        Kalman_Init(&g_Kalman);
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_Kalman.Xk[i] = g_SysVar.Xk_M[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_Kalman.Pk[i * DIM_STATE + i] = g_SysVar.Pk_M[i];
				}
				lp_Kalman->isInsRecord = NO;
			}
			
		//组合模式的调整
		if((lp_Kalman->isInsRecord == YES)&&(lp_SysVar->WorkPhase == PHASE_INTEGRATED_NAVI))
		{
			if((g_Kalman.Kal_Count >= COUNT_RESET_KALMAN_FILTER) && (g_Navi.isHeadingchange == NO))
			{
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_SysVar.Xk_M[i] = g_Kalman.Xk[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_SysVar.Pk_M[i] = g_Kalman.Pk[i * DIM_STATE + i];
				}
				Kalman_Init(&g_Kalman);
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_Kalman.Xk[i] = g_SysVar.Xk_M[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_Kalman.Pk[i * DIM_STATE + i] = g_SysVar.Pk_M[i];
				}
				lp_Kalman->isInsRecord = NO;
			}
			else
			{
				//触发滤波任务
				lp_Kalman->Work_Mode = FILTER;
				if(lp_GNSSData->isHeadingEn == YES)
				{
					lp_Kalman->Obv_Fit_Mode = POSVELHEAD_FIT;	
					lp_GNSSData->isHeadingEn = NO;
				}
				else
				{
					lp_Kalman->Obv_Fit_Mode = POSVEL_FIT;					
				}
				Rk_Init(g_Kalman.Rk,g_Kalman.Obv_Fit_Mode); 	
				Hk_Init(g_Kalman.Hk,g_Kalman.Obv_Fit_Mode);
				g_Kalman.isKalmanStart = YES;
				lp_Kalman->isInsRecord = NO;
					
			}
		}
	}
	else
	{
		lp_SysVar->isGNSS_Update = NO;
		lp_GNSSData->isHeadingEn = NO;
		//触发任务
		if((lp_Kalman->isInsRecord == YES)&&(lp_SysVar->WorkPhase == PHASE_INTEGRATED_NAVI))
		{
			lp_Kalman->Work_Mode = PREDICT;
			g_Kalman.isKalmanStart = YES;
			lp_Kalman->isInsRecord = NO;
		}
	}		
}

/*********************************函数说明***************************************/
/*函数名称：Kalman_Init                                                         */
/*函数功能描述：完成Kalman滤波结构体的初始化                                    */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:Kalman滤波结构体：lp_Kalman                              */
/*测试用例：暂缺                                                                */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化         */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递*/
/*备注3：该函数调用了C语言标准库函数memset（函数声明包含于memory.h中）          */
/*返回值：无                                                                    */
/********************************************************************************/
void Kalman_Init(p_Kalman lp_Kalman)
{
	  memset(lp_Kalman, 0, sizeof(Kalman));   //将g_Kalman结构体成员变量全部清零
	  lp_Kalman -> Obv_Fit_Mode = POSVEL_FIT;
	  Pk_Init(lp_Kalman -> Pk);               //状态误差协方差矩阵Pk的初始化
	  Qk_Init(lp_Kalman -> Qk);               //系统噪声协方差矩阵Qk的初始化
	  Rk_Init(lp_Kalman -> Rk,lp_Kalman -> Obv_Fit_Mode);               //量测噪声协方差矩阵Rk的初始化
	  Hk_Init(lp_Kalman -> Hk,lp_Kalman -> Obv_Fit_Mode);               //量测矩阵Hk的初始化
	  //滤波状态控制字
	  lp_Kalman -> isInsRecord = NO;
	  lp_Kalman -> isKalmanStart = NO;
	  lp_Kalman -> isCorrectError = NO;
	  
	  //误差修正默认设置
	  lp_Kalman -> isVnCorrect = YES;
	  lp_Kalman -> isPosCorrect = YES;
	  lp_Kalman -> isAttiCorrect = YES;
	  lp_Kalman -> isHeadingCorrect = YES;
	  lp_Kalman -> isGyroDriftCorrect = NO;
	  lp_Kalman -> isAccDriftCorrect = NO;
}

/*********************************************函数说明*******************************************************/
/*函数名称：Pk_Init                                                                                         */
/*函数功能描述：完成状态误差协方差矩阵Pk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入、输出变量：矩阵:Pk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Pk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Pk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Pk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Pk_Init(MATR Pk[DIM_STATE * DIM_STATE])
{
	  Pk[0 * DIM_STATE + 0] = 7.839279714436990e-07 * 7.839279714436990e-07;
	  Pk[1 * DIM_STATE + 1] = 7.839279714436990e-07 * 7.839279714436990e-07;
	  Pk[2 * DIM_STATE + 2] = 0.5 * 0.5;

	  Pk[3 * DIM_STATE + 3] = 0.35 * 0.35;
	  Pk[4 * DIM_STATE + 4] = 0.35 * 0.35;
	  Pk[5 * DIM_STATE + 5] = 0.35 * 0.35;

	  Pk[6 * DIM_STATE + 6] = (0.5 * D2R) * (0.5 * D2R);
	  Pk[7 * DIM_STATE + 7] = (0.5 * D2R) * (0.5 * D2R);
	  Pk[8 * DIM_STATE + 8] = (0.5 * D2R) * (0.5 * D2R);

	  Pk[9 * DIM_STATE + 9] = ((1.5 / 3600.0) * D2R) * ((1.5 / 3600.0) * D2R);
	  Pk[10 * DIM_STATE + 10] = ((1.5 / 3600.0) *D2R) *  ((1.5  / 3600.0) *D2R);
	  Pk[11 * DIM_STATE + 11] = ((1.5 / 3600.0) *D2R) *  ((1.5  / 3600.0) *D2R); 

	  Pk[12 * DIM_STATE + 12] = (0.00002 * G0) * (0.00002 * G0);
	  Pk[13 * DIM_STATE + 13] = (0.00002 * G0) * (0.00002 * G0);
	  Pk[14 * DIM_STATE + 14] = (0.00002 * G0) * (0.00002 * G0);
}

/*********************************************函数说明*******************************************************/
/*函数名称：Qk_Init                                                                                         */
/*函数功能描述：完成系统噪声协方差矩阵Qk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Qk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Qk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Qk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Qk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Qk_Init(MATR Qk[DIM_STATE * DIM_STATE])
{
		Qk[0 * DIM_STATE + 0] = 1.536357661029583e-15 * 2 * TIME_FILTER;
		Qk[1 * DIM_STATE + 1] = 1.536357661029583e-15 * 2 * TIME_FILTER;
		Qk[2 * DIM_STATE + 2] = 0.2506250000000000 * 2  * TIME_FILTER;

		Qk[3 * DIM_STATE + 3] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
		Qk[4 * DIM_STATE + 4] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
		Qk[5 * DIM_STATE + 5] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;

		Qk[6 * DIM_STATE + 6] = 1e-7  * 0.85 * TIME_FILTER;
		Qk[7 * DIM_STATE + 7] = 1e-7  * 0.85 * TIME_FILTER;
		Qk[8 * DIM_STATE + 8] = 1e-7  * 0.85 * TIME_FILTER;

		Qk[9 * DIM_STATE + 9] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[10 * DIM_STATE + 10] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[11 * DIM_STATE + 11] = 1e-21  * 0.85 / 100 * TIME_FILTER;

		Qk[12 * DIM_STATE + 12] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[13 * DIM_STATE + 13] = 1e-21  * 0.85 / 100 * TIME_FILTER;
		Qk[14 * DIM_STATE + 14] = 1e-21  * 0.85 / 100 * TIME_FILTER;
}

/*********************************************函数说明*******************************************************/
/*函数名称：Rk_Init                                                                                         */
/*函数功能描述：完成系统噪声协方差矩阵Qk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Qk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Rk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Rk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Rk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Rk_Init(MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV],MODE Obv_Fit_Mode)
{
	IPARA i;

	for (i = 0; i < DIM_MAX_OBV * DIM_MAX_OBV; i++)
	{
		Rk[i] = 0;
	}
	switch(Obv_Fit_Mode)
	{
		case POS_FIT :
		{
			g_Kalman.Actual_Dim_Obv = 3;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			break;
		}
		case VEL_FIT :
		{
			g_Kalman.Actual_Dim_Obv = 3;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.25 * 0.25;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.25 * 0.25;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.25 * 0.25;
		  break;
		}
		case POSVEL_FIT :
		{
			g_Kalman.Actual_Dim_Obv = 6;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.25 * 0.25;
			Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.25 * 0.25;
			Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.25 * 0.25;
			break;
		}
		case POSVELHEAD_FIT :
		{
        g_Kalman.Actual_Dim_Obv = 7;
        Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
		  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.25 * 0.25;
			Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.25 * 0.25;
			Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.25 * 0.25;
        Rk[6 * g_Kalman.Actual_Dim_Obv + 6] = 1.5 * D2R * 1.5 * D2R;
			break;
		}
		case POS_HEAD_FIT :
		{
        g_Kalman.Actual_Dim_Obv = 4;
        Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
		  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
        Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 1.5 * D2R * 1.5 * D2R;
			break;
		}
		case VEL_HEAD_FIT :
		{
        g_Kalman.Actual_Dim_Obv = 4;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.25 * 0.25;
			Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.25 * 0.25;
			Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.25 * 0.25;
			Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 1.5 * D2R * 1.5 * D2R;
			break;
		}
		default:
		{
			g_Kalman.Actual_Dim_Obv = 3;
			Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
		  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;
			break;
		}
   }
}
