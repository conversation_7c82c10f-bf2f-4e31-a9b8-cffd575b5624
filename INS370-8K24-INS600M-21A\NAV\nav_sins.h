/*!
    \file  nav_sins.h
    \brief Strapdown Inertial Navigation System header file
*/

#ifndef __NAV_SINS_H
#define __NAV_SINS_H

#include "nav.h"

typedef enum {
    NAV_SINS_STATUS_OK = 0,
    NAV_SINS_STATUS_ERROR
} nav_sins_status_t;

typedef struct {
    double position[3];  /* Latitude, Longitude, Altitude */
    float velocity[3];   /* North, East, Up velocity */
    float attitude[3];   /* Heading, Pitch, Roll */
} nav_sins_result_t;

nav_sins_status_t nav_sins_init(void);
nav_sins_status_t nav_sins_update(const nav_imu_data_t *imu_data, nav_sins_result_t *result);

#endif /* __NAV_SINS_H */
