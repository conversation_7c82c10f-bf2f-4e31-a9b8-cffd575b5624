/*!
    \file  appmain.h
    \brief the header file of main
    \note  Migrated from HPM6750 to GD32F4xx platform
*/
#ifndef __APPMAIN_H
#define __APPMAIN_H

/* Standard includes */
#include "systick.h"
#include <stdio.h>

/* Platform includes */
#include "main.h"
#include "platform_adapter.h"

/* GD32F4xx specific includes */
#include "gd32f4xx.h"


#include "INS_Data.h"
#include "INS_Sys.h"
#include "gnss.h"
#include "bsp_fmc.h"
#include "logger.h"
#include "time_unify.h"
#include "bsp_tim.h"
//#include "nav_app.h"
#include "fpgad.h"
//#include "nav.h"
#include "config.h"
#include "computerFrameParse.h"
#include "INS912AlgorithmEntry.h"
#include "appdefine.h"
#include "gdtypedefine.h"
#include "frame_analysis.h"
#include "InsTestingEntry.h"

#include "deviceconfig.h"
#include "datado.h"
#include "SetParaBao.h"
#include "FirmwareUpdateFile.h"


void GetChipID(void);
void INS_Init(void);
//void StartNavigation(void);
void StopNavigation(void);
void protocol_send(void);


int checkUSBReady(void);
void LEDIndicator(uint8_t state);
void bsp_systick_init01(uint32_t com);
void uart4sendmsg(char *txbuf, int size);
void get_fpgadata(void);
void comm_store_init(void);
void INS912_Output(navoutdata_t *pnavout);


extern	unsigned int gprotocol_send_baudrate;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600
extern	int	gfpgasenddatalen;
extern	uint8_t fpga_syn;
extern uint32_t fpga_syn_count;
extern uint32_t fpga_loop_count;
extern	int gbilldebuguart4;

#endif /* __APPMAIN_H */


