/*!
    \file  nav_gnss.c
    \brief Navigation GNSS interface
*/

#include "nav_gnss.h"
#include "../INAV/FUNCTION.h"
#include "../INAV/GLOBALDATA.h"

nav_gnss_status_t nav_gnss_init(void)
{
    return NAV_GNSS_STATUS_OK;
}

nav_gnss_status_t nav_gnss_update(const nav_gnss_data_t *gnss_data)
{
    if (gnss_data == NULL) {
        return NAV_GNSS_STATUS_ERROR;
    }
    
    if (gnss_data->valid) {
        Read_And_Check_GNSS_Data(&g_GNSSData_In_Use, (void*)gnss_data, &g_Navi, &g_SysVar);
    }
    
    return NAV_GNSS_STATUS_OK;
}
