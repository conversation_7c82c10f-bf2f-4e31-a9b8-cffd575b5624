/*
 * Copyright (c) 2022-2025 HPMicro
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */

#ifndef __UART_DMA_H
#define __UART_DMA_H

#include "hpm_uart_drv.h"
#ifdef HPMSOC_HAS_HPMSDK_DMAV2
#include "hpm_dmav2_drv.h"
#else
#include "hpm_dma_drv.h"
#endif
#include "hpm_dmamux_drv.h"

typedef struct
{
    UART_Type *uart_base;
    uint32_t uart_src_freq;
    uint32_t baudrate;
    bool *tx_done;
    uint8_t core_id;
    uint8_t *tx_buff;
    uint32_t tx_size;
    uint8_t *rx_buff;
    uint32_t rx_size;
    uint32_t old_rxlen;
    DMAMUX_Type *dmamux_base;
    uint8_t dmairq;
    uint8_t dmamux_tx_src;
    uint8_t dmamux_rx_src;
    DMA_Type *dma_base;
    uint32_t dma_tx_ch;
    uint32_t dma_rx_ch;
    dma_linked_descriptor_t *tx_descriptor;
    dma_linked_descriptor_t *rx_descriptor;
} uart_dma_ctx_t;

uint32_t uart_dma_recv_polling(void *handle, uint8_t *data, uint32_t maxsize);

bool get_uart_tx_idle(void *handle);

int uart_dma_output(void *handle, uint8_t *data, uint16_t length);

int uart_dma_init(void *handle);

#endif //__UART_DMA_H