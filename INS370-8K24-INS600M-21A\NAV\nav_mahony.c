/*!
    \file  nav_mahony.c
    \brief Navigation Mahony filter interface
*/

#include "nav_mahony.h"
#include "../INAV/FUNCTION.h"

nav_mahony_status_t nav_mahony_init(void)
{
    return NAV_MAHONY_STATUS_OK;
}

nav_mahony_status_t nav_mahony_update(const nav_imu_data_t *imu_data, const nav_magnet_data_t *mag_data, nav_mahony_result_t *result)
{
    (void)imu_data;
    (void)mag_data;
    (void)result;
    return NAV_MAHONY_STATUS_OK;
}
