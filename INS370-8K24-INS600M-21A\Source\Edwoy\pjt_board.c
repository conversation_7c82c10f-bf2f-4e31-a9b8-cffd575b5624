/*!
    \file  b gd32f470i_eval.c
    \brief firmware functions to manage leds, keys, COM ports
    
    \version 2024-01-17, V2.6.4, demo for GD32F4xx
*/

/*
    Copyright (c) 2024, GigaDevice Semiconductor Inc.
*/

#include "pjt_glb_head.h"
#include "pjt_board.h"
#include "stdarg.h"
#include "stdlib.h"
#include "stdint.h"
#include "string.h"
#include "stdlib.h"
#include "string.h"


///===CommonDatas==========================================================================================================================
//--update 2024.3.26---------------------------------------------------------------------------
uint8_t  g_r8bit;   
uint16_t g_buf_r16bit[16] = {0};
uint32_t g_retry , g_tmo ;
//--end update 2024.3.26-----------------------------------------------------------------------
///===End CommonDatas===================================================================================================End CommonDatas====


///===Track-Datas==========================================================================================================================
//--update 2024.6.27---------------------------------------------------------------------------------
uint32_t g_fpga_syn_cnt , g_alg_entr_cnt = 0;
//--end update 2024.6.6------------------------------------------------------------------------------
///===End Track-Datas===================================================================================================End Track-Datas====


///===Tx-FPGA_RawDatas=====================================================================================================================
/*--update 2024.6.6---------------------------------------------------------------------------------
//sttTxFm_CFRD_t g_txfm_cfrd = {
//  .head = 0x66AA,  .cmd = 0x0000, .length = 2 * (FPGA_CFRD16bit_Qty + 4),
//};

//unnTxFm_CarryFpgaRD_t g_txfm_cfrd = {
//  .frame = { .head = 0x66AA,  .cmd = 0x0000, .length = 2 * (FPGA_CFRD16bit_Qty + 4),},
//}; 

//--end update 2024.6.6----------------------------------------------------------------------------*/
///===End Tx-FPGA_RawDatas=========================================================================================End Tx-FPGA_RawDatas====

///===Board_UartPorts======================================================================================================================
//--update 2024.3.14 14:45										 
__IO  uint16_t g_ins422_rx_precnt = 0, g_ins422_rx_curcnt = 0;
__IO  bool     g_ins422_rxtkn     = false;     // rx-token: be True if received a data
__IO  bool     g_ins422_rxtkn_ovr = false; //rx-token: be True if received a amount of datas over
uint8_t        g_ins422_rxbuff[INS_422_RxBuffSize] = {0}; // uart , USART_REC_LEN.

static int8_t    _r_dbcom_txbuff[G_DBCOM_SZ];

/**********************************************************************
  *\brief reset ins422`s rx datas
  *\param uart, uart peripheral
  *\param fmt, 
  *\return void
  *\notice
       -1-, #include "stdarg.h"	 	
  *\update
  *   -1.0-,(verified)2024.3.12
  *********************************************************************/
void app_ins422rx_rst(void)
{
	g_ins422_rxtkn     = false; //rx-token: be True if received a data
	g_ins422_rxtkn_ovr = false; //rx-token: be True if received a amount of datas over	
	memset(g_ins422_rxbuff,0,INS_422_RxBuffSize);
}
/**********************************************************************
  *\brief INS422_ARM-->UART4 app_ins422_printf
  *\param uart, uart peripheral
  *\param fmt, 
  *\return void
  *\notice
       -1-, #include "stdarg.h"	 	
  *\update
  *   -2.0-,(verified)2024.3.12
         _r_dbcom_txbuffptr->rxbuf
  *   -1.0-,(verified) 2022.7.6 created
  *********************************************************************/
void app_ins422_printf(const char* fmt,...)  
{  
	/*u16_t len; 
	memset(_r_dbcom_txbuff,0,G_DBCOM_SZ);
	
	va_list ap; 
	va_start(ap,fmt);	
	vsprintf((char*)_r_dbcom_txbuff,(c8_t*)fmt,ap);
	va_end(ap);
	len = (u16_t)strlen((const char*)_r_dbcom_txbuff) ;		//
	for(u16_t i = 0; i <= len; i++)
	{
    usart_data_transmit(PRP_INS422,_r_dbcom_txbuff[i]);
		while(RESET == usart_flag_get(PRP_INS422,USART_FLAG_TC))
		{ __NOP();}
	}*/
}

/**********************************************************************
  *\brief uart
  *\param uart, uart peripheral
  *\param fmt, 
  *\return void
  *\notice
       -1-, #include "stdarg.h"	 	
  *\update
  *   -2.0-,(verified)2024.3.12
         _r_dbcom_txbuffptr->rxbuf
  *   -1.0-,(verified) 2022.7.6 created
  *********************************************************************/
void app_free_printf(uint32_t huart,const char* fmt,...)  
{  
	/*u16_t len; 
	memset(_r_dbcom_txbuff,0,G_DBCOM_SZ);
	va_list ap; 
	va_start(ap,fmt);	
	vsprintf((char*)_r_dbcom_txbuff,(c8_t*)fmt,ap);
	va_end(ap);
	len = (u16_t)strlen((const char*)_r_dbcom_txbuff) ;		//
	for(u16_t i = 0; i <= len; i++)
	{
    usart_data_transmit(huart,_r_dbcom_txbuff[i]);
		while(RESET == usart_flag_get(huart,USART_FLAG_TC))
		{ __NOP();}
	}*/
}

///===End Board_UartPorts===========================================================================================End Board_UartPorts====

///===Board-LEDs===========================================================================================================================
/* private variables */
static uint32_t GPIO_PORT[LEDs_QTY] = {LED_sdARM_GPIO_PORT, LED_WheelSpeed_GPIO_PORT,
                                   LED_WorkStatus_GPIO_PORT};
static uint32_t GPIO_PIN[LEDs_QTY] = {LED_sdARM_GPIO_PIN, LED_WheelSpeed_GPIO_PIN, LED_WorkStatus_GPIO_PIN};

/***********************************************************************
    \brief      configure led GPIO
    \param[in]  lednum: specify the Led to be configured
      \arg        LED_sdARM
      \arg        LED_WheelSpeed
      \arg        LED_WorkStatus
    \param[out] none
    \retval     none
************************************************************************/
void pjt_led_init(led_typedef_enum lednum)
{
    //LED SRAM
    HPM_IOC->PAD[IOC_PAD_PA10].FUNC_CTL = IOC_PA10_FUNC_CTL_GPIO_A_10;
    HPM_IOC->PAD[IOC_PAD_PA10].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0) | IOC_PAD_PAD_CTL_MS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, GPIO_PIN[0], gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_PORT[0], GPIO_PIN[0]);
    gpio_write_pin(HPM_GPIO0, GPIO_PORT[0], GPIO_PIN[0],1);
	
    //Wheel LED
    HPM_IOC->PAD[IOC_PAD_PA24].FUNC_CTL = IOC_PA24_FUNC_CTL_GPIO_A_24;
    HPM_IOC->PAD[IOC_PAD_PA24].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, GPIO_PIN[1], gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_PORT[1], GPIO_PIN[1]);
    gpio_write_pin(HPM_GPIO0, GPIO_PORT[1], GPIO_PIN[1],1);
	
    //State LED
    HPM_IOC->PAD[IOC_PAD_PA23].FUNC_CTL = IOC_PA23_FUNC_CTL_GPIO_A_23;
    HPM_IOC->PAD[IOC_PAD_PA23].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, GPIO_PIN[2], gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_PORT[2], GPIO_PIN[2]);
    gpio_write_pin(HPM_GPIO0, GPIO_PORT[2], GPIO_PIN[2],1);


}

/***********************************************************************
	\brief      turn on selected led
	\param[in]  lednum: specify the Led to be turned on
		\arg        LED_sdARM
		\arg        LED_WheelSpeed
		\arg        LED_WorkStatus
	\param[out] none
	\retval     none
*************************************************
    GPIO_BOP(GPIO_PORT[lednum]) = GPIO_PIN[lednum];
    GPIO_BC(GPIO_PORT[lednum]) = GPIO_PIN[lednum];
************************************************************************/
void pjt_led_on(led_typedef_enum lednum)
{
    gpio_write_pin(HPM_GPIO0, GPIO_PORT[lednum], GPIO_PIN[lednum],0);
} 

/***********************************************************************
	\brief      turn off selected led
	\param[in]  lednum: specify the Led to be turned off
		\arg        LED_sdARM
		\arg        LED_WheelSpeed
		\arg        LED_WorkStatus
	\param[out] none
	\retval     none
*************************************************
    GPIO_BOP(GPIO_PORT[lednum]) = GPIO_PIN[lednum];
    GPIO_BC(GPIO_PORT[lednum]) = GPIO_PIN[lednum];
************************************************************************/
void pjt_led_off(led_typedef_enum lednum)
{
    gpio_write_pin(HPM_GPIO0, GPIO_PORT[lednum], GPIO_PIN[lednum],1);
}
/***********************************************************************
    \brief      toggle selected led
    \param[in]  lednum: specify the Led to be toggled
      \arg        LED_sdARM
      \arg        LED_WheelSpeed
      \arg        LED_WorkStatus
    \param[out] none
    \retval     none
************************************************************************/
void pjt_led_togg(led_typedef_enum lednum)
{
    gpio_toggle_pin(HPM_GPIO0, GPIO_PORT[lednum], GPIO_PIN[lednum]);
}

/***********************************************************************
    \brief      write Led to the specified GPIO pin
    \param[in]  gpio_periph: GPIO port
                only one parameter can be selected which is shown as below:
      \arg        GPIOx(x = A,B,C,D,E,F,G,H,I)
    \param[in]  pin: GPIO pin
                one or more parameters can be selected which are shown as below:
      \arg        GPIO_PIN_x(x=0..15), GPIO_PIN_ALL
    \param[in]  bit_value: SET or RESET
      \arg        RESET: clear the port pin
      \arg        SET: set the port pin
    \param[out] none
    \retval     none
************************************************************************/
//void pjt_led_output(led_typedef_enum lednum, bit_status bit_value)
//{
//	  gpio_bit_write(GPIO_PORT[lednum],GPIO_PIN[lednum], bit_value);
//} 
///===End Board-LEDs=====================================================================================================End Board-LEDs====


