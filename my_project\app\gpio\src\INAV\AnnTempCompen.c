/***********************************************************************************************************************************/
/*COMPENSATION.C                                                                                                                 */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*const.htypedefine.hmath.hDATASTRUCT.hEXTERNGLOBALDATA.hFUNCTION.hmemory.h                                          */
/*GNSSlocusGen.m                                                                  */
/*  加计陀螺神经网络（X,Y,Z三个轴）参数                      */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
//#include <math.h>
//#include <string.h>
#include <EXTERNGLOBALDATA.h>






/*********************************************函数说明*******************************************************/
/*函数名称：AccANNCompen_Z_Init                                                                             */
/*函数功能描述：Z加速度计神经网络初始化                                                                     */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：                                                                                                  */
/************************************************************************************************************/
/*void ANNCompen_Init()
{
    GyroANNCompen_X_Init(&g_GyroANNCompen_X,&g_GyroANNCompen.ANNCompen_X);
    GyroANNCompen_Y_Init(&g_GyroANNCompen_Y,&g_GyroANNCompen.ANNCompen_Y);
    GyroANNCompen_Z_Init(&g_GyroANNCompen_Z,&g_GyroANNCompen.ANNCompen_Z);

    AccANNCompen_X_Init(&g_AccANNCompen_X,&g_AccANNCompen.ANNCompen_X);
    AccANNCompen_Y_Init(&g_AccANNCompen_Y,&g_AccANNCompen.ANNCompen_Y);
    AccANNCompen_Z_Init(&g_AccANNCompen_Z,&g_AccANNCompen.ANNCompen_Z);
}*/




/**********************************************函数说明******************************************************/
/*????:GyroANNCompen_X_Init                                                                                */
/*??????:X?????????                                                                                        */
/*???:  Ver 0.1                                                                                            */
/*????/??:                                                                                                 */
/*???:                                                                                                     */
/*????:?                                                                                                   */
/*????:?                                                                                                   */
/*????:??                                                                                                  */
/*??1:                                                                                                     */
/*???:                                                                                                      */
/************************************************************************************************************/
/*void GyroANNCompen_X_Init(p_ANNCompen lp_GyroANNCompen_X,p_ANNCompen lp_ANNCompen)
{
    IPARA i;
    
    for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
    {
        lp_GyroANNCompen_X -> Dense1_Mat[i] = lp_ANNCompen -> Dense1_Mat[i];
    }
    for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
        lp_GyroANNCompen_X -> Dense1_Bias[i] = lp_ANNCompen -> Dense1_Bias[i];
    }
    
    for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
    {
        lp_GyroANNCompen_X -> Dense2_Mat[i] = lp_ANNCompen -> Dense2_Mat[i];
    }
    
    for(i = 0; i < DENSE_2_CELL_NUM;i++)
    {
        lp_GyroANNCompen_X -> Dense2_Bias[i] = lp_ANNCompen -> Dense2_Bias[i];
    }
    
    for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
    {
        lp_GyroANNCompen_X -> Dense3_Mat[i] =  lp_ANNCompen -> Dense3_Mat[i];
    }
    
    lp_GyroANNCompen_X -> Dense3_Bias = lp_ANNCompen -> Dense3_Bias;
    
    lp_GyroANNCompen_X -> Normalized_Temp_Max = lp_ANNCompen -> Normalized_Temp_Max;
    lp_GyroANNCompen_X -> Normalized_Temp_Min = lp_ANNCompen -> Normalized_Temp_Min;
    lp_GyroANNCompen_X -> Normalized_Temp_Mean = lp_ANNCompen -> Normalized_Temp_Mean;

    lp_GyroANNCompen_X -> Normalized_Temp_Diff_Max = lp_ANNCompen -> Normalized_Temp_Diff_Max;
    lp_GyroANNCompen_X -> Normalized_Temp_Diff_Min = lp_ANNCompen -> Normalized_Temp_Diff_Min;
    lp_GyroANNCompen_X -> Normalized_Temp_Diff_Mean = lp_ANNCompen -> Normalized_Temp_Diff_Mean;

    lp_GyroANNCompen_X -> Normalized_Output_Max = lp_ANNCompen -> Normalized_Output_Max;
    lp_GyroANNCompen_X -> Normalized_Output_Min = lp_ANNCompen -> Normalized_Output_Min;
    lp_GyroANNCompen_X -> Normalized_Output_Mean = lp_ANNCompen -> Normalized_Output_Mean;
    lp_GyroANNCompen_X -> Correct_Value = lp_ANNCompen -> Correct_Value;
}*/






/*********************************************函数说明*******************************************************/
/*????:GyroANNCompen_Y_Init                                                                            */
/*??????:Y?????????                                                                                     */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
/*void GyroANNCompen_Y_Init(p_ANNCompen lp_GyroANNCompen_Y,p_ANNCompen lp_ANNCompen)
{
    IPARA i;

    for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
    {
        lp_GyroANNCompen_Y -> Dense1_Mat[i] = lp_ANNCompen -> Dense1_Mat[i];
    }
    for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
        lp_GyroANNCompen_Y -> Dense1_Bias[i] = lp_ANNCompen -> Dense1_Bias[i];
    }

    for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
    {
        lp_GyroANNCompen_Y -> Dense2_Mat[i] = lp_ANNCompen -> Dense2_Mat[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM;i++)
    {
        lp_GyroANNCompen_Y -> Dense2_Bias[i] = lp_ANNCompen -> Dense2_Bias[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
    {
        lp_GyroANNCompen_Y -> Dense3_Mat[i] = lp_ANNCompen -> Dense3_Mat[i];
    }

    lp_GyroANNCompen_Y -> Dense3_Bias = lp_ANNCompen -> Dense3_Bias;

    lp_GyroANNCompen_Y -> Normalized_Temp_Max = lp_ANNCompen -> Normalized_Temp_Max;
    lp_GyroANNCompen_Y -> Normalized_Temp_Min = lp_ANNCompen -> Normalized_Temp_Min;
    lp_GyroANNCompen_Y -> Normalized_Temp_Mean = lp_ANNCompen -> Normalized_Temp_Mean;

    lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Max = lp_ANNCompen -> Normalized_Temp_Diff_Max;
    lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Min = lp_ANNCompen -> Normalized_Temp_Diff_Min;
    lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Mean = lp_ANNCompen -> Normalized_Temp_Diff_Mean;

    lp_GyroANNCompen_Y -> Normalized_Output_Max = lp_ANNCompen -> Normalized_Output_Max;
    lp_GyroANNCompen_Y -> Normalized_Output_Min = lp_ANNCompen -> Normalized_Output_Min;
    lp_GyroANNCompen_Y -> Normalized_Output_Mean = lp_ANNCompen -> Normalized_Output_Mean;
    lp_GyroANNCompen_Y -> Correct_Value = lp_ANNCompen -> Correct_Value;
}*/






/*********************************************函数说明*******************************************************/
/*????:GyroANNCompen_Z_Init                                                                            */
/*??????:Z?????????                                                                         */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
/*void GyroANNCompen_Z_Init(p_ANNCompen lp_GyroANNCompen_Z,p_ANNCompen lp_ANNCompen)
{
    IPARA i;

    for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
    {
            lp_GyroANNCompen_Z -> Dense1_Mat[i] = lp_ANNCompen -> Dense1_Mat[i];
    }
    for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
            lp_GyroANNCompen_Z -> Dense1_Bias[i] = lp_ANNCompen -> Dense1_Bias[i];
    }

    for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
    {
            lp_GyroANNCompen_Z -> Dense2_Mat[i] = lp_ANNCompen -> Dense2_Mat[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM;i++)
    {
            lp_GyroANNCompen_Z -> Dense2_Bias[i] = lp_ANNCompen -> Dense2_Bias[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
    {
            lp_GyroANNCompen_Z -> Dense3_Mat[i] = lp_ANNCompen -> Dense3_Mat[i];
    }

    lp_GyroANNCompen_Z -> Dense3_Bias = lp_ANNCompen -> Dense3_Bias;

    lp_GyroANNCompen_Z -> Normalized_Temp_Max = lp_ANNCompen -> Normalized_Temp_Max;
    lp_GyroANNCompen_Z -> Normalized_Temp_Min = lp_ANNCompen -> Normalized_Temp_Min;
    lp_GyroANNCompen_Z -> Normalized_Temp_Mean = lp_ANNCompen -> Normalized_Temp_Mean;

    lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Max = lp_ANNCompen -> Normalized_Temp_Diff_Max;
    lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Min = lp_ANNCompen -> Normalized_Temp_Diff_Min;
    lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Mean = lp_ANNCompen -> Normalized_Temp_Diff_Mean;

    lp_GyroANNCompen_Z -> Normalized_Output_Max = lp_ANNCompen -> Normalized_Output_Max;
    lp_GyroANNCompen_Z -> Normalized_Output_Min = lp_ANNCompen -> Normalized_Output_Min;
    lp_GyroANNCompen_Z -> Normalized_Output_Mean = lp_ANNCompen -> Normalized_Output_Mean;
    lp_GyroANNCompen_Z -> Correct_Value = lp_ANNCompen -> Correct_Value;
}*/





/*********************************************函数说明*******************************************************/
/*????:AccANNCompen_X_Init                                                                            */
/*??????:X???????????                                                                         */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
/*void AccANNCompen_X_Init(p_ANNCompen lp_AccANNCompen_X,p_ANNCompen lp_ANNCompen)
{
    IPARA i;

    for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
    {
            lp_AccANNCompen_X -> Dense1_Mat[i] = lp_ANNCompen -> Dense1_Mat[i];
    }
    for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
            lp_AccANNCompen_X -> Dense1_Bias[i] = lp_ANNCompen -> Dense1_Bias[i];
    }

    for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
    {
            lp_AccANNCompen_X -> Dense2_Mat[i] = lp_ANNCompen -> Dense2_Mat[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM;i++)
    {
            lp_AccANNCompen_X -> Dense2_Bias[i] = lp_ANNCompen -> Dense2_Bias[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
    {
            lp_AccANNCompen_X -> Dense3_Mat[i] = lp_ANNCompen -> Dense3_Mat[i];
    }

    lp_AccANNCompen_X -> Dense3_Bias = lp_ANNCompen -> Dense3_Bias;

    lp_AccANNCompen_X -> Normalized_Temp_Max = lp_ANNCompen -> Normalized_Temp_Max;
    lp_AccANNCompen_X -> Normalized_Temp_Min = lp_ANNCompen -> Normalized_Temp_Min;
    lp_AccANNCompen_X -> Normalized_Temp_Mean = lp_ANNCompen -> Normalized_Temp_Mean;

    lp_AccANNCompen_X -> Normalized_Temp_Diff_Max = lp_ANNCompen -> Normalized_Temp_Diff_Max;
    lp_AccANNCompen_X -> Normalized_Temp_Diff_Min = lp_ANNCompen -> Normalized_Temp_Diff_Min;
    lp_AccANNCompen_X -> Normalized_Temp_Diff_Mean = lp_ANNCompen -> Normalized_Temp_Diff_Mean;

    lp_AccANNCompen_X -> Normalized_Output_Max = lp_ANNCompen -> Normalized_Output_Max;
    lp_AccANNCompen_X -> Normalized_Output_Min = lp_ANNCompen -> Normalized_Output_Min;
    lp_AccANNCompen_X -> Normalized_Output_Mean = lp_ANNCompen -> Normalized_Output_Mean;
    lp_AccANNCompen_X -> Correct_Value = lp_ANNCompen -> Correct_Value;
}*/





/*********************************************?函数说明*******************************************************/
/*????:AccANNCompen_Y_Init                                                                            */
/*??????:Y???????????                                                                         */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
/*void AccANNCompen_Y_Init(p_ANNCompen lp_AccANNCompen_Y,p_ANNCompen lp_ANNCompen)
{
    IPARA i;
    
    
    
    
    for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
    {
            lp_AccANNCompen_Y -> Dense1_Mat[i] = lp_ANNCompen -> Dense1_Mat[i];
    }
    for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
            lp_AccANNCompen_Y -> Dense1_Bias[i] = lp_ANNCompen -> Dense1_Bias[i];
    }
    
    for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
    {
            lp_AccANNCompen_Y -> Dense2_Mat[i] = lp_ANNCompen -> Dense2_Mat[i];
    }
    
    for(i = 0; i < DENSE_2_CELL_NUM;i++)
    {
            lp_AccANNCompen_Y -> Dense2_Bias[i] = lp_ANNCompen -> Dense2_Bias[i];
    }
    
    for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
    {
            lp_AccANNCompen_Y -> Dense3_Mat[i] = lp_ANNCompen -> Dense3_Mat[i];
    }
    
    lp_AccANNCompen_Y -> Dense3_Bias = lp_ANNCompen -> Dense3_Bias;
    
    lp_AccANNCompen_Y -> Normalized_Temp_Max = lp_ANNCompen -> Normalized_Temp_Max;
    lp_AccANNCompen_Y -> Normalized_Temp_Min = lp_ANNCompen -> Normalized_Temp_Min;
    lp_AccANNCompen_Y -> Normalized_Temp_Mean = lp_ANNCompen -> Normalized_Temp_Mean;

    lp_AccANNCompen_Y -> Normalized_Temp_Diff_Max = lp_ANNCompen -> Normalized_Temp_Diff_Max;
    lp_AccANNCompen_Y -> Normalized_Temp_Diff_Min = lp_ANNCompen -> Normalized_Temp_Diff_Min;
    lp_AccANNCompen_Y -> Normalized_Temp_Diff_Mean = lp_ANNCompen -> Normalized_Temp_Diff_Mean;

    lp_AccANNCompen_Y -> Normalized_Output_Max = lp_ANNCompen -> Normalized_Output_Max;
    lp_AccANNCompen_Y -> Normalized_Output_Min = lp_ANNCompen -> Normalized_Output_Min;
    lp_AccANNCompen_Y -> Normalized_Output_Mean = lp_ANNCompen -> Normalized_Output_Mean;
    lp_AccANNCompen_Y -> Correct_Value = lp_ANNCompen -> Correct_Value;
}*/






/*********************************************函数说明*******************************************************/
/*????:AccANNCompen_Z_Init                                                                             */
/*??????:Z???????????                                                                     */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
/*void AccANNCompen_Z_Init(p_ANNCompen lp_AccANNCompen_Z,p_ANNCompen lp_ANNCompen)
{
    IPARA i;


    for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
    {
            lp_AccANNCompen_Z -> Dense1_Mat[i] = lp_ANNCompen -> Dense1_Mat[i];
    }
    for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
            lp_AccANNCompen_Z -> Dense1_Bias[i] = lp_ANNCompen -> Dense1_Bias[i];
    }

    for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
    {
            lp_AccANNCompen_Z -> Dense2_Mat[i] = lp_ANNCompen -> Dense2_Mat[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM;i++)
    {
            lp_AccANNCompen_Z -> Dense2_Bias[i] = lp_ANNCompen -> Dense2_Bias[i];
    }

    for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
    {
            lp_AccANNCompen_Z -> Dense3_Mat[i] = lp_ANNCompen -> Dense3_Mat[i];
    }

    lp_AccANNCompen_Z -> Dense3_Bias = lp_ANNCompen -> Dense3_Bias;

    lp_AccANNCompen_Z -> Normalized_Temp_Max = lp_ANNCompen -> Normalized_Temp_Max;
    lp_AccANNCompen_Z -> Normalized_Temp_Min = lp_ANNCompen -> Normalized_Temp_Min;
    lp_AccANNCompen_Z -> Normalized_Temp_Mean = lp_ANNCompen -> Normalized_Temp_Mean;

    lp_AccANNCompen_Z -> Normalized_Temp_Diff_Max = lp_ANNCompen -> Normalized_Temp_Diff_Max;
    lp_AccANNCompen_Z -> Normalized_Temp_Diff_Min = lp_ANNCompen -> Normalized_Temp_Diff_Min;
    lp_AccANNCompen_Z -> Normalized_Temp_Diff_Mean = lp_ANNCompen -> Normalized_Temp_Diff_Mean;

    lp_AccANNCompen_Z -> Normalized_Output_Max = lp_ANNCompen -> Normalized_Output_Max;
    lp_AccANNCompen_Z -> Normalized_Output_Min = lp_ANNCompen -> Normalized_Output_Min;
    lp_AccANNCompen_Z -> Normalized_Output_Mean = lp_ANNCompen -> Normalized_Output_Mean;
    lp_AccANNCompen_Z -> Correct_Value = lp_ANNCompen -> Correct_Value;
}*/






/*********************************************函数说明*******************************************************/
/*函数名：ANN_Predcit                                                                                       */
/*函数功能描述：神经网络预测                                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：无                                                                                              */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：                                                                                                  */
/************************************************************************************************************/
DPARA ANN_Predict(DPARA Temp,DPARA Temp_Diff,p_ANNCompen lp_ANNCompen)
{
    IPARA i;
    DPARA Output1[DENSE_1_CELL_NUM] = {0};
    DPARA Output2[DENSE_2_CELL_NUM] = {0};
    DPARA Output3 = 0.0;
    DPARA RT_Bias;
    //DPARA Output4 = 0.0;

    DPARA Normalized_Temp, Normalized_Temp_Diff;
    DPARA Input1[2];
    //输入归一化
    Normalized_Temp = (Temp - lp_ANNCompen -> Normalized_Temp_Min) / (lp_ANNCompen -> Normalized_Temp_Max - lp_ANNCompen -> Normalized_Temp_Min);
    Normalized_Temp = Normalized_Temp - lp_ANNCompen -> Normalized_Temp_Mean;

    Normalized_Temp_Diff = (Temp_Diff - lp_ANNCompen->Normalized_Temp_Diff_Min) / (lp_ANNCompen->Normalized_Temp_Diff_Max - lp_ANNCompen->Normalized_Temp_Diff_Min);
    Normalized_Temp_Diff = Normalized_Temp_Diff - lp_ANNCompen->Normalized_Temp_Diff_Mean;

    Input1[0] = Normalized_Temp;
    Input1[1] = Normalized_Temp_Diff;
    //第一隐藏层
    /*for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
        Output1[i] = Normalized_Temp * lp_ANNCompen -> Dense1_Mat[i] + lp_ANNCompen -> Dense1_Bias[i];
    }*/
    Mat_Mul(Input1, (double *)lp_ANNCompen -> Dense1_Mat, Output1, 1, INPUT_DIM, DENSE_1_CELL_NUM);
    for (i = 0; i < DENSE_1_CELL_NUM; i++)
    {
        Output1[i] += lp_ANNCompen->Dense1_Bias[i];
    }
    Relu(Output1, DENSE_1_CELL_NUM);
    //第二隐藏层
    Mat_Mul(Output1, (double *)lp_ANNCompen -> Dense2_Mat, Output2, 1,DENSE_1_CELL_NUM, DENSE_2_CELL_NUM);
    for(i = 0;i < DENSE_2_CELL_NUM;i++)
    {
        Output2[i] += lp_ANNCompen -> Dense2_Bias[i];
    }
    Relu(Output2, DENSE_2_CELL_NUM);
    //输出层
    MultiDim_Vec_Dot(Output2, (double *)lp_ANNCompen -> Dense3_Mat, &Output3,DENSE_2_CELL_NUM);
    Output3 = Output3 + lp_ANNCompen -> Dense3_Bias;
    RT_Bias = (Output3 + lp_ANNCompen -> Normalized_Output_Mean) * (lp_ANNCompen -> Normalized_Output_Max - lp_ANNCompen -> Normalized_Output_Min) + lp_ANNCompen -> Normalized_Output_Min;
    RT_Bias = RT_Bias - lp_ANNCompen -> Correct_Value;
    return RT_Bias;
}

