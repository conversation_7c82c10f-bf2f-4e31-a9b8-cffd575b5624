//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：uart.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.16
//---------------------------------------------------------

#include "uart.h"
#include "SetParaBao.h"
#include "uart_dma.h"

ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4)
static uint8_t test_uart_rx_buf[U4RX_MAXCOUNT * 4];
ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4)
static uint8_t test_uart_tx_buf[U4RX_MAXCOUNT * 2];
ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4)
bool test_uart_tx_done;
ATTR_PLACE_AT_NONCACHEABLE_INIT_WITH_ALIGNMENT(8)
static dma_linked_descriptor_t test_tx_descriptors[2];
ATTR_PLACE_AT_NONCACHEABLE_INIT_WITH_ALIGNMENT(8)
static dma_linked_descriptor_t test_rx_descriptors[2];

ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4)
static uint8_t sd_uart_rx_buf[SDRX_MAXCOUNT * 4];
ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4)
static uint8_t sd_uart_tx_buf[SDRX_MAXCOUNT * 2];
ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4)
bool sd_uart_tx_done;
ATTR_PLACE_AT_NONCACHEABLE_INIT_WITH_ALIGNMENT(8)
static dma_linked_descriptor_t sd_tx_descriptors[2];
ATTR_PLACE_AT_NONCACHEABLE_INIT_WITH_ALIGNMENT(8)
static dma_linked_descriptor_t sd_rx_descriptors[2];

static uart_dma_ctx_t test_uart =
    {
        .uart_base = TEST_UART,
        .uart_src_freq = 0,
        .baudrate = 115200,
        .tx_done = &test_uart_tx_done,
        .core_id = HPM_CORE0,
        .tx_buff = test_uart_tx_buf,
        .tx_size = sizeof(test_uart_tx_buf),
        .rx_buff = test_uart_rx_buf,
        .rx_size = sizeof(test_uart_rx_buf),
        .old_rxlen = 0,
        .dmamux_base = TEST_UART_DMAMUX_CONTROLLER,
        .dmairq = TEST_UART_DMA_IRQ,
        .dmamux_tx_src = TEST_UART_TX_DMA_REQ,
        .dmamux_rx_src = TEST_UART_RX_DMA_REQ,
        .dma_base = TEST_UART_DMA_CONTROLLER,
        .dma_tx_ch = TEST_UART_TX_DMA_CHN,
        .dma_rx_ch = TEST_UART_RX_DMA_CHN,
        .tx_descriptor = test_tx_descriptors,
        .rx_descriptor = test_rx_descriptors,
};

static uart_dma_ctx_t sd_uart =
    {
        .uart_base = SD_UART,
        .uart_src_freq = 0,
        .baudrate = 115200,
        .tx_done = &sd_uart_tx_done,
        .core_id = HPM_CORE0,
        .tx_buff = sd_uart_tx_buf,
        .tx_size = sizeof(sd_uart_tx_buf),
        .rx_buff = sd_uart_rx_buf,
        .rx_size = sizeof(sd_uart_rx_buf),
        .old_rxlen = 0,
        .dmamux_base = SD_UART_DMAMUX_CONTROLLER,
        .dmairq = SD_UART_DMA_IRQ,
        .dmamux_tx_src = SD_UART_TX_DMA_REQ,
        .dmamux_rx_src = SD_UART_RX_DMA_REQ,
        .dma_base = SD_UART_DMA_CONTROLLER,
        .dma_tx_ch = SD_UART_TX_DMA_CHN,
        .dma_rx_ch = SD_UART_RX_DMA_CHN,
        .tx_descriptor = sd_tx_descriptors,
        .rx_descriptor = sd_rx_descriptors,
};

uint8_t g_Com3WriteSdFlag = 0; // 写入SD卡文件COM3 二进制数据标志
uint8_t grxbuffer[U4RX_MAXCOUNT+2] = {0};
uint8_t SD_grxbuffer[SDRX_MAXCOUNT+2] = {0};
int grxlen = 0, grxst = 0;

void UartIrqSendMsg(char *txbuf, int size)
{
    if(txbuf == NULL || size <= 0)
    {
        return;
    }
    
    while (!get_uart_tx_idle((void *)&test_uart))
    {
        printf("1\r\n");
    }
    memcpy(test_uart.tx_buff, txbuf, size);
    if (uart_dma_output((void *)&test_uart, test_uart.tx_buff, size) < 0)
    {
        printf("test uart tx send failed!\r\n");
    }
}

void SDUartIrqSendMsg(char *txbuf, int size)
{
    if(txbuf == NULL || size <= 0)
    {
        return;
    }
    while (!get_uart_tx_idle((void *)&sd_uart))
    {
        printf("2\r\n");
    }
    memcpy(sd_uart.tx_buff, txbuf, size);
    if (uart_dma_output((void *)&sd_uart, sd_uart.tx_buff, size) < 0)
    {
        printf("sd uart tx send failed!\r\n");
    }
}

static void test_uart_recv_polling(void)
{
    uint32_t offset = (grxst + grxlen) % U4RX_MAXCOUNT;
    uint32_t len = uart_dma_recv_polling((void *)&test_uart, &grxbuffer[offset], U4RX_MAXCOUNT - offset);
    if (len == 0)
    {
        return;
    }
    grxlen += (int)len;
    //printf("grxst:%d, grxlen:%d, len:%d, max:%d, offset:%d, size:%d\r\n",grxst, grxlen, len, U4RX_MAXCOUNT, offset, U4RX_MAXCOUNT - offset);
}

unsigned char gframeParsebuf[1024];
void analysisRxdata(void)
{
    test_uart_recv_polling();
    int i, j, bfind2 = 0;
    int rxlenbx = grxlen, isbkexit = 0;

    for (i = 0; i < grxlen; i++)
    {
        if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == 0xAF && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 0x55 && grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == 0xFA)
        {
            rxlenbx = (int)((grxbuffer[(grxst + 6) % U4RX_MAXCOUNT] << 8) + grxbuffer[(grxst + 5) % U4RX_MAXCOUNT]);
            gframeParsebuf[0] = 0xaf;
            gframeParsebuf[1] = 0x55;
            gframeParsebuf[2] = 0xfa;
            for (j = i + 3; j < grxlen; j++)
            {
                gframeParsebuf[j - i] = grxbuffer[(grxst + j) % U4RX_MAXCOUNT];
                if (grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xAF && grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT] == 0x55 && grxbuffer[(grxst + j + 2) % U4RX_MAXCOUNT] == 0xFA)
                {
                    bfind2 = 1;
                    break;
                }

                if (j == rxlenbx - 1 && grxbuffer[(grxst + j - 1) % U4RX_MAXCOUNT] == 0x00 && grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xff) // 新协议 帧尾 0x00 0xFF
                {
                    bfind2 = 1;
                    j++;
                    break;
                }
            }

            if (bfind2 == 0)
                break;

            grxst = (grxst + j) % U4RX_MAXCOUNT;
            grxlen -= j;
            isbkexit = 0;
            break;
        }
    }

    if (bfind2)
    {
        UartDmaRecSetPara((p_dmauart_t)gframeParsebuf);
    }
}

void sduart_recv_polling(void)
{
    static uint32_t uiLen = 0;
    uint32_t len = uart_dma_recv_polling((void *)&sd_uart, &hello_str[uiLen], SDRX_MAXCOUNT - uiLen);
    if (len == 0)
    {
        return;
    }
    uiLen += len;
    if (uiLen >= 1000)
    {
        uiLen = 0;
        g_Com3WriteSdFlag = 1;
    }
}

void UartIrqInit(void)
{
    board_init_uart(TEST_UART);
    test_uart.uart_src_freq = clock_get_frequency(TEST_UART_CLK_NAME);
    test_uart.baudrate = stSetPara.Setbaud * 100;

    if (uart_dma_init((void *)&test_uart) != 0)
    {
        printf("BAD! test uart dma init fail!\r\n");
        while (1)
            ;
    }
}

void SDUartIrqInit(void)
{
    board_init_uart(SD_UART);
    sd_uart.uart_src_freq = clock_get_frequency(SD_UART_CLK_NAME);
    sd_uart.baudrate = 19200;

    if (uart_dma_init((void *)&sd_uart) != 0)
    {
        printf("BAD! sd uart dma init fail!\r\n");
        while (1)
            ;
    }
}
