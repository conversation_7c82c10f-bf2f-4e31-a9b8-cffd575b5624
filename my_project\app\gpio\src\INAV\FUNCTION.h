#ifndef _FUNCTION_H
#define _FUNCTION_H
#include "appmain.h"
//#include "TYPEDEFINE.h"
#include "DATASTRUCT.h"
#include "ins.h"
//function.h
/*********************************************:MAIN.C*************************************************/
void SysInit(void);       //系统初始化（封装）函数
BOOL HardWare_InitSet(void);//硬件初始设置函数
void Bind_Init(p_InitBind lp_InitBind);
//BOOL Sys_Self_Test(DRAW const GyroRaw[3], DRAW const AccRaw[3], DRAW const GyroTempRaw[3], DRAW const AccTempRaw[3]);  //系统自检函数
void SysVarDefaultSet(p_SysVar lp_SysVar);
void BindDefaultSet(p_InitBind lp_InitBind);
void BindDefaultSet_by_GNSS(p_InitBind lp_InitBind,p_GNSSData lp_GNSSData);
//void Self_Test(p_SelfTest lp_SelfTest,IMU_DATA70* lp_Imu_Raw);
void GPS_Simu(void);
void ComputeVG(p_IMUSmoothAverage lp_IMUSmoothAverage);
void ComputeVGSmooth(p_IMUSmoothAverage lp_IMUSmoothAverage);
/*********************************************GNSS数据读取与处理声明:（函数体定义文件：read_and_check_gnss_data.C）****************************************/
void Read_And_Check_GNSS_Data(p_GNSSData lp_GNSSData,PAOCHE_FRAME_STRUCT* lp_paochedata,p_Navi lp_Navi,p_SysVar lp_SysVar);
void SaveGNSSData(p_GNSSData lp_GNSSData,PAOCHE_FRAME_STRUCT* lp_paochedata,p_Navi lp_Navi,p_SysVar lp_SysVar);//
void GNSSAndHeadDataTest(p_GNSSData lp_GNSSData,p_Navi lp_Navi);
/****************************************矩阵、向量运算数学函数声明:（函数体定义文件：MATVECMATH.C）****************************************/
void Vec_Cross(VEC const InVecA[3], VEC const InVecB[3], VEC OutVecC[3]);//向量叉乘函数
void Vec_Dot(VEC const InVecA[3], VEC const InVecB[3], SCAL *OutVecC);//向量点乘函数
void MultiDim_Vec_Dot(VEC *InVecA, VEC *InVecB, SCAL *OutVecC,IPARA LengthVec);
void Mat_Mul(MATR *InMatA, MATR *InMatB, MATR *OutMatC, IPARA RowA, IPARA ColA, IPARA ColB);//矩阵乘法函数
void Mat_Tr(MATR *InMatA, MATR *OutMatB, IPARA RowA, IPARA ColA);//矩阵转置函数
void Mat_Inv(MATR *InMatA, MATR *OutMatB,IPARA Order);//矩阵求逆运算函数
void Qua_Mul(QUAT InQuaA[4], QUAT InQuaB[4], QUAT OutQuaC[4]);//四元数乘法函数
BOOL LinerLeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[2]);//线性最小二乘拟合函数
BOOL Ord2LeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[3]);//
BOOL Ord3LeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[4]);
BOOL Ord4LeastSquareFit(DPARA *lp_Xaxis,DPARA *lp_Yaxis,IPARA DotNum, DPARA Coef[5]);
BOOL PolyVal(DPARA *lp_Coef, DPARA InVar, IPARA order, DPARA *lp_OutVal);
void Relu(DPARA *InArrayA , IPARA LengthArrayA);
DPARA DoubleMax3(DPARA Input[3]);
UINT32 Uint32Max3(UINT32 Input[3]);
//IIR滤波器相关函数
void IIR_Data_Input_buffer(DPARA InputBuffer[NUM_B],DPARA LastInput);
void IIR_Data_Output_buffer(DPARA OutputBuffer[NUM_A],DPARA LastOutput);
DPARA IIR_Filter(DPARA InputBuffer[NUM_B],DPARA OutputBuffer[NUM_A],DPARA IIR_a[NUM_A] ,DPARA IIR_b[NUM_B]);
/****************************************标定参数补偿函数声明:（函数体定义文件：COMPENSATION.C）*******************************************/
void GetTempRangeNum(VEC Temp[3],MATR TempSamPoint[NUM_TEMP_SAM][3],IPARA TempRangeNum[3]);
void ComputeGyroTempDiff(p_Compen lp_Compen);
void ComputeAccTempDiff(p_Compen lp_Compen);
void RTCompenPara(MATR Para[NUM_TEMP_SAM][3],VEC Temp[3],IPARA TempRangeNum[3],MATR TempSamPoint[NUM_TEMP_SAM][3],VEC RTPara[3]);
//void RTCompenPara(DRAW Para[3 * NUM_TEMP_SAM],VEC Temp[3],IPARA TempRangeNum[3],VEC TempSamPoint[NUM_TEMP_SAM],DRAW RTPara[3]);
//void LinerCompen(DRAW Raw[3],DRAW RTBias[3],DPARA RTScalFac[3],DPARA InstErr[9],DPARA CompenData[3]);
void ComputeLeverArmAcc(DPARA Gyro[3],MATR AccLeverArm[9],DPARA Acc_r[3]);

void InerLeverArmCompen(DPARA Gyro[3],DPARA Acc[3],MATR AccLeverArm[9]);
//新的适应光纤陀螺以及ARM系统的补偿函数
//void GyroCompenCompute(p_Compen lp_Compen,p_CmdNormalTempCompen lp_GyroNormalTempCompen,p_CmdFullTempCompen lp_GyroFullTempCompen,p_CmdANNCompenData lp_GyroANNCompenData);
//void AccCompenCompute(p_Compen lp_Compen,p_CmdNormalTempCompen lp_AccNormalTempCompen,p_CmdFullTempCompen lp_AccFullTempCompen,p_CmdANNCompenData lp_AccANNCompenData);
void TempSensCompen_60(DPARA  TempRaw[3], DPARA Temp[3]);
void LinerCompen_60(INT32 Raw[3],DPARA RTBias[3],DPARA RTScalFac[3],MATR InstErr[9],DPARA CompenData[3]);
void LinerCompen_60_ANN_Order(DPARA Raw[3],DPARA RTBias[3],DPARA RTScalFac[3],MATR InstErr[9],DPARA CompenData[3]);
void GyroCompenDatabuffer_60(DPARA d_Gyro[3],const p_Compen lp_Compen);
void AccCompenDatabuffer_60(DPARA Acc[3],const p_Compen lp_Compen);
void ComputeAverageValFromBuffer_60(const p_Compen lp_Compen);
void ComputeAccAverageValFromBuffer_60(const p_Compen lp_Compen);
void ComputeGyroAverageValFromBuffer_60(const p_Compen lp_Compen);
void ComputeColdStartCompenVal(TIME Time, DPARA Para[2],DPARA *Output);
void ComputeLeverArmSn(MATR Cnb[9],LEN Sb_r[3],LEN Sn_r[3]);
/****************************************初始对准运算函数声明:（函数体定义文件：ALIGN.C）*******************************************************/
void SteadyStateCorseAlignAccum(ANGRATE const Gyro[3], ACCELER const Acc[3],p_Align lp_Align);
void ComputeSelfAlignResult(LATI r_Lati,p_Align lp_Align);
void ComputeAlignBindFai(ATTI r_fai,p_Align lp_Align);
//以下为惯性凝固坐标系初始对准相关函数
void InertialSysAlign_Init(p_InitBind const lp_InitBind, p_InertialSysAlign lp_InertialSysAlign);
void InertialSysAlignCompute(DPARA const Gyro[3],DPARA const LastGyro[3], DPARA const Acc[3],DPARA const LastAcc[3] ,p_InertialSysAlign lp_InertialSysAlign);
void ComputeCen(LATI r_Lati, LOGI r_Logi, MATR Cen[9]);
void ComputeCie(TIME AlignTime , MATR Cie[9]);
void ComputeVi(ACCELER Gn,MATR Cie[9],MATR Cen[9],VEL Vi[3]);
void ComputeVib0(ACCELER Fibb[3],MATR Cbib0[9],VEL Vib0[3]);
void ComputeCib0i(VEL Vi[3],VEL Vib0[3], VEL Vi_T1[3],VEL Vib0_T1[3],MATR Cib0i[9]);
void FinishInertialSysAlign(p_InertialSysAlign lp_InertialSysAlign);
void VGDynamicInertialSysAlign(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
/****************************************动态初始对准运算函数声明:（函数体定义文件：DYNAMIC_ALIGN.C）*******************************************************/
void DynamicInertialSysAlign_Init(DPARA r_Pos[3], DPARA Vn[3],p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
void DynamicInertialSysAlignCompute(DPARA const Gyro[3],DPARA const LastGyro[3],DPARA const Acc[3],DPARA const LastAcc[3],DPARA r_Pos[3], DPARA Vn[3], p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
void UpdateAlignPosAndVn(DPARA r_Pos[3], DPARA Vn[3], p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
void ComputeSi(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
void ComputeSib0(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
void FinishDynamicInertialSysAlign(p_DynamicInertialSysAlign lp_DynamicInertialSysAlign);
/****************************************惯导计算函数声明:（函数体定义文件：Navi.C）*******************************************************/
//void Navi_Init(p_InitBind const lp_InitBind, p_Align const lp_Align, p_Navi lp_Navi);//导航结构体初始化函数
void Navi_Init(p_InitBind const lp_InitBind, p_InertialSysAlign const lp_InertialSysAlign, p_Navi lp_Navi);
void DynamicNavi_Init(p_GNSSData lp_GNSSData, p_DynamicInertialSysAlign lp_DynamicInertialSysAlign, p_Navi lp_Navi);
void NaviCompute(DPARA const Gyro[3],DPARA const LastGyro[3],DPARA const Acc[3],DPARA const LastAcc[3],p_Navi lp_Navi);
void ComputeWien(LATI r_Lati, ANGRATE r_Wien[3]);
void ComputeRmRn(LATI r_Lati, HEIGHT Height, LEN *lp_Rm, LEN *lp_Rn, DPARA *lp_invRm, DPARA *lp_invRn);
void ComputeWenn(LATI r_Lati, VEL const Vn[3], DPARA invRm, DPARA invRn, ANGRATE r_Wenn[3]);
void ComputeWnbb(ANGRATE r_Wibb[2][3], ANGRATE const r_Wien[3], ANGRATE const r_Wenn[3], MATR Cnb[9],ANGRATE r_Wnbb[2][3]);
void ComputeDelSenbb(ANGRATE r_Wnbb[2][3],DELANG r_DelSenbb_1[3],DELANG r_DelSenbb_2[3],DELANG r_DelSenbb[3]);
void ComputeQ(DELANG r_DelSenbb[3], QUAT Q[4]);
void QToCnb(QUAT const Q[4], MATR Cnb[9]);//姿态四元数转换为姿态矩阵函数
void CnbToQ(MATR Cnb[9],QUAT Q[4]);
void AttiToCnb(ATTI const r_Atti[3], MATR Cnb[9]);//姿态角转换为姿态矩阵函数
void CnbToAtti(MATR const Cnb[9], ATTI r_Atti[3]);
void ComputeG(LATI r_Lati, HEIGHT Height, ACCELER *lp_Gn);
void ComputeAttiRate(ANGRATE r_Wnbb[2][3], ATTI const r_Atti[3], ANGRATE r_AttiRate[3]);
void ComputePos(VEL const LastVn[3],VEL const Vn[3], DPARA invRm, DPARA invRn, LATI *lp_Lati, LOGI *lp_Logi, HEIGHT *lp_Height,LEN Sn[3]);
void ComputeVb(VEL const Vn[3], MATR const Cnb[9], VEL Vb[3]);
void ComputeVibn(p_Navi lp_Navi);
void ComputeVn(p_Navi lp_Navi);

void VnDapmed_PureINS(p_Navi lp_Navi);

void ComputeDeg_Ex(p_Navi lp_Navi);//计算用度表示的惯导输出参数
void ComputeTrackAtti(VEL GPSVn[3], ATTI *r_TrackAtti);//计算航迹姿态角
void TransHeading0to360(p_Navi lp_Navi);//将正负180°，北偏西为正的航向角转换为0~360°，顺时针旋转为正
void ComputeLeverArmSn(MATR Cnb[9],LEN Sb_r[3],LEN Sn_r[3]);

void AttiToCpb(ATTI const r_Atti[3], MATR Cpb[9]);
void ComputeVp(VEL const Vb[3], MATR const Cpb[9], VEL Vp[3]);
void DampedVp(VEL Vp[3],VEL Vb[3],MATR const Cpb[9],MATR const Cnb[9],VEL Vn[3]);
void ComputeLeverArmVn(MATR Cnb[9],DPARA r_Webb[3],VEC VnLeverArm[3],VEL Vn_r[3]);
/*********************************************Kalman滤波函数声明:（函数体定义文件：Kalman.C）***********************************************/
void Kalman_StartUp(p_Kalman lp_Kalman,p_GNSSData lp_GNSSData,p_SysVar lp_SysVar,p_Navi lp_Navi);
void Kalman_Init(p_Kalman lp_Kalman);
void Pk_Init(MATR Pk[DIM_STATE * DIM_STATE]);
void Qk_Init(MATR Qk[DIM_STATE * DIM_STATE]);
void Rk_Init(MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV],MODE Obv_Fit_Mode);
void Hk_Init(MATR Hk[DIM_MAX_OBV * DIM_STATE],MODE Obv_Fit_Mode);
void KalCompute(p_GNSSData lp_GNSSData, p_Kalman lp_Kalman);
void KalPredict(p_Kalman lp_Kalman);
void ComputeFn(p_Navi const lp_Navi, MATR Fn[DIM_STATE * DIM_STATE]);
void ComputeFk(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE]);
void ComputeFk_2(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE]);
void ComputeXkk_1(MATR const Fk[DIM_STATE * DIM_STATE], VEC const Xk[DIM_STATE],VEC Xkk_1[DIM_STATE]);
void ComputePkk_1_Step1(p_Kalman lp_Kalman);
void ComputePkk_1_Step2(p_Kalman lp_Kalman);
//void ComputePkk_1(MATR Fk[DIM_STATE * DIM_STATE], MATR Pk[DIM_STATE * DIM_STATE],MATR const Qk[DIM_STATE * DIM_STATE],MATR Pkk_1[DIM_STATE * DIM_STATE]);
void ComputeKk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV]);
void ComputePk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Kk[DIM_STATE * DIM_MAX_OBV], MATR Pk[DIM_STATE * DIM_STATE]);
void ComputeZk(p_GNSSData const lp_GNSSData, p_Kalman lp_Kalman,MODE Obv_Fit_Mode);
void ComputeXk(VEC Xkk_1[DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], VEC const Zk[DIM_MAX_OBV],MATR Kk[DIM_STATE * DIM_MAX_OBV],VEC Xk[DIM_STATE]);
void SaveINSData(p_Navi const lp_Navi, p_Kalman lp_Kalman);
void CorrectPos(LATI *lp_InsLati, LOGI *lp_InsLogi, HEIGHT *lp_InsHeight, VEC PosErr[3]);
void CorrectVn(VEC InsVn[3], VEC VnErr[3]);
void CorrectAtti(QUAT Q[4], VEC AttiErr[3]);
void CorrectGyroBias(ANGRATE GyroBias[3], ANGRATE EstGyroBias[3]);
void CorrectAccBias(ACCELER AccBias[3], ACCELER EstAccBias[3]);
void CorrectGyroScaleFacErr(DPARA GyroScaleFacErr[3], DPARA EstErr[3]);
void CorrectAccScaleFacErr(DPARA AccScaleFacErr[3], DPARA EstErr[3]);
void ErrCorrect(p_Navi lp_Navi, p_Kalman lp_Kalman, p_GNSSData lp_GNSSData);
void ComputeKkTest(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR const Rk[DIM_MAX_OBV * DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV]);
void ComputePkPosDef(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Kk[DIM_STATE * DIM_MAX_OBV],MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV],MATR Pk[DIM_STATE * DIM_STATE]);
void ErrStore_1s(p_Navi lp_Navi, p_Kalman lp_Kalman, p_GNSSData lp_GNSSData);
void ErrCorrect_1_Navi_Time(p_Navi lp_Navi, p_Kalman lp_Kalman);
void ErrStore_1s_For_GNSS(p_Navi lp_Navi, p_Kalman lp_Kalman);
void ErrCorrect_1_Navi_Time_For_INS(p_Navi lp_Navi, p_Kalman lp_Kalman);
/*********************************************人工神经网络相关函数声明:（函数体定义文件：AnnTempCompen.C）***********************************************/
//void ANNCompen_Init(void);
//void GyroANNCompen_X_Init(p_ANNCompen lp_GyroANNCompen_X);
//void GyroANNCompen_Y_Init(p_ANNCompen lp_GyroANNCompen_Y);
//void GyroANNCompen_Z_Init(p_ANNCompen lp_GyroANNCompen_Z);

//void AccANNCompen_X_Init(p_ANNCompen lp_AccANNCompen_X);
//void AccANNCompen_Y_Init(p_ANNCompen lp_AccANNCompen_Y);
//void AccANNCompen_Z_Init(p_ANNCompen lp_AccANNCompen_Z);
DPARA ANN_Predict(DPARA Temp,DPARA Temp_Diff,p_ANNCompen lp_ANNCompen);
void ComputeLeverArmSn(MATR Cnb[9],LEN Sb_r[3],LEN Sn_r[3]);
#endif
