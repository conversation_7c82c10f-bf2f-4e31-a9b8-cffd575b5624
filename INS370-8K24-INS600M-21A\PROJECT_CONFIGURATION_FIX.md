# Keil项目配置修正指南

## 问题诊断

通过编译输出分析发现了关键问题：

### ❌ **主要问题1：INAV算法文件未包含在项目中**
编译器没有尝试编译任何INAV算法文件：
- ❌ navi.c - 惯性导航核心算法
- ❌ kalman.c - Kalman滤波算法  
- ❌ align.c - 初始对准算法
- ❌ ins.c - INS系统接口
- ❌ ahrs.c - 姿态航向参考系统
- ❌ 其他20个INAV算法文件

### ❌ **主要问题2：不存在的包装器文件仍在项目中**
编译器尝试编译已删除的包装器文件：
- ❌ nav.c (已删除，但项目仍引用)
- ❌ nav_app.c (已删除，但项目仍引用)
- ❌ nav_kf.c (已删除，但项目仍引用)
- ❌ nav_math.c (已删除，但项目仍引用)
- ❌ transplant.c (已删除，但项目仍引用)
- ❌ 其他8个已删除的包装器文件

## 解决方案

### 步骤1：移除不存在的文件引用

在Keil项目中移除以下文件的引用：

#### NAV组中移除：
```
❌ nav.c
❌ nav_app.c
❌ nav_kf.c
❌ nav_math.c
❌ nav_ods.c
❌ nav_sins.c
❌ navlog.c
❌ nav_gnss.c
❌ nav_imu.c
❌ nav_magnet.c
❌ nav_mahony.c
❌ nav_uwb.c
```

#### Protocol组中移除：
```
❌ transplant.c
```

### 步骤2：添加INAV算法文件到项目

在Keil项目中创建新的**INAV组**，并添加以下25个文件：

#### 核心算法文件：
```
✅ ../INAV/navi.c              - 惯性导航解算核心
✅ ../INAV/kalman.c            - Kalman滤波算法
✅ ../INAV/align.c             - 初始对准算法
✅ ../INAV/dynamic_align.c     - 动态对准算法
✅ ../INAV/ins.c               - INS系统接口
✅ ../INAV/ahrs.c              - 姿态航向参考系统
✅ ../INAV/compen.c            - 补偿算法
✅ ../INAV/matvecmath.c        - 矩阵向量数学运算
✅ ../INAV/private_math.c      - 私有数学函数
```

#### 传感器融合文件：
```
✅ ../INAV/fuseGnssVelPosHeight.c    - GNSS位置速度融合
✅ ../INAV/fuseTwoAntHeading.c       - 双天线航向融合
✅ ../INAV/fuseBodyOdomVel.c         - 车体里程计融合
```

#### 捷联惯导文件：
```
✅ ../INAV/sins.c              - 捷联惯导算法1
✅ ../INAV/sins2.c             - 捷联惯导算法2
```

#### 数据处理文件：
```
✅ ../INAV/read_and_check_gnss_data.c - GNSS数据处理
✅ ../INAV/readpaoche.c        - 跑车数据处理1
✅ ../INAV/readpaoche2.c       - 跑车数据处理2
✅ ../INAV/readpaoche3.c       - 跑车数据处理3
```

#### 传感器接口文件：
```
✅ ../INAV/interrupt.c         - 中断处理
✅ ../INAV/adxl355.c           - ADXL355传感器
✅ ../INAV/AnnTempCompen.c     - 温度补偿
```

### 步骤3：验证保留的文件

确认以下文件保留在项目中：

#### NAV组保留：
```
✅ ../NAV/nav_cli.c            - 导航CLI接口
```

#### Protocol组保留：
```
✅ ../Protocol/computerFrameParse.c
✅ ../Protocol/frame_analysis.c
✅ ../Protocol/protocol.c
✅ ../Protocol/SetParaBao.c
```

#### Source组保留：
```
✅ ../Source/src/main.c
✅ ../Source/src/INS_Data.c
✅ ../Source/src/INS_Sys.c
✅ ../Source/src/INS_Output.c
✅ 其他所有Source文件
```

## 详细操作步骤

### 在Keil中的具体操作：

#### 1. 打开项目
```
File -> Open Project -> INS370-8K24-INS600M-21A/Project/INS.uvprojx
```

#### 2. 移除不存在的文件
- 右键点击NAV组
- 逐个移除nav.c, nav_app.c等不存在的文件
- 右键点击Protocol组
- 移除transplant.c

#### 3. 创建INAV组
- 右键点击项目根节点
- 选择"Add Group"
- 命名为"INAV"

#### 4. 添加INAV文件
- 右键点击INAV组
- 选择"Add Existing Files to Group 'INAV'"
- 浏览到../INAV/目录
- 选择所有25个.c文件
- 点击"Add"

#### 5. 验证配置
- 展开所有组
- 确认没有红色的"文件不存在"标记
- 确认INAV组包含25个文件

## 包含路径配置

确保以下路径在项目包含路径中：

```
C/C++ Include Paths:
../INAV                    ← 重要：INAV算法头文件
../NAV
../Source/inc
../Source/Edwoy
../Protocol
../Common/inc
../bsp/inc
../Library/CMSIS
../Library/GD32F4xx_standard_peripheral/Include
../RTT
```

## 预编译宏定义

确保以下宏定义存在：

```
Preprocessor Symbols:
GD32F470
USE_STDPERIPH_DRIVER
ARM_MATH_CM4
__FPU_PRESENT=1
CONFIG_FATFS=1
CONFIG_SDMMC=1
DEVICE_ACC_TYPE_ADLX355
```

## 验证清单

完成配置后，验证以下项目：

### ✅ 文件组织验证
- [ ] INAV组包含25个算法文件
- [ ] NAV组只包含nav_cli.c
- [ ] Protocol组不包含transplant.c
- [ ] 所有文件路径正确，无红色标记

### ✅ 编译验证
- [ ] 项目Clean成功
- [ ] 项目Rebuild开始编译INAV文件
- [ ] 无"文件找不到"错误

### ✅ 功能验证
- [ ] 编译器尝试编译navi.c
- [ ] 编译器尝试编译kalman.c
- [ ] 编译器尝试编译其他INAV文件

## 预期编译结果

配置正确后，编译输出应该包含：

```
compiling navi.c...
compiling kalman.c...
compiling align.c...
compiling ins.c...
compiling ahrs.c...
compiling compen.c...
... (其他INAV文件)
```

而不应该包含：

```
❌ compiling nav.c...          (已删除的包装器)
❌ compiling nav_app.c...      (已删除的包装器)
❌ compiling transplant.c...   (已删除的包装器)
```

## 总结

问题的根本原因是：
1. **INAV算法文件虽然已移植，但未添加到Keil项目中**
2. **已删除的包装器文件仍在项目配置中被引用**

解决方案：
1. **移除不存在文件的引用**
2. **添加INAV算法文件到项目**
3. **验证项目配置正确**

完成这些配置后，项目将包含完整的HPM6750_INS-370M-SD-OK算法实现，可以正常编译和运行。
