#ifndef ____INS_SYS_H____
#define ____INS_SYS_H____

#include "string.h"
#include "stdlib.h"
#include "stdio.h"
#include "appmain.h"

typedef enum ins_sys_status_t
{
	INS_STATUS_INIT					= 0,
	INS_STATUS_IDLE_STATE			= 1,
	INS_STATUS_START_NAV_STATE		= 2,
	INS_STATUS_WAIT_STABLE_STATE	= 3,
	INS_STATUS_ROUGH_ALIGN_STATE	= 4,
	INS_STATUS_NAVIGATION_STATE		= 5,
	INS_STATUS_STOP_NAV_STATE		= 6,
	INS_STATUS_NAV_OUTPUT			= 7,
	INS_STATUS_DEBUG				= 8,
	INS_STATUS_OTA					= 9,
	INS_STATUS_SLEEP				= 10,
}INS_STATUS_ENUMTypeDef;



typedef struct ins_status_t{
	uint8_t x;
	uint16_t y;
	uint16_t z;
}INS_STATUSTypeDef;

typedef struct txBuf_t
{
	uint8_t txBuf[512];
	uint16_t txSize;
}UARTTxBufTypeDef;

#define SAMPLE_FREQUENCY		1000		//
#define ATTITUDE_SAMPLE_NUM		2			//
//
#define WAIT_STABLE_COUNT	(SAMPLE_FREQUENCY/ATTITUDE_SAMPLE_NUM*5)

//typedef union InsGnssData{
//	char  InsGnssDataChar[148 * 2];
//	float InsGnssDataFloat[37 * 2];
//}ins_gnss_data;

typedef union InsGnssCnt{
	unsigned short InsGnssCntShort[2];
	float InsGnssCntFloat;
}ins_gnss_cnt;


//#define PI				3.14159265358979323846
//#define WIE				7.29211506e-5
#define WGS84G0			9.780318

#define	SAMPLE_FREQ							500
#define TS									1.0/SAMPLE_FREQ			// 

#define NA									15						//   +
#define NB									6						// 

#define NN									3						// 33

#define STATE_UART_DATA_COUNT	(1*SAMPLE_FREQUENCY/ATTITUDE_SAMPLE_NUM)

#define GnssDataFlagOK  7    //    7

#define RS422_UART			UART3
#define RS232A				UART4
#define RS232B				UART5

//extern ins_gnss_data gnss_data;
extern ins_gnss_cnt gnss_cnt;

#endif //____INS_SYS_H____
