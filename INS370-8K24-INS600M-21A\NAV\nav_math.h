/*!
    \file  nav_math.h
    \brief Navigation math functions header file
*/

#ifndef __NAV_MATH_H
#define __NAV_MATH_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

/* Math operation status */
typedef enum {
    NAV_MATH_STATUS_OK = 0,
    NAV_MATH_STATUS_ERROR
} nav_math_status_t;

/* Function prototypes */
void nav_math_cross_product(const float a[3], const float b[3], float c[3]);
float nav_math_dot_product(const float a[3], const float b[3]);
void nav_math_matrix_multiply(const float *a, const float *b, float *c, int m, int n, int p);
void nav_math_matrix_transpose(const float *a, float *b, int m, int n);
nav_math_status_t nav_math_matrix_inverse(const float *a, float *b, int n);
void nav_math_quaternion_multiply(const float q1[4], const float q2[4], float q3[4]);
void nav_math_quaternion_to_matrix(const float q[4], float R[9]);
void nav_math_matrix_to_quaternion(const float R[9], float q[4]);
void nav_math_euler_to_matrix(const float euler[3], float R[9]);
void nav_math_matrix_to_euler(const float R[9], float euler[3]);

#ifdef __cplusplus
}
#endif

#endif /* __NAV_MATH_H */
