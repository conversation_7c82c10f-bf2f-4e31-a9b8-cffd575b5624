/*!
    \file  nav.c
    \brief Navigation wrapper functions for INAV algorithm
    \note  This file provides compatibility layer for original NAV interface
*/

#include "nav.h"
#include "nav_type.h"
#include "nav_const.h"
#include "../INAV/ins.h"
#include "../INAV/FUNCTION.h"
#include "../INAV/GLOBALDATA.h"

/* Navigation system status */
static nav_status_t nav_status = NAV_STATUS_IDLE;
static uint32_t nav_update_count = 0;

/*!
    \brief      Initialize navigation system
    \param[in]  none
    \param[out] none
    \retval     nav_status_t
*/
nav_status_t nav_init(void)
{
    /* Initialize INAV system */
    SysInit();
    
    nav_status = NAV_STATUS_INIT;
    nav_update_count = 0;
    
    return nav_status;
}

/*!
    \brief      Update navigation system
    \param[in]  imu_data: IMU sensor data
    \param[in]  gnss_data: GNSS data
    \param[out] nav_result: navigation result
    \retval     nav_status_t
*/
nav_status_t nav_update(const nav_imu_data_t *imu_data, const nav_gnss_data_t *gnss_data, nav_result_t *nav_result)
{
    if (nav_status == NAV_STATUS_IDLE) {
        return nav_status;
    }
    
    /* Update IMU data */
    if (imu_data != NULL) {
        static DPARA last_gyro[3] = {0};
        static DPARA last_acc[3] = {0};
        
        DPARA gyro[3] = {imu_data->gyro[0], imu_data->gyro[1], imu_data->gyro[2]};
        DPARA acc[3] = {imu_data->acc[0], imu_data->acc[1], imu_data->acc[2]};
        
        /* Run navigation computation */
        NaviCompute(gyro, last_gyro, acc, last_acc, &g_Navi);
        
        /* Save for next iteration */
        for (int i = 0; i < 3; i++) {
            last_gyro[i] = gyro[i];
            last_acc[i] = acc[i];
        }
    }
    
    /* Update GNSS data if available */
    if (gnss_data != NULL && gnss_data->valid) {
        /* Process GNSS data through INAV system */
        Read_And_Check_GNSS_Data(&g_GNSSData_In_Use, (void*)gnss_data, &g_Navi, &g_SysVar);
    }
    
    /* Fill navigation result */
    if (nav_result != NULL) {
        nav_result->latitude = g_Navi.d_Lati;
        nav_result->longitude = g_Navi.d_Logi;
        nav_result->altitude = g_Navi.Height;
        
        nav_result->velocity[0] = g_Navi.Vn[0];  // North
        nav_result->velocity[1] = g_Navi.Vn[1];  // East
        nav_result->velocity[2] = g_Navi.Vn[2];  // Up
        
        nav_result->attitude[0] = g_Navi.d_Atti[0];  // Heading
        nav_result->attitude[1] = g_Navi.d_Atti[1];  // Pitch
        nav_result->attitude[2] = g_Navi.d_Atti[2];  // Roll
        
        nav_result->timestamp = nav_update_count;
        nav_result->status = nav_status;
    }
    
    nav_update_count++;
    nav_status = NAV_STATUS_RUNNING;
    
    return nav_status;
}

/*!
    \brief      Get navigation status
    \param[in]  none
    \param[out] none
    \retval     nav_status_t
*/
nav_status_t nav_get_status(void)
{
    return nav_status;
}

/*!
    \brief      Reset navigation system
    \param[in]  none
    \param[out] none
    \retval     nav_status_t
*/
nav_status_t nav_reset(void)
{
    nav_status = NAV_STATUS_IDLE;
    nav_update_count = 0;
    
    /* Reset INAV system */
    SysInit();
    
    return nav_status;
}

/*!
    \brief      Set navigation parameters
    \param[in]  param_id: parameter ID
    \param[in]  value: parameter value
    \param[out] none
    \retval     nav_status_t
*/
nav_status_t nav_set_param(uint32_t param_id, float value)
{
    /* Parameter setting implementation */
    switch (param_id) {
        case NAV_PARAM_INIT_LAT:
            g_InitBind.r_InitLati = value * D2R;
            break;
        case NAV_PARAM_INIT_LON:
            g_InitBind.r_InitLogi = value * D2R;
            break;
        case NAV_PARAM_INIT_ALT:
            g_InitBind.InitHeight = value;
            break;
        default:
            return NAV_STATUS_ERROR;
    }
    
    return nav_status;
}

/*!
    \brief      Get navigation parameters
    \param[in]  param_id: parameter ID
    \param[out] value: parameter value
    \retval     nav_status_t
*/
nav_status_t nav_get_param(uint32_t param_id, float *value)
{
    if (value == NULL) {
        return NAV_STATUS_ERROR;
    }
    
    switch (param_id) {
        case NAV_PARAM_INIT_LAT:
            *value = g_InitBind.r_InitLati * R2D;
            break;
        case NAV_PARAM_INIT_LON:
            *value = g_InitBind.r_InitLogi * R2D;
            break;
        case NAV_PARAM_INIT_ALT:
            *value = g_InitBind.InitHeight;
            break;
        default:
            return NAV_STATUS_ERROR;
    }
    
    return nav_status;
}
