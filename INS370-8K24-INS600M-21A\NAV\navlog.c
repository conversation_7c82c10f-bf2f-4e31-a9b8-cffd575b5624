/*!
    \file  navlog.c
    \brief Navigation logging system
*/

#include "navlog.h"
#include <stdio.h>

static nav_log_level_t current_log_level = NAV_LOG_LEVEL_INFO;

nav_log_status_t nav_log_init(nav_log_level_t level)
{
    current_log_level = level;
    return NAV_LOG_STATUS_OK;
}

nav_log_status_t nav_log_write(nav_log_level_t level, const char *format, ...)
{
    if (level >= current_log_level) {
        printf("[NAV] ");
        // In a real implementation, you would use va_list here
        printf("%s\n", format);
    }
    return NAV_LOG_STATUS_OK;
}
