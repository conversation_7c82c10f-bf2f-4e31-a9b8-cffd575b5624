#include "bsp_fmc.h"
#include "systick.h"
#include "fpgad.h"
#include "sdram.h"
#include "appmain.h"



/* define mode register content */
/* burst length */
#define SDRAM_MODEREG_BURST_LENGTH_1             ((uint16_t)0x0000)
#define SDRAM_MODEREG_BURST_LENGTH_2             ((uint16_t)0x0001)
#define SDRAM_MODEREG_BURST_LENGTH_4             ((uint16_t)0x0002)
#define SDRAM_MODEREG_BURST_LENGTH_8             ((uint16_t)0x0003)

/* burst type */
#define SDRAM_MODEREG_BURST_TYPE_SEQUENTIAL      ((uint16_t)0x0000)
#define SDRAM_MODEREG_BURST_TYPE_INTERLEAVED     ((uint16_t)0x0008)

/* cas latency */
#define SDRAM_MODEREG_CAS_LATENCY_2              ((uint16_t)0x0020)
#define SDRAM_MODEREG_CAS_LATENCY_3              ((uint16_t)0x0030)

/* write mode */
#define SDRAM_MODEREG_WRITEBURST_MODE_PROGRAMMED ((uint16_t)0x0000)
#define SDRAM_MODEREG_WRITEBURST_MODE_SINGLE     ((uint16_t)0x0200)

#define SDRAM_MODEREG_OPERATING_MODE_STANDARD    ((uint16_t)0x0000)

#define SDRAM_TIMEOUT                            ((uint32_t)0x0000FFFF)

/*
* brief    sdram peripheral initialize
* param[in]  sdram_device: specify the SDRAM device 
* param[out] none
* retval     none
*/
void exmc_synchronous_dynamic_ram_init(uint32_t sdram_device)
{


}

/*
* brief    fill the buffer with specified value
* param[in]  pbuffer: pointer on the buffer to fill
* param[in]  buffer_lengh: length of the buffer to fill
* param[in]  offset: the offset of the buffer
* param[out] none
* retval     none
*/
void fill_buffer(uint8_t *pbuffer, uint16_t buffer_lengh, uint16_t offset)
{

}

/*
* brief    write a byte buffer(data is 8 bits) to the EXMC SDRAM memory
* param[in]  sdram_device: specify which a SDRAM memory block is written
* param[in]  pbuffer: pointer to buffer
* param[in]  writeaddr: SDRAM memory internal address from which the data will be written
* param[in]  numbytetowrite: number of bytes to write
* param[out] none
* retval     none
*/
void sdram_writebuffer_8(uint32_t sdram_device,uint8_t* pbuffer, uint32_t writeaddr, uint32_t numbytetowrite)
{

}

/*
* brief    read a block of 8-bit data from the EXMC SDRAM memory
* param[in]  sdram_device: specify which a SDRAM memory block is written
* param[in]  pbuffer: pointer to buffer
* param[in]  readaddr: SDRAM memory internal address to read from
* param[in]  numbytetoread: number of bytes to read
* param[out] none
* retval     none
*/
void sdram_readbuffer_8(uint32_t sdram_device,uint8_t* pbuffer, uint32_t readaddr, uint32_t numbytetoread)
{

}

int DRam_Read(uint32_t addr, uint16_t* data, uint32_t len)
{

}

int DRam_Write(uint32_t addr, uint16_t* data, uint32_t len)
{

}

void FMC_ReadBuffer(EFpgaSpace space, uint32_t addr, uint16_t* pBuffer, uint32_t size)
{

}


void FMC_WriteBuffer(EFpgaSpace space, uint32_t addr, uint16_t* pBuffer, uint32_t size)
{

}

uint16_t FMC_ReadWord(EFpgaSpace space, uint32_t addr)
{

}

void FMC_WriteWord(EFpgaSpace space, uint32_t addr, uint16_t data)
{

}

/*
* brief    sram peripheral initialize
* param[in]  none 
* param[out] none
* retval     none
*/
void exmc_asynchronous_sram_init(void)
{
	fmc_init();
}


/*
* brief      get the sector number, size and range of the given address
* param[in]  address: The flash address
* param[out] none
* retval     fmc_sector_info_struct: The information of a sector
*/
fmc_sector_info_struct fmc_sector_info_get(uint32_t addr)
{


}

/*
* brief      get the sector number by a given sector name
* param[in]  address: a given sector name
* param[out] none
* retval     uint32_t: sector number
*/
uint32_t sector_name_to_number(uint32_t sector_name)
{

}

/*
* brief      erases the sector of a given address
* param[in]  address: a given address
* param[out] none
* retval     none
*/
int fmc_erase_sector_by_address(uint32_t address)
{

}

/*
* brief      write 32 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_32: data pointer
* param[out] none
* retval     none
*/
void fmc_write_32bit_data(uint32_t address, uint16_t length, int32_t* data_32)
{

}

/*
* brief      read 32 bit length data from a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_32: data pointer
* param[out] none
* retval     none
*/
void fmc_read_32bit_data(uint32_t address, uint16_t length, int32_t* data_32)
{

}

/*
* brief      write 16 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_16: data pointer
* param[out] none
* retval     none
*/
void fmc_write_16bit_data(uint32_t address, uint16_t length, int16_t* data_16)
{

}

/*
* brief      read 16 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_16: data pointer
* param[out] none
* retval     none
*/
void fmc_read_16bit_data(uint32_t address, uint16_t length, int16_t* data_16)
{

}

/*
* brief      write 8 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_8: data pointer
* param[out] none
* retval     none
*/
void fmc_write_8bit_data(uint32_t address, uint16_t length, int8_t* data_8)
{

}

void fmc_write_8bit_data1(uint32_t address, uint16_t length, int8_t* data_8)
{

}

/*
* brief      read 8 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_8: data pointer
* param[out] none
* retval     none
*/
void fmc_read_8bit_data(uint32_t address, uint16_t length, int8_t* data_8)
{

}

/********************************************************************************\
: 
: rxPort - 
     baudRate - 
     parityBits - 
     stopBits - 
     mode - RS232/422/485
     enable - 
: 
\********************************************************************************/
void Uart_RxInit(EUartRxPort rxPort, EUartBaudrate baudRate, EUartParitybits parityBits, EUartStopbits stopBits, EUartMode mode, EUartEnable enable)
{

}

/********************************************************************************\
: 
: txPort - 
     baudRate - 
     parityBits - 
     stopBits - 
     mode - RS232/422/485
     enable - 
: 
\********************************************************************************/
void Uart_TxInit(EUartTxPort txPort, EUartBaudrate baudRate, EUartParitybits parityBits, EUartStopbits stopBits, EUartMode mode, EUartEnable enable)
{

}

/********************************************************************************\
: 
: txPort - 
     buffer - 
     start - 
     len - 
: 
\********************************************************************************/
void Uart_SendMsg(EUartTxPort txPort, USHORT start, USHORT len, UCHAR* buffer)
{
    uart4sendmsg((char*)buffer, len);
}


/********************************************************************************\
: 
: rxPort - 
     buffer - 
     len - 
: 
\********************************************************************************/
USHORT Uart_RecvMsg(EUartRxPort rxPort, USHORT len, UCHAR* buffer)
{

}

/********************************************************************************\
: 
: rxPort - 
: 
\********************************************************************************/
void Uart_ClearRecvBuffer(EUartRxPort rxPort)
{

}



