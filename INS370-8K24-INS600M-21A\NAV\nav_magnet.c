/*!
    \file  nav_magnet.c
    \brief Navigation magnetometer interface
*/

#include "nav_magnet.h"

nav_magnet_status_t nav_magnet_init(void)
{
    return NAV_MAGNET_STATUS_OK;
}

nav_magnet_status_t nav_magnet_calibrate(nav_magnet_calib_t *calib)
{
    (void)calib;
    return NAV_MAGNET_STATUS_OK;
}

nav_magnet_status_t nav_magnet_update(const nav_magnet_data_t *mag_data)
{
    (void)mag_data;
    return NAV_MAGNET_STATUS_OK;
}
