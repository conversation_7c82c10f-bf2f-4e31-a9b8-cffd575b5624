***********************************************************************************************
***                                                                                         ***
***                                    LINK INFORMATION                                     ***
***                                                                                         ***
***********************************************************************************************

Linker version:

  SEGGER RISC-V Linker 4.38.11 compiled Jun 11 2024 14:00:05
  Copyright (c) 2017-2022 SEGGER Microcontroller GmbH    www.segger.com


***********************************************************************************************
***                                                                                         ***
***                                     MODULE SUMMARY                                      ***
***                                                                                         ***
***********************************************************************************************

Memory use by input file:

  Object File                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  align.o                                             1 692          96                        
  app_tool.o                                            112                                    
  board.c.o                                           4 128       1 358                        
  bsp_fmc.o                                              44                                    
  bsp_gpio.o                                            452                                    
  bsp_tim.o                                              16                                  20
  computerFrameParse.o                                  124                                 596
  datado.o                                              196          28           4           4
  diskio.c.o                                            626                                    
  dynamic_align.o                                     1 736          24                        
  ff.c.o                                             28 928         285                   1 163
  ff_queue.o                                            844                                   1
  ffunicode.c.o                                         568      87 894                        
  FirmwareUpdateFile.o                                  170                                    
  flash.o                                               552          31                     256
  fpgad.o                                               596          40                     762
  frame_analysis.o                                    4 112         360                     260
  gd32f4xx_it.o                                         212                                   2
  gdwatch.o                                             744          68                  59 798
  hpm_bootheader.c.o                                                144                        
  hpm_clock_drv.c.o                                   1 662         160                       4
  hpm_debug_console.c.o                                 256                       4           8
  hpm_dma_drv.c.o                                     1 076                                    
  hpm_femc_drv.c.o                                    1 126                                    
  hpm_gpio_drv.c.o                                      234                                    
  hpm_gptmr_drv.c.o                                     436                                    
  hpm_l1c_drv.o                                         482         171                        
  hpm_pcfg_drv.c.o                                       72                                    
  hpm_pllctl_drv.c.o                                  1 100          24                        
  hpm_pmp_drv.c.o                                       954         128                        
  hpm_sdmmc_common.c.o                                  858                                    
  hpm_sdmmc_disk.c.o                                  1 854                     216      50 713
  hpm_sdmmc_host.c.o                                  4 380                                    
  hpm_sdmmc_osal.o                                      480                                    
  hpm_sdmmc_port.c.o                                    506                                    
  hpm_sdmmc_sd.c.o                                    7 670         233                        
  hpm_sdxc_drv.c.o                                    4 966                                    
  hpm_sysctl_drv.c.o                                    546                                    
  hpm_uart_drv.c.o                                    1 030          24                        
  INS_Data.o                                            230                                 353
  INS_Init.o                                            370          44                      17
  INS_Output.o                                        6 172          59                   2 054
  InsTestingEntry.o                                  15 262         240                   2 797
  kalman.o                                           12 372         376                        
  main.o                                              1 274         331                  30 825
  matvecmath.o                                        1 398           8                        
  navi.o                                              6 736         224                        
  pinmux.c.o                                          1 256                                    
  read_and_check_gnss_data.o                          1 328          64                        
  readpaoche.o                                        7 348          88           1           1
  reset.c.o                                             204                                    
  sd_fatfs.o                                          4 114       2 147           8       4 367
  SetParaBao.o                                       15 596         256           5         603
  startup.s.o                                           152         512                        
  system.c.o                                            134                                    
  systick.o                                              24                                    
  timer.o                                             1 076                                   4
  trap.c.o                                              560          64                        
  uart.o                                              1 396         125         136      42 260
  uart_dma.o                                          1 708         258           1            
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (60 objects)                             154 250      95 864         375     196 868
  ---------------------------------------------  ----------  ----------  ----------  ----------
  heapops_basic_rv32gc_d_balanced.a                     102                                   4
  heapops_disable_interrupts_locking_rv32gc_d_balanced.a
                                                         70                                   4
  libc_rv32gc_d_balanced.a                           10 834       1 958                      28
  mbops_timeops_rv32gc_d_balanced.a                     268         549          20           4
  SEGGER_RV32_crtinit_rv32gc_d_balanced.a               238                                    
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (5 archives)                              11 512       2 507          20          40
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Linker created (shared data, fills, blocks):                   77 812                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            165 762     176 183         395     229 676
  =============================================  ==========  ==========  ==========  ==========

Memory use by archive member:

  Archive member                                    RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
                                                      3 278         248                        
  convops.o (libc_rv32gc_d_balanced.a)                  562                                    
  errno.o (libc_rv32gc_d_balanced.a)                     14                                   4
  execops.o (libc_rv32gc_d_balanced.a)                  258          30                      24
  fileops.o (libc_rv32gc_d_balanced.a)                  170                                    
  floatasmops_rv.o (libc_rv32gc_d_balanced.a)           288          24                        
  floatops.o (libc_rv32gc_d_balanced.a)               2 416         368                        
  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
                                                         70                                   4
  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
                                                        102                                   4
  intasmops_rv.o (libc_rv32gc_d_balanced.a)              76                                    
  intops.o (libc_rv32gc_d_balanced.a)                 2 150       1 024                        
  mbops.o (mbops_timeops_rv32gc_d_balanced.a)           268         549          20           4
  prinops.o (libc_rv32gc_d_balanced.a)                  586         192                        
  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
                                                        238                                    
  strasmops_rv.o (libc_rv32gc_d_balanced.a)             528                                    
  strops.o (libc_rv32gc_d_balanced.a)                   448                                    
  utilops.o (libc_rv32gc_d_balanced.a)                   60          72                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (17 members from 5 archives)              11 512       2 507          20          40
  Objects (60 files)                                154 250      95 864         375     196 868
  Linker created (shared data, fills, blocks):                   77 812                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            165 762     176 183         395     229 676
  =============================================  ==========  ==========  ==========  ==========

Memory use by linker:

  Description                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Initialization table                                           77 596                        
  Memory for block 'heap'                                                                16 384
  Memory for block 'stack'                                                               16 384
  Merged section data (64-bit)                                      216                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (linker created):                                     77 812                  32 768
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Objects (60 files)                                154 250      95 864         375     196 868
  Archives (5 files)                                 11 512       2 507          20          40
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            165 762     176 183         395     229 676
  =============================================  ==========  ==========  ==========  ==========


***********************************************************************************************
***                                                                                         ***
***                                     SECTION DETAIL                                      ***
***                                                                                         ***
***********************************************************************************************

Sections by address:

  Range              Symbol or [section] Name         Size  Al  Init  Ac  Object File
  -----------------  -------------------------  ----------  --  ----  --  -----------
  00000000-000001ff  __vector_table                    512  512
                                                                Init  RO  startup.s.o
  00000200-00000311  isr_gpio                          274   4  Init  RX  main.o
  00000312-00000313  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000314-00000435  tick_ms_isr                       290   4  Init  RX  timer.o
  00000436-00000437  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000438-00000549  sdcard_isr                        274   4  Init  RX  hpm_sdmmc_disk.c.o
  0000054a-0000054b  ( ALIGN .=.+2 )                     2   -  ----  -   -
  0000054c-00000551  nmi_handler                         6   4  Init  RX  startup.s.o
  00000552-00000553  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000554-0000073d  irq_handler_trap                  490   4  Init  RX  trap.c.o
  0000073e-000022f7  fmc2sinsraw                     7 098   2  Init  RX  readpaoche.o
  000022f8-00003b9d  AnalyticCoordinateAxis          6 310   2  Init  RX  InsTestingEntry.o
  00003b9e-00004dbf  f_mkfs                          4 642   2  Init  RX  ff.c.o
  00004dc0-00005d85  frame_pack_and_send             4 038   2  Init  RX  frame_analysis.o
  00005d86-00006b99  output_gdw_do                   3 604   2  Init  RX  INS_Output.o
  00006b9a-0000740f  ComputeFn                       2 166   2  Init  RX  kalman.o
  00007410-00007bbf  AlgorithmAct                    1 968   2  Init  RX  InsTestingEntry.o
  00007bc0-0000830b  mount_volume                    1 868   2  Init  RX  ff.c.o
  0000830c-0000888f  FPGATo422_00BB_send             1 412   2  Init  RX  InsTestingEntry.o
  00008890-00008deb  ReadFileToSd                    1 372   2  Init  RX  sd_fatfs.o
  00008dec-000093d7  ErrStore_1s                     1 516   2  Init  RX  kalman.o
  000093d8-00009909  Kalman_StartUp                  1 330   2  Init  RX  kalman.o
  0000990a-00009e4f  ComputeZk                       1 350   2  Init  RX  kalman.o
  00009e50-0000a30b  sd_decode_csd                   1 212   2  Init  RX  hpm_sdmmc_sd.c.o
  0000a30c-0000a81b  create_name                     1 296   2  Init  RX  ff.c.o
  0000a81c-0000acf9  f_lseek                         1 246   2  Init  RX  ff.c.o
  0000acfa-0000b18b  f_open                          1 170   2  Init  RX  ff.c.o
  0000b18c-0000b635  ErrCorrect_1_Navi_Time          1 194   2  Init  RX  kalman.o
  0000b636-0000ba97  INS912_Output                   1 122   2  Init  RX  INS_Output.o
  0000ba98-0000beeb  f_printf                        1 108   2  Init  RX  ff.c.o
  0000beec-0000c34f  SetParaAll                      1 124   2  Init  RX  SetParaBao.o
  0000c350-0000c787  output_fpga_void                1 080   2  Init  RX  INS_Output.o
  0000c788-0000cb63  dir_register                      988   2  Init  RX  ff.c.o
  0000cb64-0000cef1  UartDmaRecSetPara                 910   2  Init  RX  SetParaBao.o
  0000cef2-0000d2a5  f_write                           948   2  Init  RX  ff.c.o
  0000d2a6-0000d659  f_async_write                     948   2  Init  RX  ff.c.o
  0000d65a-0000da27  Rk_Init                           974   2  Init  RX  kalman.o
  0000da28-0000ddef  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                                                       968   2  Init  RX  InsTestingEntry.o
  0000ddf0-0000e1b7  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                                                       968   2  Init  RX  InsTestingEntry.o
  0000e1b8-0000e4e1  analysisRxdata                    810   2  Init  RX  uart.o
  0000e4e2-0000e807  NaviCompute                       806   2  Init  RX  navi.o
  0000e808-0000eb4b  f_read                            836   2  Init  RX  ff.c.o
  0000eb4c-0000ee49  InitParaToAlgorithm               766   2  Init  RX  SetParaBao.o
  0000ee4a-0000f0f9  sd_card_init                      688   2  Init  RX  hpm_sdmmc_sd.c.o
  0000f0fa-0000f3f7  SaveGNSSData                      766   2  Init  RX  read_and_check_gnss_data.o
  0000f3f8-0000f69f  dir_find                          680   2  Init  RX  ff.c.o
  0000f6a0-0000f957  ReadParaFromFlash                 696   2  Init  RX  SetParaBao.o
  0000f958-0000fc13  Mat_Inv                           700   2  Init  RX  matvecmath.o
  0000fc14-0000febf  sdmmchost_init_io                 684   2  Init  RX  hpm_sdmmc_host.c.o
  0000fec0-0001015d  fpgadata_Predo_chen_SetAlgParm_acc
                                                       670   2  Init  RX  InsTestingEntry.o
  0001015e-000103b7  fpgadata_Predo_chen_OutDataSet
                                                       602   2  Init  RX  InsTestingEntry.o
  000103b8-00010637  create_chain                      640   2  Init  RX  ff.c.o
  00010638-0001088d  f_sync                            598   2  Init  RX  ff.c.o
  0001088e-00010b15  create_partition                  648   2  Init  RX  ff.c.o
  00010b16-00010d87  get_fat                           626   2  Init  RX  ff.c.o
  00010d88-00010fef  fpgadata_Predo_chen_SetAlgParm_gyro
                                                       616   2  Init  RX  InsTestingEntry.o
  00010ff0-0001126f  CnbToQ                            640   2  Init  RX  navi.o
  00011270-00011497  DynamicInertialSysAlignCompute
                                                       552   2  Init  RX  dynamic_align.o
  00011498-000116cf  RestoreFactory                    568   2  Init  RX  SetParaBao.o
  000116d0-00011901  ComputeSi                         562   2  Init  RX  dynamic_align.o
  00011902-00011b23  put_fat                           546   2  Init  RX  ff.c.o
  00011b24-00011d4f  sdxc_set_adma2_desc               556   2  Init  RX  hpm_sdxc_drv.c.o
  00011d50-00011f5b  follow_path                       524   2  Init  RX  ff.c.o
  00011f5c-0001217f  dma_setup_channel                 548   2  Init  RX  hpm_dma_drv.c.o
  00012180-00012373  remove_chain                      500   2  Init  RX  ff.c.o
  00012374-00012579  QToCnb                            518   2  Init  RX  navi.o
  0001257a-00012761  load_xdir                         488   2  Init  RX  ff.c.o
  00012762-0001294b  sd_write_blocks                   490   2  Init  RX  hpm_sdmmc_sd.c.o
  0001294c-00012b2f  dir_read                          484   2  Init  RX  ff.c.o
  00012b30-00012d0d  sd_read_blocks                    478   2  Init  RX  hpm_sdmmc_sd.c.o
  00012d0e-00012ed3  InertialSysAlignCompute           454   2  Init  RX  align.o
  00012ed4-000130a3  ComputeKk                         464   2  Init  RX  kalman.o
  000130a4-0001325f  check_fs                          444   2  Init  RX  ff.c.o
  00013260-0001341f  sd_start_write_blocks             448   2  Init  RX  hpm_sdmmc_sd.c.o
  00013420-000135f3  dma_config_linked_descriptor
                                                       468   2  Init  RX  hpm_dma_drv.c.o
  000135f4-000137c7  GNSSAndHeadDataTest               468   2  Init  RX  read_and_check_gnss_data.o
  000137c8-00013995  CorrectAtti                       462   2  Init  RX  kalman.o
  00013996-00013b37  uart_dma_tx_send                  418   2  Init  RX  uart_dma.o
  00013b38-00013cf5  putc_bfd                          446   2  Init  RX  ff.c.o
  00013cf6-00013ea9  mcusendtopcdriversdata            436   2  Init  RX  gdwatch.o
  00013eaa-00014059  SetParaGnssInitValue              432   2  Init  RX  SetParaBao.o
  0001405a-00014215  sd_decode_status                  444   2  Init  RX  hpm_sdmmc_sd.c.o
  00014216-000143cb  sdxc_parse_interrupt_status
                                                       438   2  Init  RX  hpm_sdxc_drv.c.o
  000143cc-0001457f  ComputeQ                          436   2  Init  RX  navi.o
  00014580-00014737  ComputeFk                         440   2  Init  RX  kalman.o
  00014738-000148e1  sdxc_set_adma3_desc               426   2  Init  RX  hpm_sdxc_drv.c.o
  000148e2-00014a81  sdxc_transfer_nonblocking         416   2  Init  RX  hpm_sdxc_drv.c.o
  00014a82-00014bff  f_unlink                          382   2  Init  RX  ff.c.o
  00014c00-00014d9f  CnbToAtti                         416   2  Init  RX  navi.o
  00014da0-00014f39  ComputeVn                         410   2  Init  RX  navi.o
  00014f3a-000150c1  sdmmchost_power_control           392   2  Init  RX  hpm_sdmmc_host.c.o
  000150c2-0001524d  SysInit                           396   2  Init  RX  navi.o
  0001524e-000153dd  ld_qword                          400   2  Init  RX  ff.c.o
  000153de-00015551  SetParaUpdateStart                372   2  Init  RX  SetParaBao.o
  00015552-00015695  ParaUpdateHandle                  324   2  Init  RX  SetParaBao.o
  00015696-00015819  uart_init                         388   2  Init  RX  hpm_uart_drv.c.o
  0001581a-00015991  SetParaFactorGyro                 376   2  Init  RX  SetParaBao.o
  00015992-00015b09  SetParaFactorAcc                  376   2  Init  RX  SetParaBao.o
  00015b0a-00015c87  uart_dma_recv_polling             382   2  Init  RX  uart_dma.o
  00015c88-00015df3  uart_dma_init                     364   2  Init  RX  uart_dma.o
  00015df4-00015f27  initializationdriversettings
                                                       308   2  Init  RX  gdwatch.o
  00015f28-0001607d  output_fpgatxt_do                 342   2  Init  RX  INS_Output.o
  0001607e-000161c1  bsp_gpio_init                     324   2  Init  RX  bsp_gpio.o
  000161c2-00016315  WriteBB00FileToSd                 340   2  Init  RX  sd_fatfs.o
  00016316-00016491  Hk_Init                           380   2  Init  RX  kalman.o
  00016492-00016605  gen_numname                       372   2  Init  RX  ff.c.o
  00016606-00016777  ff_wtoupper                       370   2  Init  RX  ffunicode.c.o
  00016778-000168e5  board_init_sd_host_params         366   2  Init  RX  hpm_sdmmc_port.c.o
  000168e6-00016a2d  sdmmchost_transfer                328   2  Init  RX  hpm_sdmmc_host.c.o
  00016a2e-00016b8d  ReadPara_4                        352   2  Init  RX  SetParaBao.o
  00016b8e-00016cd9  KalCompute                        332   2  Init  RX  kalman.o
  00016cda-00016e1f  sd_probe_bus_voltage              326   2  Init  RX  hpm_sdmmc_sd.c.o
  00016e20-00016f81  uart_calculate_baudrate           354   2  Init  RX  hpm_uart_drv.c.o
  00016f82-000170d1  dir_next                          336   2  Init  RX  ff.c.o
  000170d2-00017217  SetParaBaud                       326   2  Init  RX  SetParaBao.o
  00017218-00017351  SetParaUpdateSend                 314   2  Init  RX  SetParaBao.o
  00017352-0001749b  sdxc_prepare_cmd_xfer             330   2  Init  RX  hpm_sdxc_drv.c.o
  0001749c-000175cd  sdxc_error_recovery               306   2  Init  RX  hpm_sdxc_drv.c.o
  000175ce-000176cf  fpgadata_Predo_chen_preAlgParm_370
                                                       258   2  Init  RX  InsTestingEntry.o
  000176d0-00017813  Qua_Mul                           324   2  Init  RX  matvecmath.o
  00017814-00017955  st_qword                          322   2  Init  RX  ff.c.o
  00017956-00017a97  gptmr_channel_config              322   2  Init  RX  hpm_gptmr_drv.c.o
  00017a98-00017bcd  gnss_check_bind                   310   2  Init  RX  InsTestingEntry.o
  00017bce-00017cff  ReadPara_2                        306   2  Init  RX  SetParaBao.o
  00017d00-00017e33  sd_switch_function                308   2  Init  RX  hpm_sdmmc_sd.c.o
  00017e34-00017f63  SetParaVector                     304   2  Init  RX  SetParaBao.o
  00017f64-00018093  SetParaGnss                       304   2  Init  RX  SetParaBao.o
  00018094-000181c3  SetParaDeviation                  304   2  Init  RX  SetParaBao.o
  000181c4-000182f3  SetParaAngle                      304   2  Init  RX  SetParaBao.o
  000182f4-00018431  AttiToCnb                         318   2  Init  RX  navi.o
  00018432-00018543  SetParaUpdateEnd                  274   2  Init  RX  SetParaBao.o
  00018544-00018661  SaveParaToFlash                   286   2  Init  RX  SetParaBao.o
  00018662-00018785  ComputeVibn                       292   2  Init  RX  navi.o
  00018786-00018897  sd_disk_initialize                274   2  Init  RX  hpm_sdmmc_disk.c.o
  00018898-000189c5  ComputePos                        302   2  Init  RX  navi.o
  000189c6-00018acd  sync_fs                           264   2  Init  RX  ff.c.o
  00018ace-00018bf5  show_error_string                 296   2  Init  RX  sd_fatfs.o
  00018bf6-00018cc5  fpgadata_syn_count_do             208   2  Init  RX  fpgad.o
  00018cc6-00018ddb  sd_polling_card_status_busy
                                                       278   2  Init  RX  hpm_sdmmc_sd.c.o
  00018ddc-00018ee9  SetParaCoord                      270   2  Init  RX  SetParaBao.o
  00018eea-00018ff7  sd_set_bus_timing                 270   2  Init  RX  hpm_sdmmc_sd.c.o
  00018ff8-00019105  create_xdir                       270   2  Init  RX  ff.c.o
  00019106-0001921f  rom_xpi_nor_erase_sector          282   2  Init  RX  flash.o
  00019220-00019337  tchar2uni                         280   2  Init  RX  ff.c.o
  00019338-0001941b  get_fpgadata_do                   228   2  Init  RX  fpgad.o
  0001941c-0001952b  dir_sdi                           272   2  Init  RX  ff.c.o
  0001952c-00019633  SetParaFrequency                  264   2  Init  RX  SetParaBao.o
  00019634-00019739  SetParaCalibration                262   2  Init  RX  SetParaBao.o
  0001973a-00019829  SetParaSdHandle                   240   2  Init  RX  SetParaBao.o
  0001982a-0001992d  ReadPara_1                        260   2  Init  RX  SetParaBao.o
  0001992e-00019a15  Fatfs_Init                        232   2  Init  RX  sd_fatfs.o
  00019a16-00019b23  ComputeAttiRate                   270   2  Init  RX  navi.o
  00019b24-00019c2f  get_boot_reason                   268   2  Init  RX  main.o
  00019c30-00019d21  ReadPara                          242   2  Init  RX  SetParaBao.o
  00019d22-00019e1b  ReadPara_3                        250   2  Init  RX  SetParaBao.o
  00019e1c-00019f15  ReadPara_0                        250   2  Init  RX  SetParaBao.o
  00019f16-0001a00b  DynamicNavi_Init                  246   2  Init  RX  navi.o
  0001a00c-0001a0c9  main                              190   2  Init  RX  main.o
  0001a0ca-0001a1c1  cmp_lfn                           248   2  Init  RX  ff.c.o
  0001a1c2-0001a2ab  SetParaDataOutType                234   2  Init  RX  SetParaBao.o
  0001a2ac-0001a351  TIMER2_IRQHandler                 166   2  Init  RX  gd32f4xx_it.o
  0001a352-0001a441  SetParaTime                       240   2  Init  RX  SetParaBao.o
  0001a442-0001a527  caninfupdate                      230   2  Init  RX  INS_Data.o
  0001a528-0001a61d  sdmmchost_vsel_pin_control
                                                       246   2  Init  RX  hpm_sdmmc_host.c.o
  0001a61e-0001a6ff  sdmmchost_start_transfer          226   2  Init  RX  hpm_sdmmc_host.c.o
  0001a700-0001a7e9  sd_read_status                    234   2  Init  RX  hpm_sdmmc_sd.c.o
  0001a7ea-0001a8d3  SetParaKalmanR                    234   2  Init  RX  SetParaBao.o
  0001a8d4-0001a9bd  SetParaKalmanQ                    234   2  Init  RX  SetParaBao.o
  0001a9be-0001aaa7  SetParaFilter                     234   2  Init  RX  SetParaBao.o
  0001aaa8-0001ab97  put_lfn                           240   2  Init  RX  ff.c.o
  0001ab98-0001ac8d  Qk_Init                           246   2  Init  RX  kalman.o
  0001ac8e-0001ad83  Pk_Init                           246   2  Init  RX  kalman.o
  0001ad84-0001ae6b  pick_lfn                          232   2  Init  RX  ff.c.o
  0001ae6c-0001af4b  SetParaGyroType                   224   2  Init  RX  SetParaBao.o
  0001af4c-0001b02b  SetParaGpsType                    224   2  Init  RX  SetParaBao.o
  0001b02c-0001b10b  SetParaDebugMode                  224   2  Init  RX  SetParaBao.o
  0001b10c-0001b1e9  sdxc_error_recovery_first_half
                                                       222   2  Init  RX  hpm_sdxc_drv.c.o
  0001b1ea-0001b2ab  sdmmchost_init                    194   2  Init  RX  hpm_sdmmc_host.c.o
  0001b2ac-0001b385  sdmmc_card_async_write            218   2  Init  RX  hpm_sdmmc_disk.c.o
  0001b386-0001b45b  sdmmchost_irq_handler             214   2  Init  RX  hpm_sdmmc_host.c.o
  0001b45c-0001b537  timer_config                      220   2  Init  RX  timer.o
  0001b538-0001b61f  find_bitmap                       232   2  Init  RX  ff.c.o
  0001b620-0001b6eb  FinishInertialSysAlign            204   2  Init  RX  align.o
  0001b6ec-0001b7b7  FinishDynamicInertialSysAlign
                                                       204   2  Init  RX  dynamic_align.o
  0001b7b8-0001b879  sdmmchost_switch_to_1v8           194   2  Init  RX  hpm_sdmmc_host.c.o
  0001b87a-0001b959  hpm_sdmmc_osal_event_wait         224   2  Init  RX  hpm_sdmmc_osal.o
  0001b95a-0001ba2d  SetParaUpdateStop                 212   2  Init  RX  SetParaBao.o
  0001ba2e-0001bb15  ComputeCie                        232   2  Init  RX  align.o
  0001bb16-0001bbeb  sd_send_scr                       214   2  Init  RX  hpm_sdmmc_sd.c.o
  0001bbec-0001bcc5  GNSS_Valid_PPSStart               218   2  Init  RX  InsTestingEntry.o
  0001bcc6-0001bd95  store_xdir                        208   2  Init  RX  ff.c.o
  0001bd96-0001be6d  Virtual_PPS_insert_5hz            216   2  Init  RX  InsTestingEntry.o
  0001be6e-0001bf49  change_bitmap                     220   2  Init  RX  ff.c.o
  0001bf4a-0001c01b  dir_alloc                         210   2  Init  RX  ff.c.o
  0001c01c-0001c0ef  sdmmc_card_write                  212   2  Init  RX  hpm_sdmmc_disk.c.o
  0001c0f0-0001c1c3  sdmmc_card_read                   212   2  Init  RX  hpm_sdmmc_disk.c.o
  0001c1c4-0001c291  Navi_Init                         206   2  Init  RX  navi.o
  0001c292-0001c35b  Kalman_Init                       202   2  Init  RX  kalman.o
  0001c35c-0001c433  sdxc_receive_cmd_response         216   2  Init  RX  hpm_sdxc_drv.c.o
  0001c434-0001c4f3  sdxc_perform_tuning_flow_sequence
                                                       192   2  Init  RX  hpm_sdxc_drv.c.o
  0001c4f4-0001c5ab  WriteFileOpenFromSd               184   2  Init  RX  sd_fatfs.o
  0001c5ac-0001c67b  ComputePk                         208   2  Init  RX  kalman.o
  0001c67c-0001c73b  ComputeCib0i                      192   2  Init  RX  align.o
  0001c73c-0001c811  extract_csd_field                 214   2  Init  RX  hpm_sdmmc_common.c.o
  0001c812-0001c8db  dir_remove                        202   2  Init  RX  ff.c.o
  0001c8dc-0001c999  uart_rx_dma_autorun               190   2  Init  RX  uart_dma.o
  0001c99a-0001ca4d  sdmmchost_send_command            180   2  Init  RX  hpm_sdmmc_host.c.o
  0001ca4e-0001cb19  ComputeDelSenbb                   204   2  Init  RX  navi.o
  0001cb1a-0001cbdb  test_gpio_input_interrupt         194   2  Init  RX  main.o
  0001cbdc-0001cc9d  sdxc_init                         194   2  Init  RX  hpm_sdxc_drv.c.o
  0001cc9e-0001cd5f  find_volume                       194   2  Init  RX  ff.c.o
  0001cd60-0001ce25  ff_queue_push                     198   2  Init  RX  ff_queue.o
  0001ce26-0001ced1  LEDIndicator                      172   2  Init  RX  INS_Init.o
  0001ced2-0001cf73  INS912AlgorithmEntry              162   2  Init  RX  InsTestingEntry.o
  0001cf74-0001d037  ComputeXk                         196   2  Init  RX  kalman.o
  0001d038-0001d0fd  ff_uni2oem                        198   2  Init  RX  ffunicode.c.o
  0001d0fe-0001d1a9  sd_init                           172   2  Init  RX  hpm_sdmmc_sd.c.o
  0001d1aa-0001d24b  SdFileOperateTypeSet              162   2  Init  RX  sd_fatfs.o
  0001d24c-0001d30f  ComputeG                          196   2  Init  RX  navi.o
  0001d310-0001d3cb  ComputeWnbb                       188   2  Init  RX  navi.o
  0001d3cc-0001d487  ComputeCen                        188   2  Init  RX  align.o
  0001d488-0001d52d  ComputeVi                         166   2  Init  RX  align.o
  0001d52e-0001d5df  pnavout_set                       178   2  Init  RX  InsTestingEntry.o
  0001d5e0-0001d68b  ComputeSib0                       172   2  Init  RX  dynamic_align.o
  0001d68c-0001d703  WriteFileToSd                     120   2  Init  RX  sd_fatfs.o
  0001d704-0001d79f  DeleteFileFromSd                  156   2  Init  RX  sd_fatfs.o
  0001d7a0-0001d845  get_ldnumber                      166   2  Init  RX  ff.c.o
  0001d846-0001d8e3  sdxc_set_data_timeout             158   2  Init  RX  hpm_sdxc_drv.c.o
  0001d8e4-0001d981  WriteCOM3FileToSd                 158   2  Init  RX  sd_fatfs.o
  0001d982-0001da19  sd_transfer                       152   2  Init  RX  hpm_sdmmc_sd.c.o
  0001da1a-0001dab1  dir_clear                         152   2  Init  RX  ff.c.o
  0001dab2-0001db45  CloseFileToSd                     148   2  Init  RX  sd_fatfs.o
  0001db46-0001dbe3  sd_convert_data_endian            158   2  Init  RX  hpm_sdmmc_sd.c.o
  0001dbe4-0001dc85  Mat_Mul                           162   2  Init  RX  matvecmath.o
  0001dc86-0001dd21  ComputePkk_1_Step2                156   2  Init  RX  kalman.o
  0001dd22-0001ddbd  xname_sum                         156   2  Init  RX  ff.c.o
  0001ddbe-0001de59  sdmmchost_is_card_detected
                                                       156   2  Init  RX  hpm_sdmmc_host.c.o
  0001de5a-0001def9  sd_disk_ioctl                     160   2  Init  RX  hpm_sdmmc_disk.c.o
  0001defa-0001df99  DynamicInertialSysAlign_Init
                                                       160   2  Init  RX  dynamic_align.o
  0001df9a-0001e02f  UartIrqSendMsg                    150   2  Init  RX  uart.o
  0001e030-0001e0bf  sdxc_set_adma_table_config
                                                       144   2  Init  RX  hpm_sdxc_drv.c.o
  0001e0c0-0001e15b  SysVarDefaultSet                  156   2  Init  RX  navi.o
  0001e15c-0001e1f3  InertialSysAlign_Init             152   2  Init  RX  align.o
  0001e1f4-0001e28f  ComputeRmRn                       156   2  Init  RX  navi.o
  0001e290-0001e329  sd_decode_scr                     154   2  Init  RX  hpm_sdmmc_sd.c.o
  0001e32a-0001e3ab  get_fpgadata_after_otherDataDo
                                                       130   2  Init  RX  fpgad.o
  0001e3ac-0001e443  ff_queue_get_buffer_by_sector
                                                       152   2  Init  RX  ff_queue.o
  0001e444-0001e4cb  sd_set_bus_width                  136   2  Init  RX  hpm_sdmmc_sd.c.o
  0001e4cc-0001e553  ff_queue_poll                     136   2  Init  RX  ff_queue.o
  0001e554-0001e5d3  f_mount                           128   2  Init  RX  ff.c.o
  0001e5d4-0001e659  sync_window                       134   2  Init  RX  ff.c.o
  0001e65a-0001e6c9  set_pwm_waveform_edge_aligned_frequency
                                                       112   2  Init  RX  timer.o
  0001e6ca-0001e755  UpdateStop_SetHead                140   2  Init  RX  SetParaBao.o
  0001e756-0001e7e1  UpdateStart_SetHead               140   2  Init  RX  SetParaBao.o
  0001e7e2-0001e86d  UpdateSend_SetHead                140   2  Init  RX  SetParaBao.o
  0001e86e-0001e8f9  UpdateEnd_SetHead                 140   2  Init  RX  SetParaBao.o
  0001e8fa-0001e985  SendPara_SetHead                  140   2  Init  RX  SetParaBao.o
  0001e986-0001ea11  ReadPara4_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ea12-0001ea9d  ReadPara3_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ea9e-0001eb29  ReadPara2_SetHead                 140   2  Init  RX  SetParaBao.o
  0001eb2a-0001ebb5  ReadPara1_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ebb6-0001ec41  ReadPara0_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ec42-0001eccb  sdxc_transfer_cb                  138   2  Init  RX  ff_queue.o
  0001eccc-0001ed4f  UartIrqInit                       132   2  Init  RX  uart.o
  0001ed50-0001edaf  INS_Init                           96   2  Init  RX  INS_Init.o
  0001edb0-0001ee29  WriteCom3FileOpenFromSd           122   2  Init  RX  sd_fatfs.o
  0001ee2a-0001eea3  WriteBB00FileOpenFromSd           122   2  Init  RX  sd_fatfs.o
  0001eea4-0001ef27  sdxc_set_transfer_config          132   2  Init  RX  hpm_sdxc_drv.c.o
  0001ef28-0001ef8b  sduart_recv_polling               100   2  Init  RX  uart.o
  0001ef8c-0001f005  load_obj_xdir                     122   2  Init  RX  ff.c.o
  0001f006-0001f087  Vec_Cross                         130   2  Init  RX  matvecmath.o
  0001f088-0001f0fd  SdFileReadOperate                 118   2  Init  RX  sd_fatfs.o
  0001f0fe-0001f167  sdmmc_enable_auto_tuning          106   2  Init  RX  hpm_sdmmc_common.c.o
  0001f168-0001f1df  sd_send_csd                       120   2  Init  RX  hpm_sdmmc_sd.c.o
  0001f1e0-0001f253  sd_mount_fs                       116   2  Init  RX  sd_fatfs.o
  0001f254-0001f2c7  ReadCom3FileOpenFromSd            116   2  Init  RX  sd_fatfs.o
  0001f2c8-0001f33b  ReadBB00FileOpenFromSd            116   2  Init  RX  sd_fatfs.o
  0001f33c-0001f3b9  sdxc_set_dma_config               126   2  Init  RX  hpm_sdxc_drv.c.o
  0001f3ba-0001f42f  sd_mkfs                           118   2  Init  RX  sd_fatfs.o
  0001f430-0001f4ab  ComputeDeg_Ex                     124   2  Init  RX  navi.o
  0001f4ac-0001f511  test_uart_recv_polling            102   2  Init  RX  uart.o
  0001f512-0001f56f  sdmmchost_set_cardclk_delay_chain
                                                        94   2  Init  RX  hpm_sdmmc_host.c.o
  0001f570-0001f5e9  hpm_csr_get_core_mcycle           122   2  Init  RX  hpm_sdmmc_osal.o
  0001f5ea-0001f663  hpm_csr_get_core_mcycle           122   2  Init  RX  hpm_sdmmc_sd.c.o
  0001f664-0001f6dd  SaveINSData                       122   2  Init  RX  kalman.o
  0001f6de-0001f751  KalPredict                        116   2  Init  RX  kalman.o
  0001f752-0001f7ab  sdxc_perform_auto_tuning           90   2  Init  RX  hpm_sdxc_drv.c.o
  0001f7ac-0001f81d  sd_all_send_cid                   114   2  Init  RX  hpm_sdmmc_sd.c.o
  0001f81e-0001f88b  SendVersionInfo                   110   2  Init  RX  datado.o
  0001f88c-0001f8fb  sdmmc_select_card                 112   2  Init  RX  hpm_sdmmc_common.c.o
  0001f8fc-0001f96b  sd_error_recovery                 112   2  Init  RX  hpm_sdmmc_sd.c.o
  0001f96c-0001f9df  ComputeXkk_1                      116   2  Init  RX  kalman.o
  0001f9e0-0001fa51  gptmr_channel_get_default_config
                                                       114   2  Init  RX  hpm_gptmr_drv.c.o
  0001fa52-0001fabf  fill_last_frag                    110   2  Init  RX  ff.c.o
  0001fac0-0001fb2f  sdxc_reset                        112   2  Init  RX  hpm_sdxc_drv.c.o
  0001fb30-0001fb9b  sd_send_if_cond                   108   2  Init  RX  hpm_sdmmc_sd.c.o
  0001fb9c-0001fc07  UpdateStop_SetEnd                 108   2  Init  RX  SetParaBao.o
  0001fc08-0001fc73  UpdateStart_SetEnd                108   2  Init  RX  SetParaBao.o
  0001fc74-0001fcdf  UpdateSend_SetEnd                 108   2  Init  RX  SetParaBao.o
  0001fce0-0001fd4b  UpdateEnd_SetEnd                  108   2  Init  RX  SetParaBao.o
  0001fd4c-0001fdb7  SendPara_SetEnd                   108   2  Init  RX  SetParaBao.o
  0001fdb8-0001fe23  ReadPara4_SetEnd                  108   2  Init  RX  SetParaBao.o
  0001fe24-0001fe8f  ReadPara3_SetEnd                  108   2  Init  RX  SetParaBao.o
  0001fe90-0001fefb  ReadPara2_SetEnd                  108   2  Init  RX  SetParaBao.o
  0001fefc-0001ff67  ReadPara1_SetEnd                  108   2  Init  RX  SetParaBao.o
  0001ff68-0001ffd3  ReadPara0_SetEnd                  108   2  Init  RX  SetParaBao.o
  0001ffd4-00020041  xdir_sum                          110   2  Init  RX  ff.c.o
  00020042-000200ab  validate                          106   2  Init  RX  ff.c.o
  000200ac-0002010f  sd_app_cmd_send_cond_op           100   2  Init  RX  hpm_sdmmc_sd.c.o
  00020110-00020169  f_close                            90   2  Init  RX  ff.c.o
  0002016a-000201d1  disk_ioctl                        104   2  Init  RX  diskio.c.o
  000201d2-00020239  ComputeVib0                       104   2  Init  RX  align.o
  0002023a-00020299  sd_app_cmd_set_write_block_erase_count
                                                        96   2  Init  RX  hpm_sdmmc_sd.c.o
  0002029a-000202ff  SDUartIrqInit                     102   2  Init  RX  uart.o
  00020300-0002035d  move_window                        94   2  Init  RX  ff.c.o
  0002035e-000203c1  fill_first_frag                   100   2  Init  RX  ff.c.o
  000203c2-00020427  st_dword                          102   2  Init  RX  ff.c.o
  00020428-00020489  sd_send_card_status                98   2  Init  RX  hpm_sdmmc_sd.c.o
  0002048a-000204ef  myget_16bit_D64                   102   2  Init  RX  readpaoche.o
  000204f0-00020551  disk_write                         98   2  Init  RX  diskio.c.o
  00020552-000205b1  disk_sync_read                     96   2  Init  RX  diskio.c.o
  000205b2-00020613  disk_read                          98   2  Init  RX  diskio.c.o
  00020614-00020675  disk_async_write                   98   2  Init  RX  diskio.c.o
  00020676-000206d3  Read_And_Check_GNSS_Data           94   2  Init  RX  read_and_check_gnss_data.o
  000206d4-00020739  BindDefaultSet_by_GNSS            102   2  Init  RX  navi.o
  0002073a-00020795  ComputePkk_1_Step1                 92   2  Init  RX  kalman.o
  00020796-000207ef  sd_disk_status                     90   2  Init  RX  hpm_sdmmc_disk.c.o
  000207f0-00020851  sd_be2le                           98   2  Init  RX  hpm_sdmmc_sd.c.o
  00020852-000208ab  init_alloc_info                    90   2  Init  RX  ff.c.o
  000208ac-0002090b  sdxc_select_voltage                96   2  Init  RX  hpm_sdxc_drv.c.o
  0002090c-00020967  sdmmc_send_application_command
                                                        92   2  Init  RX  hpm_sdmmc_common.c.o
  00020968-000209c1  sd_send_rca                        90   2  Init  RX  hpm_sdmmc_sd.c.o
  000209c2-00020a21  core_local_mem_to_sys_address
                                                        96   2  Init  RX  uart_dma.o
  00020a22-00020a81  core_local_mem_to_sys_address
                                                        96   2  Init  RX  hpm_sdmmc_disk.c.o
  00020a82-00020ae1  core_local_mem_to_sys_address
                                                        96   2  Init  RX  hpm_sdmmc_port.c.o
  00020ae2-00020b39  sdxc_set_data_bus_width            88   2  Init  RX  hpm_sdxc_drv.c.o
  00020b3a-00020b89  sdmmchost_enable_sdio_interrupt
                                                        80   2  Init  RX  hpm_sdmmc_host.c.o
  00020b8a-00020be5  fpgadata_Predo_chen_algParmCash
                                                        92   2  Init  RX  InsTestingEntry.o
  00020be6-00020c37  sd_disk_sync_read                  82   2  Init  RX  hpm_sdmmc_disk.c.o
  00020c38-00020c8d  sd_check_card_parameters           86   2  Init  RX  hpm_sdmmc_sd.c.o
  00020c8e-00020ce7  ComputeWenn                        90   2  Init  RX  navi.o
  00020ce8-00020d3b  ff_queue_init                      84   2  Init  RX  ff_queue.o
  00020d3c-00020d93  ComputeWien                        88   2  Init  RX  navi.o
  00020d94-00020de9  uart_default_config                86   2  Init  RX  hpm_uart_drv.c.o
  00020dea-00020e3b  sd_host_init                       82   2  Init  RX  hpm_sdmmc_sd.c.o
  00020e3c-00020e91  UpdateAlignPosAndVn                86   2  Init  RX  dynamic_align.o
  00020e92-00020edd  set_pwm_waveform_edge_aligned_duty
                                                        76   2  Init  RX  timer.o
  00020ede-00020f31  sdxc_set_speed_mode                84   2  Init  RX  hpm_sdxc_drv.c.o
  00020f32-00020f81  sdmmc_set_block_size               80   2  Init  RX  hpm_sdmmc_common.c.o
  00020f82-00020fd5  gpiom_set_pin_controller           84   2  Init  RX  bsp_gpio.o
  00020fd6-00021029  gpiom_set_pin_controller           84   2  Init  RX  main.o
  0002102a-0002106d  ReadFileOpenFromSd                 68   2  Init  RX  sd_fatfs.o
  0002106e-000210ad  AlgorithmDo                        64   2  Init  RX  InsTestingEntry.o
  000210ae-000210fb  sdmmc_go_idle_state                78   2  Init  RX  hpm_sdmmc_common.c.o
  000210fc-00021145  sd_send_cmd                        74   2  Init  RX  hpm_sdmmc_sd.c.o
  00021146-00021197  gpio_write_pin                     82   2  Init  RX  INS_Init.o
  00021198-000211e9  Mat_Tr                             82   2  Init  RX  matvecmath.o
  000211ea-00021239  sdxc_enable_inverse_clock          80   2  Init  RX  hpm_sdxc_drv.c.o
  0002123a-00021289  sdxc_enable_inverse_clock          80   2  Init  RX  hpm_sdmmc_host.c.o
  0002128a-000212cd  sdmmchost_wait_xfer_done           68   2  Init  RX  hpm_sdmmc_host.c.o
  000212ce-00021315  sdmmchost_wait_idle                72   2  Init  RX  hpm_sdmmc_host.c.o
  00021316-00021359  sdmmchost_wait_command_done
                                                        68   2  Init  RX  hpm_sdmmc_host.c.o
  0002135a-0002139d  sdmmchost_get_data_pin_level
                                                        68   2  Init  RX  hpm_sdmmc_host.c.o
  0002139e-000213eb  uart_send_byte                     78   2  Init  RX  hpm_uart_drv.c.o
  000213ec-0002142d  st_clust                           66   2  Init  RX  ff.c.o
  0002142e-00021473  norflash_init                      70   2  Init  RX  flash.o
  00021474-000214c1  GNSS_Lost_Time                     78   2  Init  RX  InsTestingEntry.o
  000214c2-0002150d  sum_sfn                            76   2  Init  RX  ff.c.o
  0002150e-00021555  sdmmchost_select_voltage           72   2  Init  RX  hpm_sdmmc_host.c.o
  00021556-0002159b  sd_switch_voltage                  70   2  Init  RX  hpm_sdmmc_sd.c.o
  0002159c-000215e5  xor_check                          74   2  Init  RX  frame_analysis.o
  000215e6-00021627  uart_tx_dma                        66   2  Init  RX  uart_dma.o
  00021628-0002166d  putc_flush                         70   2  Init  RX  ff.c.o
  0002166e-000216b7  gptmr_channel_reset_count          74   2  Init  RX  timer.o
  000216b8-000216df  fpgadata_Predo                     40   2  Init  RX  InsTestingEntry.o
  000216e0-0002171b  ComputeLeverArmVn                  60   2  Init  RX  navi.o
  0002171c-0002175b  ld_clust                           64   2  Init  RX  ff.c.o
  0002175c-00021787  fpgadata_Predo_chen                44   2  Init  RX  InsTestingEntry.o
  00021788-000217c9  disk_status                        66   2  Init  RX  diskio.c.o
  000217ca-0002180b  disk_initialize                    66   2  Init  RX  diskio.c.o
  0002180c-0002184f  sdxc_set_cardclk_delay_chain
                                                        68   2  Init  RX  hpm_sdmmc_host.c.o
  00021850-00021893  sdxc_enable_sd_clock               68   2  Init  RX  hpm_sdxc_drv.c.o
  00021894-000218d7  sdxc_enable_sd_clock               68   2  Init  RX  hpm_sdmmc_host.c.o
  000218d8-00021917  sd_select_card                     64   2  Init  RX  hpm_sdmmc_sd.c.o
  00021918-00021957  sd_disk_async_write                64   2  Init  RX  hpm_sdmmc_disk.c.o
  00021958-0002199b  ld_dword                           68   2  Init  RX  ff.c.o
  0002199c-000219df  dbc_1st                            68   2  Init  RX  ff.c.o
  000219e0-00021a23  comm_param_setbits                 68   2  Init  RX  computerFrameParse.o
  00021a24-00021a67  TransHeading0to360                 68   2  Init  RX  navi.o
  00021a68-00021aab  GNSS_Last_TIME                     68   2  Init  RX  InsTestingEntry.o
  00021aac-00021aed  st_word                            66   2  Init  RX  ff.c.o
  00021aee-00021b23  sdxc_tuning_error_recovery
                                                        54   2  Init  RX  hpm_sdxc_drv.c.o
  00021b24-00021b65  gptmr_update_cmp                   66   2  Init  RX  timer.o
  00021b66-00021ba5  uart_flush                         64   2  Init  RX  hpm_uart_drv.c.o
  00021ba6-00021be5  ff_queue_is_full                   64   2  Init  RX  ff_queue.o
  00021be6-00021c25  CorrectVn                          64   2  Init  RX  kalman.o
  00021c26-00021c5b  sdmmchost_set_card_bus_width
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  00021c5c-00021c95  sdmmchost_check_host_availability
                                                        58   2  Init  RX  hpm_sdmmc_host.c.o
  00021c96-00021ccd  Drv_FlashWrite                     56   2  Init  RX  FirmwareUpdateFile.o
  00021cce-00021d09  uart_modem_config                  60   2  Init  RX  hpm_uart_drv.c.o
  00021d0a-00021d45  sdxc_wait_card_active              60   2  Init  RX  hpm_sdmmc_host.c.o
  00021d46-00021d81  sdmmc_get_card_and_aligned_buf_info
                                                        60   2  Init  RX  hpm_sdmmc_disk.c.o
  00021d82-00021db9  sd_disk_write                      56   2  Init  RX  hpm_sdmmc_disk.c.o
  00021dba-00021df1  sd_disk_read                       56   2  Init  RX  hpm_sdmmc_disk.c.o
  00021df2-00021e2d  rom_xpi_nor_read                   60   2  Init  RX  flash.o
  00021e2e-00021e69  dmamux_config                      60   2  Init  RX  uart_dma.o
  00021e6a-00021ea5  dma_default_channel_config
                                                        60   2  Init  RX  hpm_dma_drv.c.o
  00021ea6-00021ed1  DeviceInit                         44   2  Init  RX  datado.o
  00021ed2-00021f0b  sdxc_stop_clock_during_phase_code_change
                                                        58   2  Init  RX  hpm_sdmmc_common.c.o
  00021f0c-00021f3d  norflash_write                     50   2  Init  RX  flash.o
  00021f3e-00021f6d  norflash_read                      48   2  Init  RX  flash.o
  00021f6e-00021fa7  ld_word                            58   2  Init  RX  ff.c.o
  00021fa8-00021fdb  uart_dma_output                    52   2  Init  RX  uart_dma.o
  00021fdc-0002200b  sdxc_send_command                  48   2  Init  RX  hpm_sdxc_drv.c.o
  0002200c-00022043  sdxc_enable_auto_tuning            56   2  Init  RX  hpm_sdxc_drv.c.o
  00022044-00022077  sdmmchost_set_speed_mode           52   2  Init  RX  hpm_sdmmc_host.c.o
  00022078-000220af  myget_16bit_D32                    56   2  Init  RX  readpaoche.o
  000220b0-000220e7  crc_verify_8bit                    56   2  Init  RX  app_tool.o
  000220e8-0002211f  app_accum_verify_8bit              56   2  Init  RX  app_tool.o
  00022120-0002214f  ComputeLeverArmSn                  48   2  Init  RX  navi.o
  00022150-00022185  sdxc_set_post_change_delay
                                                        54   2  Init  RX  hpm_sdmmc_common.c.o
  00022186-000221bb  sdxc_is_inverse_clock_enabled
                                                        54   2  Init  RX  hpm_sdxc_drv.c.o
  000221bc-000221f1  sdxc_is_inverse_clock_enabled
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  000221f2-00022227  sdxc_enable_interrupt_status
                                                        54   2  Init  RX  hpm_sdxc_drv.c.o
  00022228-0002225d  sdxc_enable_interrupt_status
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  0002225e-00022293  sdxc_enable_interrupt_signal
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  00022294-000222c9  myget_16bit_I32                    54   2  Init  RX  readpaoche.o
  000222ca-000222f7  f_chdrive                          46   2  Init  RX  ff.c.o
  000222f8-0002232d  clst2sect                          54   2  Init  RX  ff.c.o
  0002232e-0002235d  Drv_FlashErase                     48   2  Init  RX  FirmwareUpdateFile.o
  0002235e-00022391  sdxc_get_data_bus_width            52   2  Init  RX  hpm_sdxc_drv.c.o
  00022392-000223c5  sdmmchost_set_card_clock           52   2  Init  RX  hpm_sdmmc_host.c.o
  000223c6-000223ef  norflash_erase_sector              42   2  Init  RX  flash.o
  000223f0-0002241b  Uart_SendMsg                       44   2  Init  RX  bsp_fmc.o
  0002241c-0002244b  sdxc_enable_tm_clock               48   2  Init  RX  hpm_sdxc_drv.c.o
  0002244c-0002247b  sdxc_enable_power                  48   2  Init  RX  hpm_sdmmc_common.c.o
  0002247c-000224ab  sdxc_enable_power                  48   2  Init  RX  hpm_sdmmc_host.c.o
  000224ac-000224d1  Pwm_Init                           38   2  Init  RX  timer.o
  000224d2-000224fd  gptmr_stop_counter                 44   2  Init  RX  timer.o
  000224fe-00022529  gptmr_start_counter                44   2  Init  RX  timer.o
  0002252a-00022555  gpio_set_pin_output                44   2  Init  RX  bsp_gpio.o
  00022556-00022581  gpio_set_pin_input                 44   2  Init  RX  main.o
  00022582-000225ad  gpio_enable_pin_interrupt          44   2  Init  RX  main.o
  000225ae-000225d9  gpio_clear_pin_interrupt_flag
                                                        44   2  Init  RX  main.o
  000225da-000225f3  get_fpgadata                       26   2  Init  RX  fpgad.o
  000225f4-00022613  SdFileWriteOperate                 32   2  Init  RX  sd_fatfs.o
  00022614-00022635  Exti_Init                          34   2  Init  RX  main.o
  00022636-0002265b  sd_set_max_current                 38   2  Init  RX  hpm_sdmmc_sd.c.o
  0002265c-00022685  gpio_toggle_pin                    42   2  Init  RX  main.o
  00022686-000226af  ff_queue_is_empty                  42   2  Init  RX  ff_queue.o
  000226b0-000226d1  uart_get_current_recv_remaining_size
                                                        34   2  Init  RX  uart_dma.o
  000226d2-000226ef  sdmmchost_wait_card_active
                                                        30   2  Init  RX  hpm_sdmmc_host.c.o
  000226f0-00022717  hpm_sdmmc_osal_event_create
                                                        40   2  Init  RX  hpm_sdmmc_osal.o
  00022718-0002273d  putc_init                          38   2  Init  RX  ff.c.o
  0002273e-00022763  myget_8bit_I16                     38   2  Init  RX  readpaoche.o
  00022764-00022789  hpm_sdmmc_osal_delay               38   2  Init  RX  hpm_sdmmc_osal.o
  0002278a-000227a7  ff_handle_poll                     30   2  Init  RX  ff_queue.o
  000227a8-000227cb  xsum32                             36   2  Init  RX  ff.c.o
  000227cc-000227ef  sdxc_get_default_cardclk_delay_chain
                                                        36   2  Init  RX  hpm_sdmmc_host.c.o
  000227f0-0002280f  sdmmc_get_sys_addr                 32   2  Init  RX  hpm_sdmmc_port.c.o
  00022810-00022833  gptmr_check_status                 36   2  Init  RX  timer.o
  00022834-00022851  sdmmchost_switch_to_3v3_as_needed
                                                        30   2  Init  RX  hpm_sdmmc_host.c.o
  00022852-00022873  sdmmchost_register_xfer_complete_callback
                                                        34   2  Init  RX  hpm_sdmmc_host.c.o
  00022874-00022891  sdmmchost_error_recovery           30   2  Init  RX  hpm_sdmmc_host.c.o
  00022892-000228a9  output_normal_do                   24   2  Init  RX  INS_Output.o
  000228aa-000228cb  init_gpio                          34   2  Init  RX  main.o
  000228cc-000228e1  EXTI3_IRQHandler                   22   2  Init  RX  gd32f4xx_it.o
  000228e2-000228fd  timer_Init                         28   2  Init  RX  timer.o
  000228fe-00022919  Drv_FlashRead                      28   2  Init  RX  FirmwareUpdateFile.o
  0002291a-00022937  sdxc_is_bus_idle                   30   2  Init  RX  hpm_sdxc_drv.c.o
  00022938-00022951  sd_is_card_present                 26   2  Init  RX  hpm_sdmmc_sd.c.o
  00022952-00022967  loopDoOther                        22   2  Init  RX  datado.o
  00022968-00022985  hpm_sdmmc_osal_event_clear
                                                        30   2  Init  RX  hpm_sdmmc_osal.o
  00022986-0002299d  uart4sendmsg                       24   2  Init  RX  gd32f4xx_it.o
  0002299e-000229b9  sdmmchost_is_voltage_switch_supported
                                                        28   2  Init  RX  hpm_sdmmc_host.c.o
  000229ba-000229cf  sdmmchost_delay_ms                 22   2  Init  RX  hpm_sdmmc_host.c.o
  000229d0-000229eb  gptmr_enable_irq                   28   2  Init  RX  timer.o
  000229ec-000229ff  gd_eval_com_init                   20   2  Init  RX  INS_Init.o
  00022a00-00022a0f  bsp_tim_init                       16   2  Init  RX  bsp_tim.o
  00022a10-00022a25  Led_Control                        22   2  Init  RX  main.o
  00022a26-00022a3f  sdxc_reset_tuning_engine           26   2  Init  RX  hpm_sdxc_drv.c.o
  00022a40-00022a59  sdxc_is_card_inserted              26   2  Init  RX  hpm_sdmmc_host.c.o
  00022a5a-00022a73  hpm_sdmmc_osal_event_set           26   2  Init  RX  hpm_sdmmc_osal.o
  00022a74-00022a87  Drv_SystemReset                    20   2  Init  RX  FirmwareUpdateFile.o
  00022a88-00022a9f  sdxc_execute_tuning                24   2  Init  RX  hpm_sdxc_drv.c.o
  00022aa0-00022ab7  sdmmchost_is_8bit_supported
                                                        24   2  Init  RX  hpm_sdmmc_host.c.o
  00022ab8-00022acb  protocol_send                      20   2  Init  RX  computerFrameParse.o
  00022acc-00022ae3  dma_get_remaining_transfer_size
                                                        24   2  Init  RX  uart_dma.o
  00022ae4-00022afb  delay_ms                           24   2  Init  RX  systick.o
  00022afc-00022b0f  Algorithm_before_otherDataDo
                                                        20   2  Init  RX  datado.o
  00022b10-00022b23  ACC_gyroreset_r_TAFEAG16_buf
                                                        20   2  Init  RX  InsTestingEntry.o
  00022b24-00022b39  get_uart_tx_idle                   22   2  Init  RX  uart_dma.o
  00022b3a-00022b4d  gptmr_clear_status                 20   2  Init  RX  timer.o
  00022b4e-00022b5f  sdxc_get_data7_4_level             18   2  Init  RX  hpm_sdmmc_host.c.o
  00022b60-00022b71  sdxc_get_data3_0_level             18   2  Init  RX  hpm_sdmmc_host.c.o
  00022b72-00022b83  sdxc_clear_interrupt_status
                                                        18   2  Init  RX  hpm_sdxc_drv.c.o
  00022b84-00022b95  sdxc_clear_interrupt_status
                                                        18   2  Init  RX  hpm_sdmmc_host.c.o
  00022b96-00022ba7  ppor_sw_reset                      18   2  Init  RX  FirmwareUpdateFile.o
  00022ba8-00022bb9  mchtmr_get_count                   18   2  Init  RX  sd_fatfs.o
  00022bba-00022bc9  sdxc_select_cardclk_delay_source
                                                        16   2  Init  RX  hpm_sdmmc_common.c.o
  00022bca-00022bd9  comm_read_currentFreq              16   2  Init  RX  computerFrameParse.o
  00022bda-00022be9  comm_axis_read                     16   2  Init  RX  computerFrameParse.o
  00022bea-00022bf7  sdxc_get_present_status            14   2  Init  RX  hpm_sdxc_drv.c.o
  00022bf8-00022c05  sdxc_get_interrupt_status          14   2  Init  RX  hpm_sdxc_drv.c.o
  00022c06-00022c13  sdxc_get_interrupt_status          14   2  Init  RX  hpm_sdmmc_host.c.o
  00022c14-00022c21  sdxc_get_interrupt_signal          14   2  Init  RX  hpm_sdmmc_host.c.o
  00022c22-00022c2d  sdxc_is_ddr50_supported            12   2  Init  RX  hpm_sdmmc_port.c.o
  00022c2e-00022c37  sd_deinit                          10   2  Init  RX  hpm_sdmmc_sd.c.o
  00022c38-00022c3b  get_fpgadata_before                 4   2  Init  RX  fpgad.o
  00022c3c-00022c3f  comm_nav_para_syn                   4   2  Init  RX  computerFrameParse.o
  00022c40-0007ffff  ( UNUSED .=.+381888 )         381 888   -  ----  -   -
  00080000-000800d7  s_sd                              216   8  Init  RW  hpm_sdmmc_disk.c.o
  000800d8-0008011b  test_uart                          68   4  Init  RW  uart.o
  0008011c-0008015f  sd_uart                            68   4  Init  RW  uart.o
  00080160-00080173  __SEGGER_RTL_global_locale
                                                        20   4  Init  RW  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  00080174-00080177  xgpsTrueTime                        4   4  Zero  ZI  frame_analysis.o
  00080178-0008017f  start_ticks                         8   8  Zero  ZI  sd_fatfs.o
  00080180-0008417f  s_sd_aligned_buf               16 384  64  Zero  ZI  hpm_sdmmc_disk.c.o
  00084180-00087c97  g_Kalman                       15 128   8  Zero  ZI  main.o
  00087c98-0008a46f  g_Compen                       10 200   8  Zero  ZI  main.o
  0008a470-0008af47  g_Navi                          2 776   8  Zero  ZI  main.o
  0008af48-0008b32f  g_DynamicInertialSysAlign       1 000   8  Zero  ZI  main.o
  0008b330-0008b667  g_InertialSysAlign                824   8  Zero  ZI  main.o
  0008b668-0008b8b7  s_fileCOM3                        592   8  Zero  ZI  sd_fatfs.o
  0008b8b8-0008bb07  s_fileBB00                        592   8  Zero  ZI  sd_fatfs.o
  0008bb08-0008bd2f  paochedata                        552   8  Zero  ZI  InsTestingEntry.o
  0008bd30-0008bec7  g_SysVar                          408   8  Zero  ZI  main.o
  0008bec8-0008bf7f  g_Align                           184   8  Zero  ZI  main.o
  0008bf80-0008bfff  g_SelfTest                        128   8  Zero  ZI  main.o
  0008c000-0008c06f  gnavout                           112   8  Zero  ZI  INS_Data.o
  0008c070-0008c0df  g_GNSSData_In_Use                 112   8  Zero  ZI  main.o
  0008c0e0-0008c127  Gw                                 72   8  Zero  ZI  InsTestingEntry.o
  0008c128-0008c16f  Aw                                 72   8  Zero  ZI  InsTestingEntry.o
  0008c170-0008c1af  g_InitBind                         64   8  Zero  ZI  main.o
  0008c1b0-0008c1c7  r_LastGyro                         24   8  Zero  ZI  InsTestingEntry.o
  0008c1c8-0008c1df  r_Gyro                             24   8  Zero  ZI  InsTestingEntry.o
  0008c1e0-0008c1f7  gw                                 24   8  Zero  ZI  InsTestingEntry.o
  0008c1f8-0008c20f  gWm                                24   8  Zero  ZI  InsTestingEntry.o
  0008c210-0008c227  gW_real                            24   8  Zero  ZI  InsTestingEntry.o
  0008c228-0008c23f  aw                                 24   8  Zero  ZI  InsTestingEntry.o
  0008c240-0008c257  aWm                                24   8  Zero  ZI  InsTestingEntry.o
  0008c258-0008c26f  aW_real                            24   8  Zero  ZI  InsTestingEntry.o
  0008c270-0008c287  LastAcc                            24   8  Zero  ZI  InsTestingEntry.o
  0008c288-0008c29f  Acc                                24   8  Zero  ZI  InsTestingEntry.o
  0008c2a0-0008c2a7  end_ticks                           8   8  Zero  ZI  sd_fatfs.o
  0008c2a8-0009ab7f  gdriverdatalist                59 608   4  Zero  ZI  gdwatch.o
  0009ab80-0009bb81  grxbuffer                       4 098   4  Zero  ZI  uart.o
  0009bb82-0009bb83  tCnt.0                              2   2  Zero  ZI  InsTestingEntry.o
  0009bb84-0009c383  hello_str                       2 048   4  Zero  ZI  sd_fatfs.o
  0009c384-0009cb83  fpgatesttxt                     2 048   4  Zero  ZI  INS_Output.o
  0009cb84-0009cf83  gframeParsebuf                  1 024   4  Zero  ZI  uart.o
  0009cf84-0009d1e3  DirBuf                            608   4  Zero  ZI  ff.c.o
  0009d1e4-0009d437  hSetting                          596   4  Zero  ZI  computerFrameParse.o
  0009d438-0009d683  s_sd_disk                         588   4  Zero  ZI  sd_fatfs.o
  0009d684-0009d8cd  stSetPara                         586   4  Zero  ZI  SetParaBao.o
  0009d8ce-0009d8cf  tCnt.0                              2   2  Zero  ZI  INS_Output.o
  0009d8d0-0009db11  r_TAFEAG8_buf                     578   4  Zero  ZI  InsTestingEntry.o
  0009db12-0009db13  tCnt.0                              2   2  Zero  ZI  sd_fatfs.o
  0009db14-0009dd13  work                              512   4  Zero  ZI  sd_fatfs.o
  0009dd14-0009df13  LfnBuf                            512   4  Zero  ZI  ff.c.o
  0009df14-0009e0a3  gfpgadata                         400   4  Zero  ZI  fpgad.o
  0009e0a4-0009e1f3  gfpgadataPredoSend                336   4  Zero  ZI  InsTestingEntry.o
  0009e1f4-0009e343  BB00SdData                        336   4  Zero  ZI  InsTestingEntry.o
  0009e344-0009e453  gpagedata                         272   4  Zero  ZI  fpgad.o
  0009e454-0009e561  gins912data                       270   4  Zero  ZI  InsTestingEntry.o
  0009e562-0009e563  SDCnt.0                             2   2  Zero  ZI  gd32f4xx_it.o
  0009e564-0009e671  ginputdata                        270   4  Zero  ZI  InsTestingEntry.o
  0009e672-0009e673  Fsid                                2   2  Zero  ZI  ff.c.o
  0009e674-0009e773  s_xpi_nor_config                  256   4  Zero  ZI  flash.o
  0009e774-0009e863  hINSData                          240   4  Zero  ZI  INS_Data.o
  0009e864-0009e8f7  ggpsorgdata                       148   4  Zero  ZI  frame_analysis.o
  0009e8f8-0009e981  gdriversettings                   138   4  Zero  ZI  gdwatch.o
  0009e982-0009e983  COM3Flag.3                          2   2  Zero  ZI  sd_fatfs.o
  0009e984-0009e9e7  rs422_frame                       100   4  Zero  ZI  frame_analysis.o
  0009e9e8-0009ea13  galgrithomresultTx                 44   4  Zero  ZI  gdwatch.o
  0009ea14-0009ea3b  FatFs                              40   4  Zero  ZI  ff.c.o
  0009ea3c-0009ea61  gcanInfo                           38   4  Zero  ZI  fpgad.o
  0009ea62-0009ea63  BB00Flag.2                          2   2  Zero  ZI  sd_fatfs.o
  0009ea64-0009ea83  infoArr                            32   4  Zero  ZI  fpgad.o
  0009ea84-0009ea9b  __SEGGER_RTL_aSigTab               24   4  Zero  ZI  execops.o (libc_rv32gc_d_balanced.a)
  0009ea9c-0009eab3  StrMiddleWare                      24   4  Zero  ZI  InsTestingEntry.o
  0009eab4-0009eac7  canin                              20   4  Zero  ZI  InsTestingEntry.o
  0009eac8-0009ead3  r_fog                              12   4  Zero  ZI  InsTestingEntry.o
  0009ead4-0009eadf  r_acc                              12   4  Zero  ZI  InsTestingEntry.o
  0009eae0-0009eae3  xgpsTime                            4   4  Zero  ZI  frame_analysis.o
  0009eae4-0009eae7  uiOffsetAddr.0                      4   4  Zero  ZI  SetParaBao.o
  0009eae8-0009eaeb  uiLen.0                             4   4  Zero  ZI  uart.o
  0009eaec-0009eaef  time_base_periodic_cnt              4   4  Zero  ZI  bsp_tim.o
  0009eaf0-0009eaf3  time_base_20ms_periodic_cnt
                                                         4   4  Zero  ZI  bsp_tim.o
  0009eaf4-0009eaf7  time_base_20ms_Flag                 4   4  Zero  ZI  bsp_tim.o
  0009eaf8-0009eafb  time_base_100ms_periodic_cnt
                                                         4   4  Zero  ZI  bsp_tim.o
  0009eafc-0009eaff  time_base_100ms_Flag                4   4  Zero  ZI  bsp_tim.o
  0009eb00-0009eb03  test_count                          4   4  Zero  ZI  sd_fatfs.o
  0009eb04-0009eb07  read_ms                             4   4  Zero  ZI  sd_fatfs.o
  0009eb08-0009eb0b  read_count                          4   4  Zero  ZI  sd_fatfs.o
  0009eb0c-0009eb0f  hpm_core_clock                      4   4  Zero  ZI  hpm_clock_drv.c.o
  0009eb10-0009eb13  grxst                               4   4  Zero  ZI  uart.o
  0009eb14-0009eb17  grxlen                              4   4  Zero  ZI  uart.o
  0009eb18-0009eb1b  gins912outputmode                   4   4  Zero  ZI  INS_Output.o
  0009eb1c-0009eb1f  ggdworgdata_packet                  4   4  Zero  ZI  gdwatch.o
  0009eb20-0009eb23  gframeindex                         4   4  Zero  ZI  fpgad.o
  0009eb24-0009eb27  gfpgasenddatalen                    4   4  Zero  ZI  fpgad.o
  0009eb28-0009eb2b  gdriverspacket                      4   4  Zero  ZI  gdwatch.o
  0009eb2c-0009eb2f  g_console_uart                      4   4  Zero  ZI  hpm_debug_console.c.o
  0009eb30-0009eb33  g_DataOutTime                       4   4  Zero  ZI  SetParaBao.o
  0009eb34-0009eb37  g_CAN_Timeout_Start_flag            4   4  Zero  ZI  INS_Init.o
  0009eb38-0009eb3b  g_CAN_Timeout_Cnt                   4   4  Zero  ZI  INS_Init.o
  0009eb3c-0009eb3f  fpga_syn_count                      4   4  Zero  ZI  INS_Init.o
  0009eb40-0009eb43  fpga_syn_btw_last                   4   4  Zero  ZI  fpgad.o
  0009eb44-0009eb47  fpga_syn_btw                        4   4  Zero  ZI  fpgad.o
  0009eb48-0009eb4b  fpga_syn_NAVI_btw                   4   4  Zero  ZI  fpgad.o
  0009eb4c-0009eb4f  fpga_loop_count                     4   4  Zero  ZI  INS_Init.o
  0009eb50-0009eb53  flag.1                              4   4  Zero  ZI  SetParaBao.o
  0009eb54-0009eb57  en                                  4   4  Zero  ZI  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0009eb58-0009eb5b  current_reload                      4   4  Zero  ZI  timer.o
  0009eb5c-0009eb5f  axisInfo                            4   4  Zero  ZI  frame_analysis.o
  0009eb60-0009eb63  __SEGGER_RTL_stdout_file            4   4  Zero  ZI  hpm_debug_console.c.o
  0009eb64-0009eb67  __SEGGER_RTL_locale_ptr             4   4  Zero  ZI  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0009eb68-0009eb6b  __SEGGER_RTL_heap_globals           4   4  Zero  ZI  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0009eb6c-0009eb6f  NaviCompute_do_count                4   4  Zero  ZI  datado.o
  0009eb70-0009eb70  inscanruning                        1   1  Zero  ZI  readpaoche.o
  0009eb71-0009eb71  init_done                           1   1  Zero  ZI  ff_queue.o
  0009eb72-0009eb72  has_card_initialized.0              1   1  Zero  ZI  hpm_sdmmc_disk.c.o
  0009eb73-0009eb73  g_ucSystemResetFlag                 1   1  Zero  ZI  SetParaBao.o
  0009eb74-0009eb74  g_UpdateSuccessful                  1   1  Zero  ZI  SetParaBao.o
  0009eb75-0009eb75  g_StartUpdateFirm                   1   1  Zero  ZI  main.o
  0009eb76-0009eb76  g_LEDIndicatorState                 1   1  Zero  ZI  INS_Data.o
  0009eb77-0009eb77  g_DataOutTypeFlag                   1   1  Zero  ZI  SetParaBao.o
  0009eb78-0009eb78  g_Com3WriteSdFlag                   1   1  Zero  ZI  uart.o
  0009eb79-0009eb79  g_BB00WriteSdFlag                   1   1  Zero  ZI  InsTestingEntry.o
  0009eb7a-0009eb7a  fpga_syn                            1   1  Zero  ZI  INS_Init.o
  0009eb7b-0009eb7b  fatfs_result                        1   1  Zero  ZI  sd_fatfs.o
  0009eb7c-0009eb7c  SetSdOperateType                    1   1  Zero  ZI  SetParaBao.o
  0009eb7d-0009eb7d  SetSdFlieType                       1   1  Zero  ZI  SetParaBao.o
  0009eb7e-0009eb7e  CurrVol                             1   1  Zero  ZI  ff.c.o
  0009eb7f-0009eb7f  rcv_state                           1   1  Init  RW  readpaoche.o
  0009eb80-000a7b84  test_uart_rx_buf               36 869   4  None  ZI  uart.o
  000a7b85-000a7b85  g_UpdateBackFlag                    1   1  Init  RW  SetParaBao.o
  000a7b86-000a7b87  ( UNUSED .=.+2 )                    2   -  ----  -   -
  000a7b88-000a7b88  dma_done                            1   4  None  RW  uart_dma.o
  000a7b89-000a7b8b  ( ALIGN .=.+3 )                     3   -  ----  -   -
  000a7b8c-000a7b8f  uiLastBaoInDex.2                    4   4  Init  RW  SetParaBao.o
  000a7b90-000a7b93  stdout                              4   4  Init  RW  hpm_debug_console.c.o
  000a7b94-000a7b97  gprotocol_send_baudrate             4   4  Init  RW  datado.o
  000a7b98-000a7b9b  Com3FileName                        4   4  Init  RW  sd_fatfs.o
  000a7b9c-000a7b9f  BB00FileName                        4   4  Init  RW  sd_fatfs.o
  000a7ba0-000a7ba3  __SEGGER_RTL_errno                  4   4  Zero  ZI  errno.o (libc_rv32gc_d_balanced.a)
  000a7ba4-000afea7  g_queue                        33 540   4  Init  ZI  hpm_sdmmc_disk.c.o
  000afea8-000b3ea7  [.bss.block.heap]              16 384   8  None  ZI  [ Linker created ]
  000b3ea8-000bbfff  ( UNUSED .=.+33112 )           33 112   -  ----  -   -
  000bc000-000bffff  [.bss.block.stack]             16 384  16  None  ZI  [ Linker created ]
  01100000-01100313  s_sd_host                         788   4  Zero  ZI  hpm_sdmmc_disk.c.o
  01100314-01100317  ( UNUSED .=.+4 )                    4   -  ----  -   -
  01100318-01100417  test_tx_descriptors               256   8  Init  ZI  uart.o
  01100418-8000cfff  ( UNUSED .=.+2129710056 )  2 129 710 056
                                                             -  ----  -   -
  8000d000-8000d00f  option                             16   4  Cnst  RO  board.c.o
  8000d010-8000dfff  ( UNUSED .=.+4080 )             4 080   -  ----  -   -
  8000e000-8000e00f  header                             16   4  Cnst  RO  hpm_bootheader.c.o
  8000e010-8000e08f  fw_info                           128   4  Cnst  RO  hpm_bootheader.c.o
  8000e090-8000ffff  ( UNUSED .=.+8048 )             8 048   -  ----  -   -
  80010000-80010091  _start                            146   2  Code  RX  startup.s.o
  80010092-80010093  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                         2   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  80010094-80010097  s_adc_clk_mux_node                  4   4  Cnst  RO  hpm_clock_drv.c.o
  80010098-800100f7  [.rodata]                          96   8  Cnst  RO  align.o
  800100f8-8001010f  [.rodata]                          24   8  Cnst  RO  dynamic_align.o
  80010110-80010287  [.rodata]                         376   8  Cnst  RO  kalman.o
  80010288-8001028f  [.rodata]                           8   8  Cnst  RO  matvecmath.o
  80010290-8001036f  [.rodata]                         224   8  Cnst  RO  navi.o
  80010370-800103af  [.rodata]                          64   8  Cnst  RO  read_and_check_gnss_data.o
  800103b0-80010407  [.rodata]                          88   8  Cnst  RO  readpaoche.o
  80010408-8001050b  [.rodata]                         260   8  Cnst  RO  frame_analysis.o
  8001050c-8001050f  s_i2s_clk_mux_node                  4   4  Cnst  RO  hpm_clock_drv.c.o
  80010510-8001059f  [.rodata]                         144   8  Cnst  RO  InsTestingEntry.o
  800105a0-800105c7  [.rodata]                          40   8  Cnst  RO  fpgad.o
  800105c8-800105f3  [.rodata]                          44   8  Cnst  RO  INS_Init.o
  800105f4-800105f5  __SEGGER_RTL_data_utf8_period
                                                         2   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800105f6-800105f7  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                         2   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  800105f8-8001060f  [.rodata]                          24   8  Cnst  RO  hpm_pllctl_drv.c.o
  80010610-80010617  __SEGGER_RTL_2pow64                 8   8  Cnst  RO  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  80010618-8001061f  __SEGGER_RTL_2pow32                 8   8  Cnst  RO  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  80010620-80010627  __SEGGER_RTL_2powNeg32              8   8  Cnst  RO  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  80010628-80010687  __SEGGER_RTL_float64_ATan          96   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  80010688-800106bf  __SEGGER_RTL_float64_Tan           56   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  800106c0-800106e7  __SEGGER_RTL_float64_Log           40   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  800106e8-80010757  __SEGGER_RTL_float64_ASinACos
                                                       112   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  80010758-80010797  __SEGGER_RTL_float64_SinCos
                                                        64   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  80010798-800107df  __SEGGER_RTL_aPower2               72   8  Cnst  RO  utilops.o (libc_rv32gc_d_balanced.a)
  800107e0-8001087f  __SEGGER_RTL_ipow10               160   8  Cnst  RO  prinops.o (libc_rv32gc_d_balanced.a)
  80010880-80010957  [.srodata.merged.cst8]            216   8  Cnst  RO  [ Linker created ]
  80010958-800109bb  axisTab                           100   4  Cnst  RO  frame_analysis.o
  800109bc-80010a1b  SetDir                             96   4  Cnst  RO  InsTestingEntry.o
  80010a1c-80010a37  [.rodata]                          28   4  Cnst  RO  datado.o
  80010a38-80010a7b  [.rodata]                          68   4  Cnst  RO  gdwatch.o
  80010a7c-80010b7b  [.rodata]                         256   4  Cnst  RO  SetParaBao.o
  80010b7c-800113db  [.rodata]                       2 144   4  Cnst  RO  sd_fatfs.o
  800113dc-8001141b  [.rodata.write_pmp_addr]           64   4  Cnst  RO  hpm_pmp_drv.c.o
  8001141c-8001145b  [.rodata.write_pma_addr]           64   4  Cnst  RO  hpm_pmp_drv.c.o
  8001145c-80011473  [.rodata]                          24   4  Cnst  RO  hpm_uart_drv.c.o
  80011474-80011557  [.rodata]                         228   4  Cnst  RO  ff.c.o
  80011558-80011567  defopt.2                           16   4  Cnst  RO  ff.c.o
  80011568-800269eb  uni2oem936                     87 172   4  Cnst  RO  ffunicode.c.o
  800269ec-80026a0f  [.rodata]                          36   4  Cnst  RO  ffunicode.c.o
  80026a10-80026acb  cvt2.0                            188   4  Cnst  RO  ffunicode.c.o
  80026acc-80026b0b  [.rodata.exception_handler]
                                                        64   4  Cnst  RO  trap.c.o
  80026b0c-80026b1b  s_wdgs                             16   4  Cnst  RO  hpm_clock_drv.c.o
  80026b1c-80026b4f  [.rodata.clock_get_frequency]
                                                        52   4  Cnst  RO  hpm_clock_drv.c.o
  80026b50-80026b6f  [.rodata.get_frequency_for_source]
                                                        32   4  Cnst  RO  hpm_clock_drv.c.o
  80026b70-80026ba3  [.rodata.clock_set_source_divider]
                                                        52   4  Cnst  RO  hpm_clock_drv.c.o
  80026ba4-80026fa3  __SEGGER_RTL_Moeller_inverse_lut
                                                     1 024   4  Cnst  RO  intops.o (libc_rv32gc_d_balanced.a)
  80026fa4-80026fb3  __SEGGER_RTL_hex_lc                16   4  Cnst  RO  prinops.o (libc_rv32gc_d_balanced.a)
  80026fb4-80026fc3  __SEGGER_RTL_hex_uc                16   4  Cnst  RO  prinops.o (libc_rv32gc_d_balanced.a)
  80026fc4-80026ff3  [.rodata.libc.__SEGGER_RTL_vfprintf_float_long.str1.4]
                                                        48   4  Cnst  RO  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  80026ff4-800270bb  [.rodata.libc.__SEGGER_RTL_vfprintf_float_long]
                                                       200   4  Cnst  RO  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  800270bc-800270c7  __SEGGER_RTL_c_locale              12   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800270c8-8002711f  __SEGGER_RTL_c_locale_data
                                                        88   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027120-8002713f  __SEGGER_RTL_codeset_ascii
                                                        32   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027140-800271bf  __SEGGER_RTL_ascii_ctype_map
                                                       128   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800271c0-800272c1  [.rodata]                         258   4  Cnst  RO  uart_dma.o
  800272c2-80027313  l1c_op                             82   2  Code  RX  hpm_l1c_drv.o
  80027314-80027851  [.rodata]                       1 342   4  Cnst  RO  board.c.o
  80027852-80027885  l1c_dc_enable                      52   2  Code  RX  hpm_l1c_drv.o
  80027886-80027887  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                         2   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  80027888-80027895  cst32.1                            14   4  Cnst  RO  ff.c.o
  80027896-800278c3  l1c_ic_enable                      46   2  Code  RX  hpm_l1c_drv.o
  800278c4-800278d1  cst.0                              14   4  Cnst  RO  ff.c.o
  800278d2-80027929  l1c_dc_invalidate                  88   2  Code  RX  hpm_l1c_drv.o
  8002792a-8002792b  ( UNUSED .=.+2 )                    2   -  ----  -   -
  8002792c-80027b1d  cvt1.1                            498   4  Cnst  RO  ffunicode.c.o
  80027b1e-80027b77  l1c_dc_writeback                   90   2  Code  RX  hpm_l1c_drv.o
  80027b78-80027b95  [.rodata.libc.__SEGGER_RTL_X_assert.str1.4]
                                                        30   4  Cnst  RO  execops.o (libc_rv32gc_d_balanced.a)
  80027b96-80027bef  l1c_dc_flush                       90   2  Code  RX  hpm_l1c_drv.o
  80027bf0-80027bf9  [.rodata.libc.__SEGGER_RTL_find_locale.str1.4]
                                                        10   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027bfa-80027c03  l1c_dc_enable_writearound          10   2  Code  RX  hpm_l1c_drv.o
  80027c04-80027c29  pllctl_xtal_set_rampup_time
                                                        38   2  Code  RX  board.c.o
  80027c2a-80027c2b  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80027c2c-80027c65  __SEGGER_RTL_c_locale_day_names
                                                        58   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027c66-80027d37  pcfg_dcdc_switch_to_dcm_mode
                                                       210   2  Code  RX  board.c.o
  80027d38-80027db4  [.rodata]                         125   4  Cnst  RO  uart.o
  80027db5-80027db5  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80027db6-80027e17  board_init_console                 98   2  Code  RX  board.c.o
  80027e18-80027e24  LfnOfs                             13   4  Cnst  RO  ff.c.o
  80027e25-80027e25  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80027e26-80028023  board_print_clock_freq            510   2  Code  RX  board.c.o
  80028024-8002810c  [.rodata]                         233   4  Cnst  RO  hpm_sdmmc_sd.c.o
  8002810d-8002810d  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8002810e-80028127  board_init_uart                    26   2  Code  RX  board.c.o
  80028128-80028128  __SEGGER_RTL_data_empty_string
                                                         1   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028129-80028129  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8002812a-80028187  board_print_banner                 94   2  Code  RX  board.c.o
  80028188-800281b8  __SEGGER_RTL_c_locale_abbrev_month_names
                                                        49   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800281b9-800281b9  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800281ba-800281fb  board_turnoff_rgb_led              66   2  Code  RX  board.c.o
  800281fc-80028218  __SEGGER_RTL_c_locale_abbrev_day_names
                                                        29   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028219-80028219  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8002821a-80028243  board_init_femc_clock              42   2  Code  RX  board.c.o
  80028244-80028250  __SEGGER_RTL_ascii_ctype_mask
                                                        13   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028251-80028251  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028252-80028537  board_init_pmp                    742   2  Code  RX  board.c.o
  80028538-80028540  __SEGGER_RTL_c_locale_time_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028541-80028541  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028542-80028a13  board_init_clock                1 234   2  Code  RX  board.c.o
  80028a14-80028a1c  __SEGGER_RTL_c_locale_date_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028a1d-80028a1d  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028a1e-80028aa7  _init_ext_ram                     138   2  Code  RX  board.c.o
  80028aa8-80028ae2  [.rodata]                          59   4  Cnst  RO  INS_Output.o
  80028ae3-80028ae3  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028ae4-80028b02  [.rodata]                          31   4  Cnst  RO  flash.o
  80028b03-80028b03  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028b04-80028c4e  [.rodata]                         331   4  Cnst  RO  main.o
  80028c4f-80028c4f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028c50-80028c52  driver_num_buf                      3   4  Cnst  RO  sd_fatfs.o
  80028c53-80028c53  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028c54-80028cfe  [.rodata]                         171   4  Cnst  RO  hpm_l1c_drv.o
  80028cff-80028cff  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d00-80028d0e  __SEGGER_RTL_c_locale_date_time_format
                                                        15   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028d0f-80028d0f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d10-80028d16  __SEGGER_RTL_c_locale_am_pm_indicator
                                                         7   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028d17-80028d17  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d18-80028d6e  __SEGGER_RTL_c_locale_month_names
                                                        87   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028d6f-80028d6f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d70-80028e71  board_sd_configure_clock          258   2  Code  RX  board.c.o
  80028e72-80028f97  init_sram_pins                    294   2  Code  RX  pinmux.c.o
  80028f98-80029041  init_gptmr_pins                   170   2  Code  RX  pinmux.c.o
  80029042-8002907f  init_sdxc_cd_pin                   62   2  Code  RX  pinmux.c.o
  80029080-80029121  init_sdxc_clk_data_pins           162   2  Code  RX  pinmux.c.o
  80029122-80029137  femc_enable                        22   2  Code  RX  hpm_femc_drv.c.o
  80029138-80029159  femc_disable                       34   2  Code  RX  hpm_femc_drv.c.o
  8002915a-800291df  femc_default_config               134   2  Code  RX  hpm_femc_drv.c.o
  800291e0-800292fd  femc_init                         286   2  Code  RX  hpm_femc_drv.c.o
  800292fe-8002934f  femc_convert_actual_size_to_memory_size
                                                        82   2  Code  RX  hpm_femc_drv.c.o
  80029350-80029385  ns2cycle                           54   2  Code  RX  hpm_femc_drv.c.o
  80029386-800293ff  femc_get_typical_sram_config
                                                       122   2  Code  RX  hpm_femc_drv.c.o
  80029400-8002956b  femc_config_sram                  364   2  Code  RX  hpm_femc_drv.c.o
  8002956c-80029655  gpio_config_pin_interrupt         234   2  Code  RX  hpm_gpio_drv.c.o
  80029656-80029703  pllctl_pll_poweron                174   2  Code  RX  hpm_pllctl_drv.c.o
  80029704-8002981d  pllctl_get_pll_freq_in_hz         282   2  Code  RX  hpm_pllctl_drv.c.o
  8002981e-8002988b  read_pmp_cfg                      110   2  Code  RX  hpm_pmp_drv.c.o
  8002988c-80029935  write_pmp_addr                    170   2  Code  RX  hpm_pmp_drv.c.o
  80029936-800299a3  read_pma_cfg                      110   2  Code  RX  hpm_pmp_drv.c.o
  800299a4-80029a4d  write_pma_addr                    170   2  Code  RX  hpm_pmp_drv.c.o
  80029a4e-80029b1f  pmp_config                        210   2  Code  RX  hpm_pmp_drv.c.o
  80029b20-80029b31  syscall_handler                    18   2  Code  RX  trap.c.o
  80029b32-80029bab  hpm_csr_get_core_cycle            122   2  Code  RX  hpm_clock_drv.c.o
  80029bac-80029c15  pllctl_get_div                    106   2  Code  RX  hpm_clock_drv.c.o
  80029c16-80029cdd  clock_get_frequency               200   2  Code  RX  hpm_clock_drv.c.o
  80029cde-80029d4b  get_frequency_for_ip_in_common_group
                                                       110   2  Code  RX  hpm_clock_drv.c.o
  80029d4c-80029e27  clock_set_source_divider          220   2  Code  RX  hpm_clock_drv.c.o
  80029e28-80029e61  clock_add_to_group                 58   2  Code  RX  hpm_clock_drv.c.o
  80029e62-80029ef3  clock_cpu_delay_ms                146   2  Code  RX  hpm_clock_drv.c.o
  80029ef4-80029f27  clock_update_core_clock            52   2  Code  RX  hpm_clock_drv.c.o
  80029f28-80029f51  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  hpm_sysctl_drv.c.o
  80029f52-80029f7f  sysctl_clock_target_is_busy
                                                        46   2  Code  RX  hpm_sysctl_drv.c.o
  80029f80-8002a021  sysctl_config_clock               162   2  Code  RX  hpm_sysctl_drv.c.o
  8002a022-8002a07b  system_init                        90   2  Code  RX  system.c.o
  8002a07c-8002a0e5  __SEGGER_RTL_xtoa                 106   2  Code  RX  convops.o (libc_rv32gc_d_balanced.a)
  8002a0e6-8002a101  itoa                               28   2  Code  RX  convops.o (libc_rv32gc_d_balanced.a)
  8002a102-8002a10f  __SEGGER_RTL_X_errno_addr          14   2  Code  RX  errno.o (libc_rv32gc_d_balanced.a)
  8002a110-8002a155  fwrite                             70   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002a156-8002a179  fputc                              36   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002a17a-8002a22f  __floatundisf                     182   2  Code  RX  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a230-8002a245  __floatundidf                      22   2  Code  RX  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a246-8002a27b  __SEGGER_RTL_float64_PolyEvalQ
                                                        54   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a27c-8002a2b9  __SEGGER_RTL_float64_frexp_inline
                                                        62   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a2ba-8002a2c3  __SEGGER_RTL_float64_isnan
                                                        10   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a2c4-8002a2cd  __SEGGER_RTL_float64_isinf
                                                        10   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a2ce-8002a2d7  __SEGGER_RTL_float64_isnormal
                                                        10   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a2d8-8002a395  floor                             190   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a396-8002a47f  tan                               234   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a480-8002a485  sqrt                                6   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a486-8002a5a3  __SEGGER_RTL_float64_asinacos_fpu
                                                       286   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a5a4-8002a5a7  asin                                4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a5a8-8002a5cd  __ashldi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a5ce-8002a5f3  __lshrdi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a5f4-8002aa13  __udivdi3                       1 056   2  Code  RX  intops.o (libc_rv32gc_d_balanced.a)
  8002aa14-8002ae4f  __umoddi3                       1 084   2  Code  RX  intops.o (libc_rv32gc_d_balanced.a)
  8002ae50-8002ae59  abs                                10   2  Code  RX  intops.o (libc_rv32gc_d_balanced.a)
  8002ae5a-8002aedf  memcpy                            134   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002aee0-8002af51  strchr                            114   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002af52-8002af73  __SEGGER_RTL_prin_flush            34   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002af74-8002af8d  __SEGGER_RTL_pre_padding           26   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002af8e-8002afaf  __SEGGER_RTL_init_prin_l           34   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002afb0-8002afc7  __SEGGER_RTL_init_prin             24   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002afc8-8002afed  vfprintf                           38   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002afee-8002b015  printf                             40   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002b016-8002bce3  __SEGGER_RTL_vfprintf_float_long
                                                     3 278   2  Code  RX  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  8002bce4-8002bcf7  __SEGGER_init_heap                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  8002bcf8-8002bd09  __SEGGER_RTL_init_heap             18   2  Code  RX  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  8002bd0a-8002bd27  malloc                             30   2  Code  RX  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  8002bd28-8002bd35  __SEGGER_RTL_ascii_toupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd36-8002bd43  __SEGGER_RTL_ascii_towupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd44-8002bd6d  __SEGGER_RTL_ascii_mbtowc          42   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd6e-8002bd73  isdigit                             6   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd74-8002bd79  isspace                             6   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd7a-8002bd91  l1c_dc_invalidate_all              24   2  Code  RX  hpm_l1c_drv.o
  8002bd92-8002bdbd  sysctl_clock_set_preset            44   2  Code  RX  board.c.o
  8002bdbe-8002be0d  sdxc_enable_inverse_clock          80   2  Code  RX  board.c.o
  8002be0e-8002be51  sdxc_enable_sd_clock               68   2  Code  RX  board.c.o
  8002be52-8002be75  board_init                         36   2  Code  RX  board.c.o
  8002be76-8002be85  board_init_sram_pins               16   2  Code  RX  board.c.o
  8002be86-8002be99  board_delay_ms                     20   2  Code  RX  board.c.o
  8002be9a-8002c031  board_init_uart_clock             408   2  Code  RX  board.c.o
  8002c032-8002c04d  trgm_enable_io_output              28   2  Code  RX  pinmux.c.o
  8002c04e-8002c0a1  trgm_output_config                 84   2  Code  RX  pinmux.c.o
  8002c0a2-8002c1f9  init_uart_pins                    344   2  Code  RX  pinmux.c.o
  8002c1fa-8002c259  init_sdxc_cmd_pin                  96   2  Code  RX  pinmux.c.o
  8002c25a-8002c269  init_sdxc_pwr_pin                  16   2  Code  RX  pinmux.c.o
  8002c26a-8002c2d1  console_init                      104   2  Code  RX  hpm_debug_console.c.o
  8002c2d2-8002c351  __SEGGER_RTL_X_file_write         128   2  Code  RX  hpm_debug_console.c.o
  8002c352-8002c35d  __SEGGER_RTL_X_file_stat           12   2  Code  RX  hpm_debug_console.c.o
  8002c35e-8002c369  __SEGGER_RTL_X_file_bufsize
                                                        12   2  Code  RX  hpm_debug_console.c.o
  8002c36a-8002c385  femc_sw_reset                      28   2  Code  RX  hpm_femc_drv.c.o
  8002c386-8002c3cd  pcfg_dcdc_set_voltage              72   2  Code  RX  hpm_pcfg_drv.c.o
  8002c3ce-8002c419  pllctl_pll_powerdown               76   2  Code  RX  hpm_pllctl_drv.c.o
  8002c41a-8002c651  pllctl_init_int_pll_with_freq
                                                       568   2  Code  RX  hpm_pllctl_drv.c.o
  8002c652-8002c6ad  write_pmp_cfg                      92   2  Code  RX  hpm_pmp_drv.c.o
  8002c6ae-8002c709  write_pma_cfg                      92   2  Code  RX  hpm_pmp_drv.c.o
  8002c70a-8002c7b5  _clean_up                         172   2  Code  RX  reset.c.o
  8002c7b6-8002c7d1  reset_handler                      28   2  Code  RX  reset.c.o
  8002c7d2-8002c7d5  _init                               4   2  Code  RX  reset.c.o
  8002c7d6-8002c7d9  mchtmr_isr                          4   2  Code  RX  trap.c.o
  8002c7da-8002c7dd  swi_isr                             4   2  Code  RX  trap.c.o
  8002c7de-8002c809  exception_handler                  44   2  Code  RX  trap.c.o
  8002c80a-8002c909  get_frequency_for_source          256   2  Code  RX  hpm_clock_drv.c.o
  8002c90a-8002c9e1  get_frequency_for_i2s_or_adc
                                                       216   2  Code  RX  hpm_clock_drv.c.o
  8002c9e2-8002ca15  get_frequency_for_wdg              52   2  Code  RX  hpm_clock_drv.c.o
  8002ca16-8002ca39  get_frequency_for_pwdg             36   2  Code  RX  hpm_clock_drv.c.o
  8002ca3a-8002ca65  clock_connect_group_to_cpu
                                                        44   2  Code  RX  hpm_clock_drv.c.o
  8002ca66-8002ca91  clock_get_core_clock_ticks_per_ms
                                                        44   2  Code  RX  hpm_clock_drv.c.o
  8002ca92-8002cbb9  sysctl_enable_group_resource
                                                       296   2  Code  RX  hpm_sysctl_drv.c.o
  8002cbba-8002cbe5  enable_plic_feature                44   2  Code  RX  system.c.o
  8002cbe6-8002cd91  strtod                            428   2  Code  RX  convops.o (libc_rv32gc_d_balanced.a)
  8002cd92-8002cdb5  __SEGGER_RTL_puts_no_nl            36   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002cdb6-8002cde5  signal                             48   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002cde6-8002ce41  raise                              92   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ce42-8002ce4b  abort                              10   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ce4c-8002ce8d  __SEGGER_RTL_X_assert              66   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ce8e-8002ce99  putchar                            12   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002ce9a-8002cecd  puts                               52   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002cece-8002cf21  __fixunsdfdi                       84   2  Code  RX  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002cf22-8002cf9d  __SEGGER_RTL_ldouble_to_double
                                                       124   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002cf9e-8002cfd5  __SEGGER_RTL_float64_PolyEvalP
                                                        56   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002cfd6-8002d06f  __SEGGER_RTL_float64_sin_inline
                                                       154   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d070-8002d115  __SEGGER_RTL_float64_cos_inline
                                                       166   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d116-8002d133  __trunctfdf2                       30   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d134-8002d13f  __SEGGER_RTL_float64_signbit
                                                        12   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d140-8002d19f  ldexp.localalias                   96   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d1a0-8002d1a3  frexp                               4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d1a4-8002d317  fmod                              372   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d318-8002d31b  sin                                 4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d31c-8002d31f  cos                                 4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d320-8002d425  atan                              262   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d426-8002d52f  log                               266   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d530-8002d597  memset                            104   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002d598-8002d5df  strcpy                             72   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002d5e0-8002d647  strlen                            104   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002d648-8002d753  memcmp                            268   2  Code  RX  strops.o (libc_rv32gc_d_balanced.a)
  8002d754-8002d76f  strcat                             28   2  Code  RX  strops.o (libc_rv32gc_d_balanced.a)
  8002d770-8002d807  strnlen                           152   2  Code  RX  strops.o (libc_rv32gc_d_balanced.a)
  8002d808-8002d843  __SEGGER_RTL_pow10                 60   2  Code  RX  utilops.o (libc_rv32gc_d_balanced.a)
  8002d844-8002d84f  __SEGGER_RTL_stream_write          12   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d850-8002d8eb  __SEGGER_RTL_putc                 156   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d8ec-8002d915  __SEGGER_RTL_print_padding
                                                        42   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d916-8002d951  sprintf                            60   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d952-8002d9c9  vfprintf_l                        120   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d9ca-8002da1d  __SEGGER_RTL_alloc                 84   2  Code  RX  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  8002da1e-8002da31  __SEGGER_RTL_X_heap_lock           20   2  Code  RX  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  8002da32-8002da45  __SEGGER_RTL_X_heap_unlock
                                                        20   2  Code  RX  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  8002da46-8002da71  __SEGGER_RTL_ascii_isctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002da72-8002da81  __SEGGER_RTL_ascii_tolower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002da82-8002daad  __SEGGER_RTL_ascii_iswctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002daae-8002dabd  __SEGGER_RTL_ascii_towlower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002dabe-8002dad1  __SEGGER_RTL_ascii_wctomb          20   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002dad2-8002dae1  __SEGGER_RTL_current_locale
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002dae2-8002daff  __SEGGER_RTL_isctype               30   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002db00-8002dbdf  __SEGGER_init_table__             224   4  Cnst  RO  [ Linker created ]
  8002dbe0-80040a1b  __SEGGER_init_data__           77 372   4  Cnst  RO  [ Linker created ]
  80040a1c-80040a65  __SEGGER_init_pack                 74   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  80040a66-80040ac5  __SEGGER_init_lzss                 96   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  80040ac6-80040ad9  __SEGGER_init_zero                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  80040ada-80040af5  __SEGGER_init_copy                 28   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)


***********************************************************************************************
***                                                                                         ***
***                                       SYMBOL LIST                                       ***
***                                                                                         ***
***********************************************************************************************

RAM function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  ACC_gyroreset_r_TAFEAG16_buf
                             0x00022B10          24      2  Init  Gb  InsTestingEntry.o
  AlgorithmAct               0x00007410       2 056      2  Init  Gb  InsTestingEntry.o
  AlgorithmDo                0x0002106E          84      2  Init  Gb  InsTestingEntry.o
  Algorithm_before_otherDataDo
                             0x00022AFC          24      2  Init  Gb  datado.o
  AnalyticCoordinateAxis     0x000022F8       6 598      2  Init  Gb  InsTestingEntry.o
  AttiToCnb                  0x000182F4         318      2  Init  Gb  navi.o
  BindDefaultSet_by_GNSS     0x000206D4         102      2  Init  Gb  navi.o
  CloseFileToSd              0x0001DAB2         164      2  Init  Gb  sd_fatfs.o
  CnbToAtti                  0x00014C00         416      2  Init  Gb  navi.o
  CnbToQ                     0x00010FF0         640      2  Init  Gb  navi.o
  ComputeAttiRate            0x00019A16         270      2  Init  Gb  navi.o
  ComputeCen                 0x0001D3CC         188      2  Init  Gb  align.o
  ComputeCib0i               0x0001C67C         216      2  Init  Gb  align.o
  ComputeCie                 0x0001BA2E         232      2  Init  Gb  align.o
  ComputeDeg_Ex              0x0001F430         124      2  Init  Gb  navi.o
  ComputeDelSenbb            0x0001CA4E         208      2  Init  Gb  navi.o
  ComputeFk                  0x00014580         440      2  Init  Gb  kalman.o
  ComputeFn                  0x00006B9A       2 166      2  Init  Gb  kalman.o
  ComputeG                   0x0001D24C         196      2  Init  Gb  navi.o
  ComputeKk                  0x00012ED4         484      2  Init  Gb  kalman.o
  ComputeLeverArmSn          0x00022120          56      2  Init  Gb  navi.o
  ComputeLeverArmVn          0x000216E0          74      2  Init  Gb  navi.o
  ComputePk                  0x0001C5AC         216      2  Init  Gb  kalman.o
  ComputePkk_1_Step1         0x0002073A         100      2  Init  Gb  kalman.o
  ComputePkk_1_Step2         0x0001DC86         162      2  Init  Gb  kalman.o
  ComputePos                 0x00018898         302      2  Init  Gb  navi.o
  ComputeQ                   0x000143CC         440      2  Init  Gb  navi.o
  ComputeRmRn                0x0001E1F4         156      2  Init  Gb  navi.o
  ComputeSi                  0x000116D0         582      2  Init  Gb  dynamic_align.o
  ComputeSib0                0x0001D5E0         178      2  Init  Gb  dynamic_align.o
  ComputeVi                  0x0001D488         186      2  Init  Gb  align.o
  ComputeVib0                0x000201D2         108      2  Init  Gb  align.o
  ComputeVibn                0x00018662         308      2  Init  Gb  navi.o
  ComputeVn                  0x00014DA0         410      2  Init  Gb  navi.o
  ComputeWenn                0x00020C8E          90      2  Init  Gb  navi.o
  ComputeWien                0x00020D3C          88      2  Init  Gb  navi.o
  ComputeWnbb                0x0001D310         192      2  Init  Gb  navi.o
  ComputeXk                  0x0001CF74         204      2  Init  Gb  kalman.o
  ComputeXkk_1               0x0001F96C         116      2  Init  Gb  kalman.o
  ComputeZk                  0x0000990A       1 350      2  Init  Gb  kalman.o
  CorrectAtti                0x000137C8         466      2  Init  Gb  kalman.o
  CorrectVn                  0x00021BE6          64      2  Init  Gb  kalman.o
  DeleteFileFromSd           0x0001D704         172      2  Init  Gb  sd_fatfs.o
  DeviceInit                 0x00021EA6          60      2  Init  Gb  datado.o
  Drv_FlashErase             0x0002232E          54      2  Init  Gb  FirmwareUpdateFile.o
  Drv_FlashRead              0x000228FE          32      2  Init  Gb  FirmwareUpdateFile.o
  Drv_FlashWrite             0x00021C96          62      2  Init  Gb  FirmwareUpdateFile.o
  Drv_SystemReset            0x00022A74          26      2  Init  Gb  FirmwareUpdateFile.o
  DynamicInertialSysAlignCompute
                             0x00011270         606      2  Init  Gb  dynamic_align.o
  DynamicInertialSysAlign_Init
                             0x0001DEFA         160      2  Init  Gb  dynamic_align.o
  DynamicNavi_Init           0x00019F16         266      2  Init  Gb  navi.o
  EXTI3_IRQHandler           0x000228CC          34      2  Init  Gb  gd32f4xx_it.o
  ErrCorrect_1_Navi_Time     0x0000B18C       1 222      2  Init  Gb  kalman.o
  ErrStore_1s                0x00008DEC       1 516      2  Init  Gb  kalman.o
  Exti_Init                  0x00022614          44      2  Init  Gb  main.o
  FPGATo422_00BB_send        0x0000830C       1 612      2  Init  Gb  InsTestingEntry.o
  Fatfs_Init                 0x0001992E         276      2  Init  Gb  sd_fatfs.o
  FinishDynamicInertialSysAlign
                             0x0001B6EC         236      2  Init  Gb  dynamic_align.o
  FinishInertialSysAlign     0x0001B620         236      2  Init  Gb  align.o
  GNSSAndHeadDataTest        0x000135F4         468      2  Init  Gb  read_and_check_gnss_data.o
  GNSS_Last_TIME             0x00021A68          68      2  Init  Gb  InsTestingEntry.o
  GNSS_Lost_Time             0x00021474          78      2  Init  Gb  InsTestingEntry.o
  GNSS_Valid_PPSStart        0x0001BBEC         230      2  Init  Gb  InsTestingEntry.o
  Hk_Init                    0x00016316         380      2  Init  Gb  kalman.o
  INS912AlgorithmEntry       0x0001CED2         204      2  Init  Gb  InsTestingEntry.o
  INS912_Output              0x0000B636       1 194      2  Init  Gb  INS_Output.o
  INS_Init                   0x0001ED50         136      2  Init  Gb  INS_Init.o
  InertialSysAlignCompute    0x00012D0E         486      2  Init  Gb  align.o
  InertialSysAlign_Init      0x0001E15C         156      2  Init  Gb  align.o
  InitParaToAlgorithm        0x0000EB4C         786      2  Init  Gb  SetParaBao.o
  KalCompute                 0x00016B8E         364      2  Init  Gb  kalman.o
  KalPredict                 0x0001F6DE         122      2  Init  Gb  kalman.o
  Kalman_Init                0x0001C292         218      2  Init  Gb  kalman.o
  Kalman_StartUp             0x000093D8       1 354      2  Init  Gb  kalman.o
  LEDIndicator               0x0001CE26         204      2  Init  Gb  INS_Init.o
  Led_Control                0x00022A10          28      2  Init  Gb  main.o
  Mat_Inv                    0x0000F958         700      2  Init  Gb  matvecmath.o
  Mat_Mul                    0x0001DBE4         162      2  Init  Gb  matvecmath.o
  Mat_Tr                     0x00021198          82      2  Init  Gb  matvecmath.o
  NaviCompute                0x0000E4E2         870      2  Init  Gb  navi.o
  Navi_Init                  0x0001C1C4         218      2  Init  Gb  navi.o
  ParaUpdateHandle           0x00015552         400      2  Init  Gb  SetParaBao.o
  Pk_Init                    0x0001AC8E         246      2  Init  Gb  kalman.o
  Pwm_Init                   0x000224AC          46      2  Init  Gb  timer.o
  QToCnb                     0x00012374         518      2  Init  Gb  navi.o
  Qk_Init                    0x0001AB98         246      2  Init  Gb  kalman.o
  Qua_Mul                    0x000176D0         324      2  Init  Gb  matvecmath.o
  ReadBB00FileOpenFromSd     0x0001F2C8         128      2  Init  Lc  sd_fatfs.o
  ReadCom3FileOpenFromSd     0x0001F254         128      2  Init  Lc  sd_fatfs.o
  ReadFileOpenFromSd         0x0002102A          84      2  Init  Gb  sd_fatfs.o
  ReadFileToSd               0x00008890       1 540      2  Init  Gb  sd_fatfs.o
  ReadPara                   0x00019C30         268      2  Init  Gb  SetParaBao.o
  ReadPara0_SetEnd           0x0001FF68         112      2  Init  Gb  SetParaBao.o
  ReadPara0_SetHead          0x0001EBB6         140      2  Init  Gb  SetParaBao.o
  ReadPara1_SetEnd           0x0001FEFC         112      2  Init  Gb  SetParaBao.o
  ReadPara1_SetHead          0x0001EB2A         140      2  Init  Gb  SetParaBao.o
  ReadPara2_SetEnd           0x0001FE90         112      2  Init  Gb  SetParaBao.o
  ReadPara2_SetHead          0x0001EA9E         140      2  Init  Gb  SetParaBao.o
  ReadPara3_SetEnd           0x0001FE24         112      2  Init  Gb  SetParaBao.o
  ReadPara3_SetHead          0x0001EA12         140      2  Init  Gb  SetParaBao.o
  ReadPara4_SetEnd           0x0001FDB8         112      2  Init  Gb  SetParaBao.o
  ReadPara4_SetHead          0x0001E986         140      2  Init  Gb  SetParaBao.o
  ReadParaFromFlash          0x0000F6A0         704      2  Init  Gb  SetParaBao.o
  ReadPara_0                 0x00019E1C         266      2  Init  Gb  SetParaBao.o
  ReadPara_1                 0x0001982A         276      2  Init  Gb  SetParaBao.o
  ReadPara_2                 0x00017BCE         322      2  Init  Gb  SetParaBao.o
  ReadPara_3                 0x00019D22         266      2  Init  Gb  SetParaBao.o
  ReadPara_4                 0x00016A2E         368      2  Init  Gb  SetParaBao.o
  Read_And_Check_GNSS_Data   0x00020676         102      2  Init  Gb  read_and_check_gnss_data.o
  RestoreFactory             0x00011498         600      2  Init  Gb  SetParaBao.o
  Rk_Init                    0x0000D65A         974      2  Init  Gb  kalman.o
  SDUartIrqInit              0x0002029A         106      2  Init  Gb  uart.o
  SaveGNSSData               0x0000F0FA         774      2  Init  Gb  read_and_check_gnss_data.o
  SaveINSData                0x0001F664         122      2  Init  Gb  kalman.o
  SaveParaToFlash            0x00018544         310      2  Init  Gb  SetParaBao.o
  SdFileOperateTypeSet       0x0001D1AA         196      2  Init  Gb  sd_fatfs.o
  SdFileReadOperate          0x0001F088         130      2  Init  Gb  sd_fatfs.o
  SdFileWriteOperate         0x000225F4          44      2  Init  Gb  sd_fatfs.o
  SendPara_SetEnd            0x0001FD4C         112      2  Init  Gb  SetParaBao.o
  SendPara_SetHead           0x0001E8FA         140      2  Init  Gb  SetParaBao.o
  SendVersionInfo            0x0001F81E         118      2  Init  Gb  datado.o
  SetParaAll                 0x0000BEEC       1 140      2  Init  Gb  SetParaBao.o
  SetParaAngle               0x000181C4         320      2  Init  Gb  SetParaBao.o
  SetParaBaud                0x000170D2         350      2  Init  Gb  SetParaBao.o
  SetParaCalibration         0x00019634         278      2  Init  Gb  SetParaBao.o
  SetParaCoord               0x00018DDC         286      2  Init  Gb  SetParaBao.o
  SetParaDataOutType         0x0001A1C2         258      2  Init  Gb  SetParaBao.o
  SetParaDebugMode           0x0001B02C         240      2  Init  Gb  SetParaBao.o
  SetParaDeviation           0x00018094         320      2  Init  Gb  SetParaBao.o
  SetParaFactorAcc           0x00015992         392      2  Init  Gb  SetParaBao.o
  SetParaFactorGyro          0x0001581A         392      2  Init  Gb  SetParaBao.o
  SetParaFilter              0x0001A9BE         250      2  Init  Gb  SetParaBao.o
  SetParaFrequency           0x0001952C         280      2  Init  Gb  SetParaBao.o
  SetParaGnss                0x00017F64         320      2  Init  Gb  SetParaBao.o
  SetParaGnssInitValue       0x00013EAA         448      2  Init  Gb  SetParaBao.o
  SetParaGpsType             0x0001AF4C         240      2  Init  Gb  SetParaBao.o
  SetParaGyroType            0x0001AE6C         240      2  Init  Gb  SetParaBao.o
  SetParaKalmanQ             0x0001A8D4         250      2  Init  Gb  SetParaBao.o
  SetParaKalmanR             0x0001A7EA         250      2  Init  Gb  SetParaBao.o
  SetParaSdHandle            0x0001973A         276      2  Init  Gb  SetParaBao.o
  SetParaTime                0x0001A352         256      2  Init  Gb  SetParaBao.o
  SetParaUpdateEnd           0x00018432         310      2  Init  Gb  SetParaBao.o
  SetParaUpdateSend          0x00017218         338      2  Init  Gb  SetParaBao.o
  SetParaUpdateStart         0x000153DE         400      2  Init  Gb  SetParaBao.o
  SetParaUpdateStop          0x0001B95A         232      2  Init  Gb  SetParaBao.o
  SetParaVector              0x00017E34         320      2  Init  Gb  SetParaBao.o
  SysInit                    0x000150C2         404      2  Init  Gb  navi.o
  SysVarDefaultSet           0x0001E0C0         156      2  Init  Gb  navi.o
  TIMER2_IRQHandler          0x0001A2AC         258      2  Init  Gb  gd32f4xx_it.o
  TransHeading0to360         0x00021A24          68      2  Init  Gb  navi.o
  UartDmaRecSetPara          0x0000CB64       1 022      2  Init  Gb  SetParaBao.o
  UartIrqInit                0x0001ECCC         136      2  Init  Gb  uart.o
  UartIrqSendMsg             0x0001DF9A         158      2  Init  Gb  uart.o
  Uart_SendMsg               0x000223F0          50      2  Init  Gb  bsp_fmc.o
  UpdateAlignPosAndVn        0x00020E3C          86      2  Init  Gb  dynamic_align.o
  UpdateEnd_SetEnd           0x0001FCE0         112      2  Init  Gb  SetParaBao.o
  UpdateEnd_SetHead          0x0001E86E         140      2  Init  Gb  SetParaBao.o
  UpdateSend_SetEnd          0x0001FC74         112      2  Init  Gb  SetParaBao.o
  UpdateSend_SetHead         0x0001E7E2         140      2  Init  Gb  SetParaBao.o
  UpdateStart_SetEnd         0x0001FC08         112      2  Init  Gb  SetParaBao.o
  UpdateStart_SetHead        0x0001E756         140      2  Init  Gb  SetParaBao.o
  UpdateStop_SetEnd          0x0001FB9C         112      2  Init  Gb  SetParaBao.o
  UpdateStop_SetHead         0x0001E6CA         140      2  Init  Gb  SetParaBao.o
  Vec_Cross                  0x0001F006         130      2  Init  Gb  matvecmath.o
  Virtual_PPS_insert_5hz     0x0001BD96         228      2  Init  Gb  InsTestingEntry.o
  WriteBB00FileOpenFromSd    0x0001EE2A         134      2  Init  Lc  sd_fatfs.o
  WriteBB00FileToSd          0x000161C2         380      2  Init  Gb  sd_fatfs.o
  WriteCOM3FileToSd          0x0001D8E4         166      2  Init  Gb  sd_fatfs.o
  WriteCom3FileOpenFromSd    0x0001EDB0         134      2  Init  Lc  sd_fatfs.o
  WriteFileOpenFromSd        0x0001C4F4         216      2  Init  Gb  sd_fatfs.o
  WriteFileToSd              0x0001D68C         172      2  Init  Gb  sd_fatfs.o
  analysisRxdata             0x0000E1B8         890      2  Init  Gb  uart.o
  app_accum_verify_8bit      0x000220E8          56      2  Init  Gb  app_tool.o
  board_init_sd_host_params  0x00016778         370      2  Init  Wk  hpm_sdmmc_port.c.o
  bsp_gpio_init              0x0001607E         380      2  Init  Gb  bsp_gpio.o
  bsp_tim_init               0x00022A00          28      2  Init  Gb  bsp_tim.o
  caninfupdate               0x0001A442         254      2  Init  Gb  INS_Data.o
  change_bitmap              0x0001BE6E         224      2  Init  Lc  ff.c.o
  check_fs                   0x000130A4         476      2  Init  Lc  ff.c.o
  clst2sect                  0x000222F8          54      2  Init  Lc  ff.c.o
  cmp_lfn                    0x0001A0CA         264      2  Init  Lc  ff.c.o
  comm_axis_read             0x00022BDA          16      2  Init  Gb  computerFrameParse.o
  comm_nav_para_syn          0x00022C3C           4      2  Init  Gb  computerFrameParse.o
  comm_param_setbits         0x000219E0          68      2  Init  Gb  computerFrameParse.o
  comm_read_currentFreq      0x00022BCA          16      2  Init  Gb  computerFrameParse.o
  core_local_mem_to_sys_address
                             0x000209C2          96      2  Init  Lc  uart_dma.o
  core_local_mem_to_sys_address
                             0x00020A22          96      2  Init  Lc  hpm_sdmmc_disk.c.o
  core_local_mem_to_sys_address
                             0x00020A82          96      2  Init  Lc  hpm_sdmmc_port.c.o
  crc_verify_8bit            0x000220B0          56      2  Init  Gb  app_tool.o
  create_chain               0x000103B8         678      2  Init  Lc  ff.c.o
  create_name                0x0000A30C       1 308      2  Init  Lc  ff.c.o
  create_partition           0x0001088E         668      2  Init  Lc  ff.c.o
  create_xdir                0x00018FF8         282      2  Init  Lc  ff.c.o
  dbc_1st                    0x0002199C          68      2  Init  Lc  ff.c.o
  default_isr_111            0x00000450         248      4  Init  Gb  hpm_sdmmc_disk.c.o
  default_isr_2              0x0000021C         246      4  Init  Gb  main.o
  default_isr_63             0x0000033C         248      4  Init  Gb  timer.o
  delay_ms                   0x00022AE4          24      2  Init  Gb  systick.o
  dir_alloc                  0x0001BF4A         222      2  Init  Lc  ff.c.o
  dir_clear                  0x0001DA1A         164      2  Init  Lc  ff.c.o
  dir_find                   0x0000F3F8         724      2  Init  Lc  ff.c.o
  dir_next                   0x00016F82         352      2  Init  Lc  ff.c.o
  dir_read                   0x0001294C         506      2  Init  Lc  ff.c.o
  dir_register               0x0000C788       1 056      2  Init  Lc  ff.c.o
  dir_remove                 0x0001C812         214      2  Init  Lc  ff.c.o
  dir_sdi                    0x0001941C         280      2  Init  Lc  ff.c.o
  disk_async_write           0x00020614         102      2  Init  Gb  diskio.c.o
  disk_initialize            0x000217CA          70      2  Init  Gb  diskio.c.o
  disk_ioctl                 0x0002016A         108      2  Init  Gb  diskio.c.o
  disk_read                  0x000205B2         102      2  Init  Gb  diskio.c.o
  disk_status                0x00021788          70      2  Init  Gb  diskio.c.o
  disk_sync_read             0x00020552         102      2  Init  Gb  diskio.c.o
  disk_write                 0x000204F0         102      2  Init  Gb  diskio.c.o
  dma_config_linked_descriptor
                             0x00013420         468      2  Init  Gb  hpm_dma_drv.c.o
  dma_default_channel_config
                             0x00021E6A          60      2  Init  Gb  hpm_dma_drv.c.o
  dma_get_remaining_transfer_size
                             0x00022ACC          24      2  Init  Lc  uart_dma.o
  dma_setup_channel          0x00011F5C         548      2  Init  Gb  hpm_dma_drv.c.o
  dmamux_config              0x00021E2E          60      2  Init  Lc  uart_dma.o
  extract_csd_field          0x0001C73C         214      2  Init  Gb  hpm_sdmmc_common.c.o
  f_async_write              0x0000D2A6         976      2  Init  Gb  ff.c.o
  f_chdrive                  0x000222CA          54      2  Init  Gb  ff.c.o
  f_close                    0x00020110         108      2  Init  Gb  ff.c.o
  f_lseek                    0x0000A81C       1 278      2  Init  Gb  ff.c.o
  f_mkfs                     0x00003B9E       4 942      2  Init  Gb  ff.c.o
  f_mount                    0x0001E554         144      2  Init  Gb  ff.c.o
  f_open                     0x0000ACFA       1 250      2  Init  Gb  ff.c.o
  f_printf                   0x0000BA98       1 152      2  Init  Gb  ff.c.o
  f_read                     0x0000E808         868      2  Init  Gb  ff.c.o
  f_sync                     0x00010638         670      2  Init  Gb  ff.c.o
  f_unlink                   0x00014A82         418      2  Init  Gb  ff.c.o
  f_write                    0x0000CEF2         976      2  Init  Gb  ff.c.o
  ff_handle_poll             0x0002278A          38      2  Init  Gb  ff_queue.o
  ff_queue_get_buffer_by_sector
                             0x0001E3AC         152      2  Init  Gb  ff_queue.o
  ff_queue_init              0x00020CE8          88      2  Init  Gb  ff_queue.o
  ff_queue_is_empty          0x00022686          42      2  Init  Gb  ff_queue.o
  ff_queue_is_full           0x00021BA6          64      2  Init  Gb  ff_queue.o
  ff_queue_poll              0x0001E4CC         144      2  Init  Gb  ff_queue.o
  ff_queue_push              0x0001CD60         206      2  Init  Gb  ff_queue.o
  ff_uni2oem                 0x0001D038         198      2  Init  Gb  ffunicode.c.o
  ff_wtoupper                0x00016606         370      2  Init  Gb  ffunicode.c.o
  fill_first_frag            0x0002035E         104      2  Init  Lc  ff.c.o
  fill_last_frag             0x0001FA52         114      2  Init  Lc  ff.c.o
  find_bitmap                0x0001B538         236      2  Init  Lc  ff.c.o
  find_volume                0x0001CC9E         206      2  Init  Lc  ff.c.o
  fmc2sinsraw                0x0000073E       7 230      2  Init  Gb  readpaoche.o
  follow_path                0x00011D50         552      2  Init  Lc  ff.c.o
  fpgadata_Predo             0x000216B8          74      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen        0x0002175C          72      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_OutDataSet
                             0x0001015E         686      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_acc
                             0x0000FEC0         694      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_gyro
                             0x00010D88         640      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                             0x0000DDF0         968      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                             0x0000DA28         968      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_algParmCash
                             0x00020B8A          92      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_preAlgParm_370
                             0x000175CE         326      2  Init  Gb  InsTestingEntry.o
  fpgadata_syn_count_do      0x00018BF6         296      2  Init  Gb  fpgad.o
  frame_pack_and_send        0x00004DC0       4 466      2  Init  Gb  frame_analysis.o
  gd_eval_com_init           0x000229EC          28      2  Init  Gb  INS_Init.o
  gen_numname                0x00016492         376      2  Init  Lc  ff.c.o
  get_boot_reason            0x00019B24         268      2  Init  Gb  main.o
  get_fat                    0x00010B16         658      2  Init  Lc  ff.c.o
  get_fpgadata               0x000225DA          44      2  Init  Gb  fpgad.o
  get_fpgadata_after_otherDataDo
                             0x0001E32A         154      2  Init  Gb  fpgad.o
  get_fpgadata_before        0x00022C38           4      2  Init  Gb  fpgad.o
  get_fpgadata_do            0x00019338         280      2  Init  Gb  fpgad.o
  get_ldnumber               0x0001D7A0         170      2  Init  Lc  ff.c.o
  get_uart_tx_idle           0x00022B24          22      2  Init  Gb  uart_dma.o
  gnss_check_bind            0x00017A98         322      2  Init  Gb  InsTestingEntry.o
  gpio_clear_pin_interrupt_flag
                             0x000225AE          44      2  Init  Lc  main.o
  gpio_enable_pin_interrupt  0x00022582          44      2  Init  Lc  main.o
  gpio_set_pin_input         0x00022556          44      2  Init  Lc  main.o
  gpio_set_pin_output        0x0002252A          44      2  Init  Lc  bsp_gpio.o
  gpio_toggle_pin            0x0002265C          42      2  Init  Lc  main.o
  gpio_write_pin             0x00021146          82      2  Init  Lc  INS_Init.o
  gpiom_set_pin_controller   0x00020F82          84      2  Init  Lc  bsp_gpio.o
  gpiom_set_pin_controller   0x00020FD6          84      2  Init  Lc  main.o
  gptmr_channel_config       0x00017956         322      2  Init  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_get_default_config
                             0x0001F9E0         114      2  Init  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_reset_count  0x0002166E          74      2  Init  Lc  timer.o
  gptmr_check_status         0x00022810          36      2  Init  Lc  timer.o
  gptmr_clear_status         0x00022B3A          20      2  Init  Lc  timer.o
  gptmr_enable_irq           0x000229D0          28      2  Init  Lc  timer.o
  gptmr_start_counter        0x000224FE          44      2  Init  Lc  timer.o
  gptmr_stop_counter         0x000224D2          44      2  Init  Lc  timer.o
  gptmr_update_cmp           0x00021B24          66      2  Init  Lc  timer.o
  hpm_csr_get_core_mcycle    0x0001F570         122      2  Init  Lc  hpm_sdmmc_osal.o
  hpm_csr_get_core_mcycle    0x0001F5EA         122      2  Init  Lc  hpm_sdmmc_sd.c.o
  hpm_sdmmc_osal_delay       0x00022764          38      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_clear
                             0x00022968          30      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_create
                             0x000226F0          40      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_set   0x00022A5A          26      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_wait  0x0001B87A         232      2  Init  Wk  hpm_sdmmc_osal.o
  init_alloc_info            0x00020852          98      2  Init  Lc  ff.c.o
  init_gpio                  0x000228AA          34      2  Init  Gb  main.o
  initializationdriversettings
                             0x00015DF4         384      2  Init  Gb  gdwatch.o
  irq_handler_trap           0x00000554         488      4  Init  Gb  trap.c.o
  isr_gpio                   0x00000200          36      4  Init  Gb  main.o
  ld_clust                   0x0002171C          72      2  Init  Lc  ff.c.o
  ld_dword                   0x00021958          68      2  Init  Lc  ff.c.o
  ld_qword                   0x0001524E         400      2  Init  Lc  ff.c.o
  ld_word                    0x00021F6E          58      2  Init  Lc  ff.c.o
  load_obj_xdir              0x0001EF8C         130      2  Init  Lc  ff.c.o
  load_xdir                  0x0001257A         516      2  Init  Lc  ff.c.o
  loopDoOther                0x00022952          30      2  Init  Gb  datado.o
  main                       0x0001A00C         264      2  Init  Gb  main.o
  mchtmr_get_count           0x00022BA8          18      2  Init  Lc  sd_fatfs.o
  mcusendtopcdriversdata     0x00013CF6         452      2  Init  Gb  gdwatch.o
  mount_volume               0x00007BC0       2 016      2  Init  Lc  ff.c.o
  move_window                0x00020300         104      2  Init  Lc  ff.c.o
  myget_16bit_D32            0x00022078          56      2  Init  Gb  readpaoche.o
  myget_16bit_D64            0x0002048A         102      2  Init  Gb  readpaoche.o
  myget_16bit_I32            0x00022294          54      2  Init  Gb  readpaoche.o
  myget_8bit_I16             0x0002273E          38      2  Init  Gb  readpaoche.o
  norflash_erase_sector      0x000223C6          50      2  Init  Gb  flash.o
  norflash_init              0x0002142E          78      2  Init  Gb  flash.o
  norflash_read              0x00021F3E          58      2  Init  Gb  flash.o
  norflash_write             0x00021F0C          58      2  Init  Gb  flash.o
  output_fpga_void           0x0000C350       1 128      2  Init  Gb  INS_Output.o
  output_fpgatxt_do          0x00015F28         382      2  Init  Gb  INS_Output.o
  output_gdw_do              0x00005D86       3 824      2  Init  Gb  INS_Output.o
  output_normal_do           0x00022892          34      2  Init  Gb  INS_Output.o
  pick_lfn                   0x0001AD84         240      2  Init  Lc  ff.c.o
  pnavout_set                0x0001D52E         178      2  Init  Gb  InsTestingEntry.o
  ppor_sw_reset              0x00022B96          18      2  Init  Lc  FirmwareUpdateFile.o
  protocol_send              0x00022AB8          24      2  Init  Gb  computerFrameParse.o
  put_fat                    0x00011902         574      2  Init  Lc  ff.c.o
  put_lfn                    0x0001AAA8         248      2  Init  Lc  ff.c.o
  putc_bfd                   0x00013B38         458      2  Init  Lc  ff.c.o
  putc_flush                 0x00021628          74      2  Init  Lc  ff.c.o
  putc_init                  0x00022718          38      2  Init  Lc  ff.c.o
  remove_chain               0x00012180         520      2  Init  Lc  ff.c.o
  rom_xpi_nor_auto_config    0x000191F2          46      2  Init  Lc  flash.o
  rom_xpi_nor_erase_block    0x00019140          58      2  Init  Lc  flash.o
  rom_xpi_nor_erase_chip     0x0001917A          54      2  Init  Lc  flash.o
  rom_xpi_nor_erase_sector   0x00019106          58      2  Init  Lc  flash.o
  rom_xpi_nor_program        0x000191B0          66      2  Init  Lc  flash.o
  rom_xpi_nor_read           0x00021DF2          60      2  Init  Lc  flash.o
  sd_all_send_cid            0x0001F7AC         118      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_app_cmd_send_cond_op    0x000200AC         108      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_app_cmd_set_write_block_erase_count
                             0x0002023A         106      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_be2le                   0x000207F0          98      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_card_init               0x0000EE4A         784      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_check_card_parameters   0x00020C38          90      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_convert_data_endian     0x0001DB46         162      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_decode_csd              0x00009E50       1 332      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_decode_scr              0x0001E290         154      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_decode_status           0x0001405A         444      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_deinit                  0x00022C2E          10      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_disk_async_write        0x00021918          68      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_initialize         0x00018786         306      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_ioctl              0x0001DE5A         160      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_read               0x00021DBA          60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_status             0x00020796          98      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_sync_read          0x00020BE6          90      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_write              0x00021D82          60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_error_recovery          0x0001F8FC         116      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_host_init               0x00020DEA          86      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_init                    0x0001D0FE         196      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_is_card_present         0x00022938          30      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_mkfs                    0x0001F3BA         126      2  Init  Lc  sd_fatfs.o
  sd_mount_fs                0x0001F1E0         128      2  Init  Lc  sd_fatfs.o
  sd_polling_card_status_busy
                             0x00018CC6         290      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_probe_bus_voltage       0x00016CDA         362      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_read_blocks             0x00012B30         490      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_read_status             0x0001A700         250      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_select_card             0x000218D8          68      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_send_card_status        0x00020428         102      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_cmd                0x000210FC          82      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_csd                0x0001F168         128      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_if_cond            0x0001FB30         112      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_rca                0x00020968          96      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_scr                0x0001BB16         230      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_set_bus_timing          0x00018EEA         282      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_set_bus_width           0x0001E444         144      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_set_max_current         0x00022636          42      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_start_write_blocks      0x00013260         472      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_switch_function         0x00017D00         320      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_switch_voltage          0x00021556          76      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_transfer                0x0001D982         164      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_write_blocks            0x00012762         514      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdcard_isr                 0x00000438          28      4  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmc_card_async_write     0x0001B2AC         238      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmc_card_read            0x0001C0F0         220      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_card_write           0x0001C01C         220      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_enable_auto_tuning   0x0001F0FE         128      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_get_card_and_aligned_buf_info
                             0x00021D46          60      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_get_sys_addr         0x000227F0          36      2  Init  Gb  hpm_sdmmc_port.c.o
  sdmmc_go_idle_state        0x000210AE          82      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_select_card          0x0001F88C         116      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_send_application_command
                             0x0002090C          96      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_set_block_size       0x00020F32          84      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmchost_check_host_availability
                             0x00021C5C          62      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_delay_ms         0x000229BA          28      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_enable_sdio_interrupt
                             0x00020B3A          92      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_error_recovery   0x00022874          34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_get_data_pin_level
                             0x0002135A          80      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_init             0x0001B1EA         238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_init_io          0x0000FC14         696      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_irq_handler      0x0001B386         238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_8bit_supported
                             0x00022AA0          24      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_card_detected
                             0x0001DDBE         160      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_voltage_switch_supported
                             0x0002299E          28      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_power_control    0x00014F3A         408      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_register_xfer_complete_callback
                             0x00022852          34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_select_voltage   0x0002150E          76      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_send_command     0x0001C99A         210      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_card_bus_width
                             0x00021C26          62      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_card_clock   0x00022392          52      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_cardclk_delay_chain
                             0x0001F512         122      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_speed_mode   0x00022044          56      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_start_transfer   0x0001A61E         250      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_switch_to_1v8    0x0001B7B8         234      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_switch_to_3v3_as_needed
                             0x00022834          34      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_transfer         0x000168E6         368      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_vsel_pin_control
                             0x0001A528         250      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_wait_card_active
                             0x000226D2          40      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_wait_command_done
                             0x00021316          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_idle        0x000212CE          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_xfer_done   0x0002128A          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sduart_recv_polling        0x0001EF28         132      2  Init  Gb  uart.o
  sdxc_clear_interrupt_status
                             0x00022B72          18      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_clear_interrupt_status
                             0x00022B84          18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_auto_tuning    0x0002200C          56      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_interrupt_signal
                             0x0002225E          54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_interrupt_status
                             0x000221F2          54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_interrupt_status
                             0x00022228          54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_inverse_clock  0x000211EA          80      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_inverse_clock  0x0002123A          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_power          0x0002244C          48      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_enable_power          0x0002247C          48      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_sd_clock       0x00021850          68      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_sd_clock       0x00021894          68      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_tm_clock       0x0002241C          48      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_error_recovery        0x0001749C         330      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_error_recovery_first_half
                             0x0001B10C         238      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_execute_tuning        0x00022A88          24      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_get_data3_0_level     0x00022B60          18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data7_4_level     0x00022B4E          18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data_bus_width    0x0002235E          52      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_get_default_cardclk_delay_chain
                             0x000227CC          36      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_interrupt_signal  0x00022C14          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_interrupt_status  0x00022BF8          14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_get_interrupt_status  0x00022C06          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_present_status    0x00022BEA          14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_init                  0x0001CBDC         206      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_is_bus_idle           0x0002291A          30      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_is_card_inserted      0x00022A40          26      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_is_ddr50_supported    0x00022C22          12      2  Init  Lc  hpm_sdmmc_port.c.o
  sdxc_is_inverse_clock_enabled
                             0x00022186          54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_is_inverse_clock_enabled
                             0x000221BC          54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_parse_interrupt_status
                             0x00014216         442      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_perform_auto_tuning   0x0001F752         118      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_perform_tuning_flow_sequence
                             0x0001C434         216      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_prepare_cmd_xfer      0x00017352         330      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_receive_cmd_response  0x0001C35C         216      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_reset                 0x0001FAC0         112      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_reset_tuning_engine   0x00022A26          26      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_select_cardclk_delay_source
                             0x00022BBA          16      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_select_voltage        0x000208AC          96      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_send_command          0x00021FDC          56      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_adma2_desc        0x00011B24         556      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_adma3_desc        0x00014738         434      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_adma_table_config
                             0x0001E030         156      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_cardclk_delay_chain
                             0x0002180C          68      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_set_data_bus_width    0x00020AE2          92      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_data_timeout      0x0001D846         166      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_dma_config        0x0001F33C         126      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_post_change_delay
                             0x00022150          54      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_set_speed_mode        0x00020EDE          84      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_transfer_config   0x0001EEA4         132      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_stop_clock_during_phase_code_change
                             0x00021ED2          58      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_transfer_cb           0x0001EC42         138      2  Init  Lc  ff_queue.o
  sdxc_transfer_nonblocking  0x000148E2         428      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_tuning_error_recovery
                             0x00021AEE          66      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_wait_card_active      0x00021D0A          60      2  Init  Lc  hpm_sdmmc_host.c.o
  set_pwm_waveform_edge_aligned_duty
                             0x00020E92          84      2  Init  Lc  timer.o
  set_pwm_waveform_edge_aligned_frequency
                             0x0001E65A         140      2  Init  Lc  timer.o
  show_error_string          0x00018ACE         296      2  Init  Gb  sd_fatfs.o
  st_clust                   0x000213EC          78      2  Init  Lc  ff.c.o
  st_dword                   0x000203C2         102      2  Init  Lc  ff.c.o
  st_qword                   0x00017814         322      2  Init  Lc  ff.c.o
  st_word                    0x00021AAC          66      2  Init  Lc  ff.c.o
  store_xdir                 0x0001BCC6         228      2  Init  Lc  ff.c.o
  sum_sfn                    0x000214C2          76      2  Init  Lc  ff.c.o
  sync_fs                    0x000189C6         296      2  Init  Lc  ff.c.o
  sync_window                0x0001E5D4         142      2  Init  Lc  ff.c.o
  tchar2uni                  0x00019220         280      2  Init  Lc  ff.c.o
  test_gpio_input_interrupt  0x0001CB1A         206      2  Init  Gb  main.o
  test_uart_recv_polling     0x0001F4AC         122      2  Init  Lc  uart.o
  tick_ms_isr                0x00000314          52      4  Init  Gb  timer.o
  timer_Init                 0x000228E2          32      2  Init  Gb  timer.o
  timer_config               0x0001B45C         236      2  Init  Lc  timer.o
  uart4sendmsg               0x00022986          28      2  Init  Gb  gd32f4xx_it.o
  uart_calculate_baudrate    0x00016E20         354      2  Init  Lc  hpm_uart_drv.c.o
  uart_default_config        0x00020D94          86      2  Init  Gb  hpm_uart_drv.c.o
  uart_dma_init              0x00015C88         386      2  Init  Gb  uart_dma.o
  uart_dma_output            0x00021FA8          56      2  Init  Gb  uart_dma.o
  uart_dma_recv_polling      0x00015B0A         386      2  Init  Gb  uart_dma.o
  uart_dma_tx_send           0x00013996         458      2  Init  Lc  uart_dma.o
  uart_flush                 0x00021B66          64      2  Init  Gb  hpm_uart_drv.c.o
  uart_get_current_recv_remaining_size
                             0x000226B0          40      2  Init  Lc  uart_dma.o
  uart_init                  0x00015696         396      2  Init  Gb  hpm_uart_drv.c.o
  uart_modem_config          0x00021CCE          60      2  Init  Lc  hpm_uart_drv.c.o
  uart_rx_dma_autorun        0x0001C8DC         210      2  Init  Lc  uart_dma.o
  uart_send_byte             0x0002139E          78      2  Init  Gb  hpm_uart_drv.c.o
  uart_tx_dma                0x000215E6          74      2  Init  Lc  uart_dma.o
  validate                   0x00020042         110      2  Init  Lc  ff.c.o
  xdir_sum                   0x0001FFD4         110      2  Init  Lc  ff.c.o
  xname_sum                  0x0001DD22         160      2  Init  Lc  ff.c.o
  xor_check                  0x0002159C          74      2  Init  Gb  frame_analysis.o
  xsum32                     0x000227A8          36      2  Init  Lc  ff.c.o

RAM function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x00000200  isr_gpio                           36      4  Init  Gb  main.o
  0x0000021C  default_isr_2                     246      4  Init  Gb  main.o
  0x00000314  tick_ms_isr                        52      4  Init  Gb  timer.o
  0x0000033C  default_isr_63                    248      4  Init  Gb  timer.o
  0x00000438  sdcard_isr                         28      4  Init  Gb  hpm_sdmmc_disk.c.o
  0x00000450  default_isr_111                   248      4  Init  Gb  hpm_sdmmc_disk.c.o
  0x00000554  irq_handler_trap                  488      4  Init  Gb  trap.c.o
  0x0000073E  fmc2sinsraw                     7 230      2  Init  Gb  readpaoche.o
  0x000022F8  AnalyticCoordinateAxis          6 598      2  Init  Gb  InsTestingEntry.o
  0x00003B9E  f_mkfs                          4 942      2  Init  Gb  ff.c.o
  0x00004DC0  frame_pack_and_send             4 466      2  Init  Gb  frame_analysis.o
  0x00005D86  output_gdw_do                   3 824      2  Init  Gb  INS_Output.o
  0x00006B9A  ComputeFn                       2 166      2  Init  Gb  kalman.o
  0x00007410  AlgorithmAct                    2 056      2  Init  Gb  InsTestingEntry.o
  0x00007BC0  mount_volume                    2 016      2  Init  Lc  ff.c.o
  0x0000830C  FPGATo422_00BB_send             1 612      2  Init  Gb  InsTestingEntry.o
  0x00008890  ReadFileToSd                    1 540      2  Init  Gb  sd_fatfs.o
  0x00008DEC  ErrStore_1s                     1 516      2  Init  Gb  kalman.o
  0x000093D8  Kalman_StartUp                  1 354      2  Init  Gb  kalman.o
  0x0000990A  ComputeZk                       1 350      2  Init  Gb  kalman.o
  0x00009E50  sd_decode_csd                   1 332      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0000A30C  create_name                     1 308      2  Init  Lc  ff.c.o
  0x0000A81C  f_lseek                         1 278      2  Init  Gb  ff.c.o
  0x0000ACFA  f_open                          1 250      2  Init  Gb  ff.c.o
  0x0000B18C  ErrCorrect_1_Navi_Time          1 222      2  Init  Gb  kalman.o
  0x0000B636  INS912_Output                   1 194      2  Init  Gb  INS_Output.o
  0x0000BA98  f_printf                        1 152      2  Init  Gb  ff.c.o
  0x0000BEEC  SetParaAll                      1 140      2  Init  Gb  SetParaBao.o
  0x0000C350  output_fpga_void                1 128      2  Init  Gb  INS_Output.o
  0x0000C788  dir_register                    1 056      2  Init  Lc  ff.c.o
  0x0000CB64  UartDmaRecSetPara               1 022      2  Init  Gb  SetParaBao.o
  0x0000CEF2  f_write                           976      2  Init  Gb  ff.c.o
  0x0000D2A6  f_async_write                     976      2  Init  Gb  ff.c.o
  0x0000D65A  Rk_Init                           974      2  Init  Gb  kalman.o
  0x0000DA28  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                                                968      2  Init  Gb  InsTestingEntry.o
  0x0000DDF0  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                                                968      2  Init  Gb  InsTestingEntry.o
  0x0000E1B8  analysisRxdata                    890      2  Init  Gb  uart.o
  0x0000E4E2  NaviCompute                       870      2  Init  Gb  navi.o
  0x0000E808  f_read                            868      2  Init  Gb  ff.c.o
  0x0000EB4C  InitParaToAlgorithm               786      2  Init  Gb  SetParaBao.o
  0x0000EE4A  sd_card_init                      784      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0000F0FA  SaveGNSSData                      774      2  Init  Gb  read_and_check_gnss_data.o
  0x0000F3F8  dir_find                          724      2  Init  Lc  ff.c.o
  0x0000F6A0  ReadParaFromFlash                 704      2  Init  Gb  SetParaBao.o
  0x0000F958  Mat_Inv                           700      2  Init  Gb  matvecmath.o
  0x0000FC14  sdmmchost_init_io                 696      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0000FEC0  fpgadata_Predo_chen_SetAlgParm_acc
                                                694      2  Init  Gb  InsTestingEntry.o
  0x0001015E  fpgadata_Predo_chen_OutDataSet
                                                686      2  Init  Gb  InsTestingEntry.o
  0x000103B8  create_chain                      678      2  Init  Lc  ff.c.o
  0x00010638  f_sync                            670      2  Init  Gb  ff.c.o
  0x0001088E  create_partition                  668      2  Init  Lc  ff.c.o
  0x00010B16  get_fat                           658      2  Init  Lc  ff.c.o
  0x00010D88  fpgadata_Predo_chen_SetAlgParm_gyro
                                                640      2  Init  Gb  InsTestingEntry.o
  0x00010FF0  CnbToQ                            640      2  Init  Gb  navi.o
  0x00011270  DynamicInertialSysAlignCompute
                                                606      2  Init  Gb  dynamic_align.o
  0x00011498  RestoreFactory                    600      2  Init  Gb  SetParaBao.o
  0x000116D0  ComputeSi                         582      2  Init  Gb  dynamic_align.o
  0x00011902  put_fat                           574      2  Init  Lc  ff.c.o
  0x00011B24  sdxc_set_adma2_desc               556      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00011D50  follow_path                       552      2  Init  Lc  ff.c.o
  0x00011F5C  dma_setup_channel                 548      2  Init  Gb  hpm_dma_drv.c.o
  0x00012180  remove_chain                      520      2  Init  Lc  ff.c.o
  0x00012374  QToCnb                            518      2  Init  Gb  navi.o
  0x0001257A  load_xdir                         516      2  Init  Lc  ff.c.o
  0x00012762  sd_write_blocks                   514      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0001294C  dir_read                          506      2  Init  Lc  ff.c.o
  0x00012B30  sd_read_blocks                    490      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00012D0E  InertialSysAlignCompute           486      2  Init  Gb  align.o
  0x00012ED4  ComputeKk                         484      2  Init  Gb  kalman.o
  0x000130A4  check_fs                          476      2  Init  Lc  ff.c.o
  0x00013260  sd_start_write_blocks             472      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00013420  dma_config_linked_descriptor
                                                468      2  Init  Gb  hpm_dma_drv.c.o
  0x000135F4  GNSSAndHeadDataTest               468      2  Init  Gb  read_and_check_gnss_data.o
  0x000137C8  CorrectAtti                       466      2  Init  Gb  kalman.o
  0x00013996  uart_dma_tx_send                  458      2  Init  Lc  uart_dma.o
  0x00013B38  putc_bfd                          458      2  Init  Lc  ff.c.o
  0x00013CF6  mcusendtopcdriversdata            452      2  Init  Gb  gdwatch.o
  0x00013EAA  SetParaGnssInitValue              448      2  Init  Gb  SetParaBao.o
  0x0001405A  sd_decode_status                  444      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00014216  sdxc_parse_interrupt_status
                                                442      2  Init  Gb  hpm_sdxc_drv.c.o
  0x000143CC  ComputeQ                          440      2  Init  Gb  navi.o
  0x00014580  ComputeFk                         440      2  Init  Gb  kalman.o
  0x00014738  sdxc_set_adma3_desc               434      2  Init  Gb  hpm_sdxc_drv.c.o
  0x000148E2  sdxc_transfer_nonblocking         428      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00014A82  f_unlink                          418      2  Init  Gb  ff.c.o
  0x00014C00  CnbToAtti                         416      2  Init  Gb  navi.o
  0x00014DA0  ComputeVn                         410      2  Init  Gb  navi.o
  0x00014F3A  sdmmchost_power_control           408      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000150C2  SysInit                           404      2  Init  Gb  navi.o
  0x0001524E  ld_qword                          400      2  Init  Lc  ff.c.o
  0x000153DE  SetParaUpdateStart                400      2  Init  Gb  SetParaBao.o
  0x00015552  ParaUpdateHandle                  400      2  Init  Gb  SetParaBao.o
  0x00015696  uart_init                         396      2  Init  Gb  hpm_uart_drv.c.o
  0x0001581A  SetParaFactorGyro                 392      2  Init  Gb  SetParaBao.o
  0x00015992  SetParaFactorAcc                  392      2  Init  Gb  SetParaBao.o
  0x00015B0A  uart_dma_recv_polling             386      2  Init  Gb  uart_dma.o
  0x00015C88  uart_dma_init                     386      2  Init  Gb  uart_dma.o
  0x00015DF4  initializationdriversettings
                                                384      2  Init  Gb  gdwatch.o
  0x00015F28  output_fpgatxt_do                 382      2  Init  Gb  INS_Output.o
  0x0001607E  bsp_gpio_init                     380      2  Init  Gb  bsp_gpio.o
  0x000161C2  WriteBB00FileToSd                 380      2  Init  Gb  sd_fatfs.o
  0x00016316  Hk_Init                           380      2  Init  Gb  kalman.o
  0x00016492  gen_numname                       376      2  Init  Lc  ff.c.o
  0x00016606  ff_wtoupper                       370      2  Init  Gb  ffunicode.c.o
  0x00016778  board_init_sd_host_params         370      2  Init  Wk  hpm_sdmmc_port.c.o
  0x000168E6  sdmmchost_transfer                368      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00016A2E  ReadPara_4                        368      2  Init  Gb  SetParaBao.o
  0x00016B8E  KalCompute                        364      2  Init  Gb  kalman.o
  0x00016CDA  sd_probe_bus_voltage              362      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00016E20  uart_calculate_baudrate           354      2  Init  Lc  hpm_uart_drv.c.o
  0x00016F82  dir_next                          352      2  Init  Lc  ff.c.o
  0x000170D2  SetParaBaud                       350      2  Init  Gb  SetParaBao.o
  0x00017218  SetParaUpdateSend                 338      2  Init  Gb  SetParaBao.o
  0x00017352  sdxc_prepare_cmd_xfer             330      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0001749C  sdxc_error_recovery               330      2  Init  Gb  hpm_sdxc_drv.c.o
  0x000175CE  fpgadata_Predo_chen_preAlgParm_370
                                                326      2  Init  Gb  InsTestingEntry.o
  0x000176D0  Qua_Mul                           324      2  Init  Gb  matvecmath.o
  0x00017814  st_qword                          322      2  Init  Lc  ff.c.o
  0x00017956  gptmr_channel_config              322      2  Init  Gb  hpm_gptmr_drv.c.o
  0x00017A98  gnss_check_bind                   322      2  Init  Gb  InsTestingEntry.o
  0x00017BCE  ReadPara_2                        322      2  Init  Gb  SetParaBao.o
  0x00017D00  sd_switch_function                320      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00017E34  SetParaVector                     320      2  Init  Gb  SetParaBao.o
  0x00017F64  SetParaGnss                       320      2  Init  Gb  SetParaBao.o
  0x00018094  SetParaDeviation                  320      2  Init  Gb  SetParaBao.o
  0x000181C4  SetParaAngle                      320      2  Init  Gb  SetParaBao.o
  0x000182F4  AttiToCnb                         318      2  Init  Gb  navi.o
  0x00018432  SetParaUpdateEnd                  310      2  Init  Gb  SetParaBao.o
  0x00018544  SaveParaToFlash                   310      2  Init  Gb  SetParaBao.o
  0x00018662  ComputeVibn                       308      2  Init  Gb  navi.o
  0x00018786  sd_disk_initialize                306      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00018898  ComputePos                        302      2  Init  Gb  navi.o
  0x000189C6  sync_fs                           296      2  Init  Lc  ff.c.o
  0x00018ACE  show_error_string                 296      2  Init  Gb  sd_fatfs.o
  0x00018BF6  fpgadata_syn_count_do             296      2  Init  Gb  fpgad.o
  0x00018CC6  sd_polling_card_status_busy
                                                290      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00018DDC  SetParaCoord                      286      2  Init  Gb  SetParaBao.o
  0x00018EEA  sd_set_bus_timing                 282      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00018FF8  create_xdir                       282      2  Init  Lc  ff.c.o
  0x00019106  rom_xpi_nor_erase_sector           58      2  Init  Lc  flash.o
  0x00019140  rom_xpi_nor_erase_block            58      2  Init  Lc  flash.o
  0x0001917A  rom_xpi_nor_erase_chip             54      2  Init  Lc  flash.o
  0x000191B0  rom_xpi_nor_program                66      2  Init  Lc  flash.o
  0x000191F2  rom_xpi_nor_auto_config            46      2  Init  Lc  flash.o
  0x00019220  tchar2uni                         280      2  Init  Lc  ff.c.o
  0x00019338  get_fpgadata_do                   280      2  Init  Gb  fpgad.o
  0x0001941C  dir_sdi                           280      2  Init  Lc  ff.c.o
  0x0001952C  SetParaFrequency                  280      2  Init  Gb  SetParaBao.o
  0x00019634  SetParaCalibration                278      2  Init  Gb  SetParaBao.o
  0x0001973A  SetParaSdHandle                   276      2  Init  Gb  SetParaBao.o
  0x0001982A  ReadPara_1                        276      2  Init  Gb  SetParaBao.o
  0x0001992E  Fatfs_Init                        276      2  Init  Gb  sd_fatfs.o
  0x00019A16  ComputeAttiRate                   270      2  Init  Gb  navi.o
  0x00019B24  get_boot_reason                   268      2  Init  Gb  main.o
  0x00019C30  ReadPara                          268      2  Init  Gb  SetParaBao.o
  0x00019D22  ReadPara_3                        266      2  Init  Gb  SetParaBao.o
  0x00019E1C  ReadPara_0                        266      2  Init  Gb  SetParaBao.o
  0x00019F16  DynamicNavi_Init                  266      2  Init  Gb  navi.o
  0x0001A00C  main                              264      2  Init  Gb  main.o
  0x0001A0CA  cmp_lfn                           264      2  Init  Lc  ff.c.o
  0x0001A1C2  SetParaDataOutType                258      2  Init  Gb  SetParaBao.o
  0x0001A2AC  TIMER2_IRQHandler                 258      2  Init  Gb  gd32f4xx_it.o
  0x0001A352  SetParaTime                       256      2  Init  Gb  SetParaBao.o
  0x0001A442  caninfupdate                      254      2  Init  Gb  INS_Data.o
  0x0001A528  sdmmchost_vsel_pin_control
                                                250      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001A61E  sdmmchost_start_transfer          250      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001A700  sd_read_status                    250      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0001A7EA  SetParaKalmanR                    250      2  Init  Gb  SetParaBao.o
  0x0001A8D4  SetParaKalmanQ                    250      2  Init  Gb  SetParaBao.o
  0x0001A9BE  SetParaFilter                     250      2  Init  Gb  SetParaBao.o
  0x0001AAA8  put_lfn                           248      2  Init  Lc  ff.c.o
  0x0001AB98  Qk_Init                           246      2  Init  Gb  kalman.o
  0x0001AC8E  Pk_Init                           246      2  Init  Gb  kalman.o
  0x0001AD84  pick_lfn                          240      2  Init  Lc  ff.c.o
  0x0001AE6C  SetParaGyroType                   240      2  Init  Gb  SetParaBao.o
  0x0001AF4C  SetParaGpsType                    240      2  Init  Gb  SetParaBao.o
  0x0001B02C  SetParaDebugMode                  240      2  Init  Gb  SetParaBao.o
  0x0001B10C  sdxc_error_recovery_first_half
                                                238      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0001B1EA  sdmmchost_init                    238      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001B2AC  sdmmc_card_async_write            238      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x0001B386  sdmmchost_irq_handler             238      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001B45C  timer_config                      236      2  Init  Lc  timer.o
  0x0001B538  find_bitmap                       236      2  Init  Lc  ff.c.o
  0x0001B620  FinishInertialSysAlign            236      2  Init  Gb  align.o
  0x0001B6EC  FinishDynamicInertialSysAlign
                                                236      2  Init  Gb  dynamic_align.o
  0x0001B7B8  sdmmchost_switch_to_1v8           234      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001B87A  hpm_sdmmc_osal_event_wait         232      2  Init  Wk  hpm_sdmmc_osal.o
  0x0001B95A  SetParaUpdateStop                 232      2  Init  Gb  SetParaBao.o
  0x0001BA2E  ComputeCie                        232      2  Init  Gb  align.o
  0x0001BB16  sd_send_scr                       230      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001BBEC  GNSS_Valid_PPSStart               230      2  Init  Gb  InsTestingEntry.o
  0x0001BCC6  store_xdir                        228      2  Init  Lc  ff.c.o
  0x0001BD96  Virtual_PPS_insert_5hz            228      2  Init  Gb  InsTestingEntry.o
  0x0001BE6E  change_bitmap                     224      2  Init  Lc  ff.c.o
  0x0001BF4A  dir_alloc                         222      2  Init  Lc  ff.c.o
  0x0001C01C  sdmmc_card_write                  220      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x0001C0F0  sdmmc_card_read                   220      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x0001C1C4  Navi_Init                         218      2  Init  Gb  navi.o
  0x0001C292  Kalman_Init                       218      2  Init  Gb  kalman.o
  0x0001C35C  sdxc_receive_cmd_response         216      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001C434  sdxc_perform_tuning_flow_sequence
                                                216      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001C4F4  WriteFileOpenFromSd               216      2  Init  Gb  sd_fatfs.o
  0x0001C5AC  ComputePk                         216      2  Init  Gb  kalman.o
  0x0001C67C  ComputeCib0i                      216      2  Init  Gb  align.o
  0x0001C73C  extract_csd_field                 214      2  Init  Gb  hpm_sdmmc_common.c.o
  0x0001C812  dir_remove                        214      2  Init  Lc  ff.c.o
  0x0001C8DC  uart_rx_dma_autorun               210      2  Init  Lc  uart_dma.o
  0x0001C99A  sdmmchost_send_command            210      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001CA4E  ComputeDelSenbb                   208      2  Init  Gb  navi.o
  0x0001CB1A  test_gpio_input_interrupt         206      2  Init  Gb  main.o
  0x0001CBDC  sdxc_init                         206      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001CC9E  find_volume                       206      2  Init  Lc  ff.c.o
  0x0001CD60  ff_queue_push                     206      2  Init  Gb  ff_queue.o
  0x0001CE26  LEDIndicator                      204      2  Init  Gb  INS_Init.o
  0x0001CED2  INS912AlgorithmEntry              204      2  Init  Gb  InsTestingEntry.o
  0x0001CF74  ComputeXk                         204      2  Init  Gb  kalman.o
  0x0001D038  ff_uni2oem                        198      2  Init  Gb  ffunicode.c.o
  0x0001D0FE  sd_init                           196      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0001D1AA  SdFileOperateTypeSet              196      2  Init  Gb  sd_fatfs.o
  0x0001D24C  ComputeG                          196      2  Init  Gb  navi.o
  0x0001D310  ComputeWnbb                       192      2  Init  Gb  navi.o
  0x0001D3CC  ComputeCen                        188      2  Init  Gb  align.o
  0x0001D488  ComputeVi                         186      2  Init  Gb  align.o
  0x0001D52E  pnavout_set                       178      2  Init  Gb  InsTestingEntry.o
  0x0001D5E0  ComputeSib0                       178      2  Init  Gb  dynamic_align.o
  0x0001D68C  WriteFileToSd                     172      2  Init  Gb  sd_fatfs.o
  0x0001D704  DeleteFileFromSd                  172      2  Init  Gb  sd_fatfs.o
  0x0001D7A0  get_ldnumber                      170      2  Init  Lc  ff.c.o
  0x0001D846  sdxc_set_data_timeout             166      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001D8E4  WriteCOM3FileToSd                 166      2  Init  Gb  sd_fatfs.o
  0x0001D982  sd_transfer                       164      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001DA1A  dir_clear                         164      2  Init  Lc  ff.c.o
  0x0001DAB2  CloseFileToSd                     164      2  Init  Gb  sd_fatfs.o
  0x0001DB46  sd_convert_data_endian            162      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001DBE4  Mat_Mul                           162      2  Init  Gb  matvecmath.o
  0x0001DC86  ComputePkk_1_Step2                162      2  Init  Gb  kalman.o
  0x0001DD22  xname_sum                         160      2  Init  Lc  ff.c.o
  0x0001DDBE  sdmmchost_is_card_detected
                                                160      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001DE5A  sd_disk_ioctl                     160      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x0001DEFA  DynamicInertialSysAlign_Init
                                                160      2  Init  Gb  dynamic_align.o
  0x0001DF9A  UartIrqSendMsg                    158      2  Init  Gb  uart.o
  0x0001E030  sdxc_set_adma_table_config
                                                156      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001E0C0  SysVarDefaultSet                  156      2  Init  Gb  navi.o
  0x0001E15C  InertialSysAlign_Init             156      2  Init  Gb  align.o
  0x0001E1F4  ComputeRmRn                       156      2  Init  Gb  navi.o
  0x0001E290  sd_decode_scr                     154      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001E32A  get_fpgadata_after_otherDataDo
                                                154      2  Init  Gb  fpgad.o
  0x0001E3AC  ff_queue_get_buffer_by_sector
                                                152      2  Init  Gb  ff_queue.o
  0x0001E444  sd_set_bus_width                  144      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001E4CC  ff_queue_poll                     144      2  Init  Gb  ff_queue.o
  0x0001E554  f_mount                           144      2  Init  Gb  ff.c.o
  0x0001E5D4  sync_window                       142      2  Init  Lc  ff.c.o
  0x0001E65A  set_pwm_waveform_edge_aligned_frequency
                                                140      2  Init  Lc  timer.o
  0x0001E6CA  UpdateStop_SetHead                140      2  Init  Gb  SetParaBao.o
  0x0001E756  UpdateStart_SetHead               140      2  Init  Gb  SetParaBao.o
  0x0001E7E2  UpdateSend_SetHead                140      2  Init  Gb  SetParaBao.o
  0x0001E86E  UpdateEnd_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001E8FA  SendPara_SetHead                  140      2  Init  Gb  SetParaBao.o
  0x0001E986  ReadPara4_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EA12  ReadPara3_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EA9E  ReadPara2_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EB2A  ReadPara1_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EBB6  ReadPara0_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EC42  sdxc_transfer_cb                  138      2  Init  Lc  ff_queue.o
  0x0001ECCC  UartIrqInit                       136      2  Init  Gb  uart.o
  0x0001ED50  INS_Init                          136      2  Init  Gb  INS_Init.o
  0x0001EDB0  WriteCom3FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  0x0001EE2A  WriteBB00FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  0x0001EEA4  sdxc_set_transfer_config          132      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0001EF28  sduart_recv_polling               132      2  Init  Gb  uart.o
  0x0001EF8C  load_obj_xdir                     130      2  Init  Lc  ff.c.o
  0x0001F006  Vec_Cross                         130      2  Init  Gb  matvecmath.o
  0x0001F088  SdFileReadOperate                 130      2  Init  Gb  sd_fatfs.o
  0x0001F0FE  sdmmc_enable_auto_tuning          128      2  Init  Gb  hpm_sdmmc_common.c.o
  0x0001F168  sd_send_csd                       128      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001F1E0  sd_mount_fs                       128      2  Init  Lc  sd_fatfs.o
  0x0001F254  ReadCom3FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  0x0001F2C8  ReadBB00FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  0x0001F33C  sdxc_set_dma_config               126      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001F3BA  sd_mkfs                           126      2  Init  Lc  sd_fatfs.o
  0x0001F430  ComputeDeg_Ex                     124      2  Init  Gb  navi.o
  0x0001F4AC  test_uart_recv_polling            122      2  Init  Lc  uart.o
  0x0001F512  sdmmchost_set_cardclk_delay_chain
                                                122      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001F570  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_osal.o
  0x0001F5EA  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001F664  SaveINSData                       122      2  Init  Gb  kalman.o
  0x0001F6DE  KalPredict                        122      2  Init  Gb  kalman.o
  0x0001F752  sdxc_perform_auto_tuning          118      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001F7AC  sd_all_send_cid                   118      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001F81E  SendVersionInfo                   118      2  Init  Gb  datado.o
  0x0001F88C  sdmmc_select_card                 116      2  Init  Gb  hpm_sdmmc_common.c.o
  0x0001F8FC  sd_error_recovery                 116      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001F96C  ComputeXkk_1                      116      2  Init  Gb  kalman.o
  0x0001F9E0  gptmr_channel_get_default_config
                                                114      2  Init  Gb  hpm_gptmr_drv.c.o
  0x0001FA52  fill_last_frag                    114      2  Init  Lc  ff.c.o
  0x0001FAC0  sdxc_reset                        112      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001FB30  sd_send_if_cond                   112      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001FB9C  UpdateStop_SetEnd                 112      2  Init  Gb  SetParaBao.o
  0x0001FC08  UpdateStart_SetEnd                112      2  Init  Gb  SetParaBao.o
  0x0001FC74  UpdateSend_SetEnd                 112      2  Init  Gb  SetParaBao.o
  0x0001FCE0  UpdateEnd_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0001FD4C  SendPara_SetEnd                   112      2  Init  Gb  SetParaBao.o
  0x0001FDB8  ReadPara4_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0001FE24  ReadPara3_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0001FE90  ReadPara2_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0001FEFC  ReadPara1_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0001FF68  ReadPara0_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0001FFD4  xdir_sum                          110      2  Init  Lc  ff.c.o
  0x00020042  validate                          110      2  Init  Lc  ff.c.o
  0x000200AC  sd_app_cmd_send_cond_op           108      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00020110  f_close                           108      2  Init  Gb  ff.c.o
  0x0002016A  disk_ioctl                        108      2  Init  Gb  diskio.c.o
  0x000201D2  ComputeVib0                       108      2  Init  Gb  align.o
  0x0002023A  sd_app_cmd_set_write_block_erase_count
                                                106      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0002029A  SDUartIrqInit                     106      2  Init  Gb  uart.o
  0x00020300  move_window                       104      2  Init  Lc  ff.c.o
  0x0002035E  fill_first_frag                   104      2  Init  Lc  ff.c.o
  0x000203C2  st_dword                          102      2  Init  Lc  ff.c.o
  0x00020428  sd_send_card_status               102      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0002048A  myget_16bit_D64                   102      2  Init  Gb  readpaoche.o
  0x000204F0  disk_write                        102      2  Init  Gb  diskio.c.o
  0x00020552  disk_sync_read                    102      2  Init  Gb  diskio.c.o
  0x000205B2  disk_read                         102      2  Init  Gb  diskio.c.o
  0x00020614  disk_async_write                  102      2  Init  Gb  diskio.c.o
  0x00020676  Read_And_Check_GNSS_Data          102      2  Init  Gb  read_and_check_gnss_data.o
  0x000206D4  BindDefaultSet_by_GNSS            102      2  Init  Gb  navi.o
  0x0002073A  ComputePkk_1_Step1                100      2  Init  Gb  kalman.o
  0x00020796  sd_disk_status                     98      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x000207F0  sd_be2le                           98      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00020852  init_alloc_info                    98      2  Init  Lc  ff.c.o
  0x000208AC  sdxc_select_voltage                96      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0002090C  sdmmc_send_application_command
                                                 96      2  Init  Gb  hpm_sdmmc_common.c.o
  0x00020968  sd_send_rca                        96      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000209C2  core_local_mem_to_sys_address
                                                 96      2  Init  Lc  uart_dma.o
  0x00020A22  core_local_mem_to_sys_address
                                                 96      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x00020A82  core_local_mem_to_sys_address
                                                 96      2  Init  Lc  hpm_sdmmc_port.c.o
  0x00020AE2  sdxc_set_data_bus_width            92      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00020B3A  sdmmchost_enable_sdio_interrupt
                                                 92      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00020B8A  fpgadata_Predo_chen_algParmCash
                                                 92      2  Init  Gb  InsTestingEntry.o
  0x00020BE6  sd_disk_sync_read                  90      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00020C38  sd_check_card_parameters           90      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00020C8E  ComputeWenn                        90      2  Init  Gb  navi.o
  0x00020CE8  ff_queue_init                      88      2  Init  Gb  ff_queue.o
  0x00020D3C  ComputeWien                        88      2  Init  Gb  navi.o
  0x00020D94  uart_default_config                86      2  Init  Gb  hpm_uart_drv.c.o
  0x00020DEA  sd_host_init                       86      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00020E3C  UpdateAlignPosAndVn                86      2  Init  Gb  dynamic_align.o
  0x00020E92  set_pwm_waveform_edge_aligned_duty
                                                 84      2  Init  Lc  timer.o
  0x00020EDE  sdxc_set_speed_mode                84      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00020F32  sdmmc_set_block_size               84      2  Init  Gb  hpm_sdmmc_common.c.o
  0x00020F82  gpiom_set_pin_controller           84      2  Init  Lc  bsp_gpio.o
  0x00020FD6  gpiom_set_pin_controller           84      2  Init  Lc  main.o
  0x0002102A  ReadFileOpenFromSd                 84      2  Init  Gb  sd_fatfs.o
  0x0002106E  AlgorithmDo                        84      2  Init  Gb  InsTestingEntry.o
  0x000210AE  sdmmc_go_idle_state                82      2  Init  Gb  hpm_sdmmc_common.c.o
  0x000210FC  sd_send_cmd                        82      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00021146  gpio_write_pin                     82      2  Init  Lc  INS_Init.o
  0x00021198  Mat_Tr                             82      2  Init  Gb  matvecmath.o
  0x000211EA  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0002123A  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x0002128A  sdmmchost_wait_xfer_done           80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000212CE  sdmmchost_wait_idle                80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00021316  sdmmchost_wait_command_done
                                                 80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x0002135A  sdmmchost_get_data_pin_level
                                                 80      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0002139E  uart_send_byte                     78      2  Init  Gb  hpm_uart_drv.c.o
  0x000213EC  st_clust                           78      2  Init  Lc  ff.c.o
  0x0002142E  norflash_init                      78      2  Init  Gb  flash.o
  0x00021474  GNSS_Lost_Time                     78      2  Init  Gb  InsTestingEntry.o
  0x000214C2  sum_sfn                            76      2  Init  Lc  ff.c.o
  0x0002150E  sdmmchost_select_voltage           76      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00021556  sd_switch_voltage                  76      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0002159C  xor_check                          74      2  Init  Gb  frame_analysis.o
  0x000215E6  uart_tx_dma                        74      2  Init  Lc  uart_dma.o
  0x00021628  putc_flush                         74      2  Init  Lc  ff.c.o
  0x0002166E  gptmr_channel_reset_count          74      2  Init  Lc  timer.o
  0x000216B8  fpgadata_Predo                     74      2  Init  Gb  InsTestingEntry.o
  0x000216E0  ComputeLeverArmVn                  74      2  Init  Gb  navi.o
  0x0002171C  ld_clust                           72      2  Init  Lc  ff.c.o
  0x0002175C  fpgadata_Predo_chen                72      2  Init  Gb  InsTestingEntry.o
  0x00021788  disk_status                        70      2  Init  Gb  diskio.c.o
  0x000217CA  disk_initialize                    70      2  Init  Gb  diskio.c.o
  0x0002180C  sdxc_set_cardclk_delay_chain
                                                 68      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00021850  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00021894  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000218D8  sd_select_card                     68      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00021918  sd_disk_async_write                68      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00021958  ld_dword                           68      2  Init  Lc  ff.c.o
  0x0002199C  dbc_1st                            68      2  Init  Lc  ff.c.o
  0x000219E0  comm_param_setbits                 68      2  Init  Gb  computerFrameParse.o
  0x00021A24  TransHeading0to360                 68      2  Init  Gb  navi.o
  0x00021A68  GNSS_Last_TIME                     68      2  Init  Gb  InsTestingEntry.o
  0x00021AAC  st_word                            66      2  Init  Lc  ff.c.o
  0x00021AEE  sdxc_tuning_error_recovery
                                                 66      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00021B24  gptmr_update_cmp                   66      2  Init  Lc  timer.o
  0x00021B66  uart_flush                         64      2  Init  Gb  hpm_uart_drv.c.o
  0x00021BA6  ff_queue_is_full                   64      2  Init  Gb  ff_queue.o
  0x00021BE6  CorrectVn                          64      2  Init  Gb  kalman.o
  0x00021C26  sdmmchost_set_card_bus_width
                                                 62      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00021C5C  sdmmchost_check_host_availability
                                                 62      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00021C96  Drv_FlashWrite                     62      2  Init  Gb  FirmwareUpdateFile.o
  0x00021CCE  uart_modem_config                  60      2  Init  Lc  hpm_uart_drv.c.o
  0x00021D0A  sdxc_wait_card_active              60      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00021D46  sdmmc_get_card_and_aligned_buf_info
                                                 60      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x00021D82  sd_disk_write                      60      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00021DBA  sd_disk_read                       60      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00021DF2  rom_xpi_nor_read                   60      2  Init  Lc  flash.o
  0x00021E2E  dmamux_config                      60      2  Init  Lc  uart_dma.o
  0x00021E6A  dma_default_channel_config
                                                 60      2  Init  Gb  hpm_dma_drv.c.o
  0x00021EA6  DeviceInit                         60      2  Init  Gb  datado.o
  0x00021ED2  sdxc_stop_clock_during_phase_code_change
                                                 58      2  Init  Lc  hpm_sdmmc_common.c.o
  0x00021F0C  norflash_write                     58      2  Init  Gb  flash.o
  0x00021F3E  norflash_read                      58      2  Init  Gb  flash.o
  0x00021F6E  ld_word                            58      2  Init  Lc  ff.c.o
  0x00021FA8  uart_dma_output                    56      2  Init  Gb  uart_dma.o
  0x00021FDC  sdxc_send_command                  56      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0002200C  sdxc_enable_auto_tuning            56      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022044  sdmmchost_set_speed_mode           56      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022078  myget_16bit_D32                    56      2  Init  Gb  readpaoche.o
  0x000220B0  crc_verify_8bit                    56      2  Init  Gb  app_tool.o
  0x000220E8  app_accum_verify_8bit              56      2  Init  Gb  app_tool.o
  0x00022120  ComputeLeverArmSn                  56      2  Init  Gb  navi.o
  0x00022150  sdxc_set_post_change_delay
                                                 54      2  Init  Lc  hpm_sdmmc_common.c.o
  0x00022186  sdxc_is_inverse_clock_enabled
                                                 54      2  Init  Lc  hpm_sdxc_drv.c.o
  0x000221BC  sdxc_is_inverse_clock_enabled
                                                 54      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000221F2  sdxc_enable_interrupt_status
                                                 54      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022228  sdxc_enable_interrupt_status
                                                 54      2  Init  Lc  hpm_sdmmc_host.c.o
  0x0002225E  sdxc_enable_interrupt_signal
                                                 54      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022294  myget_16bit_I32                    54      2  Init  Gb  readpaoche.o
  0x000222CA  f_chdrive                          54      2  Init  Gb  ff.c.o
  0x000222F8  clst2sect                          54      2  Init  Lc  ff.c.o
  0x0002232E  Drv_FlashErase                     54      2  Init  Gb  FirmwareUpdateFile.o
  0x0002235E  sdxc_get_data_bus_width            52      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00022392  sdmmchost_set_card_clock           52      2  Init  Gb  hpm_sdmmc_host.c.o
  0x000223C6  norflash_erase_sector              50      2  Init  Gb  flash.o
  0x000223F0  Uart_SendMsg                       50      2  Init  Gb  bsp_fmc.o
  0x0002241C  sdxc_enable_tm_clock               48      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0002244C  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_common.c.o
  0x0002247C  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000224AC  Pwm_Init                           46      2  Init  Gb  timer.o
  0x000224D2  gptmr_stop_counter                 44      2  Init  Lc  timer.o
  0x000224FE  gptmr_start_counter                44      2  Init  Lc  timer.o
  0x0002252A  gpio_set_pin_output                44      2  Init  Lc  bsp_gpio.o
  0x00022556  gpio_set_pin_input                 44      2  Init  Lc  main.o
  0x00022582  gpio_enable_pin_interrupt          44      2  Init  Lc  main.o
  0x000225AE  gpio_clear_pin_interrupt_flag
                                                 44      2  Init  Lc  main.o
  0x000225DA  get_fpgadata                       44      2  Init  Gb  fpgad.o
  0x000225F4  SdFileWriteOperate                 44      2  Init  Gb  sd_fatfs.o
  0x00022614  Exti_Init                          44      2  Init  Gb  main.o
  0x00022636  sd_set_max_current                 42      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0002265C  gpio_toggle_pin                    42      2  Init  Lc  main.o
  0x00022686  ff_queue_is_empty                  42      2  Init  Gb  ff_queue.o
  0x000226B0  uart_get_current_recv_remaining_size
                                                 40      2  Init  Lc  uart_dma.o
  0x000226D2  sdmmchost_wait_card_active
                                                 40      2  Init  Gb  hpm_sdmmc_host.c.o
  0x000226F0  hpm_sdmmc_osal_event_create
                                                 40      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022718  putc_init                          38      2  Init  Lc  ff.c.o
  0x0002273E  myget_8bit_I16                     38      2  Init  Gb  readpaoche.o
  0x00022764  hpm_sdmmc_osal_delay               38      2  Init  Wk  hpm_sdmmc_osal.o
  0x0002278A  ff_handle_poll                     38      2  Init  Gb  ff_queue.o
  0x000227A8  xsum32                             36      2  Init  Lc  ff.c.o
  0x000227CC  sdxc_get_default_cardclk_delay_chain
                                                 36      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000227F0  sdmmc_get_sys_addr                 36      2  Init  Gb  hpm_sdmmc_port.c.o
  0x00022810  gptmr_check_status                 36      2  Init  Lc  timer.o
  0x00022834  sdmmchost_switch_to_3v3_as_needed
                                                 34      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022852  sdmmchost_register_xfer_complete_callback
                                                 34      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022874  sdmmchost_error_recovery           34      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022892  output_normal_do                   34      2  Init  Gb  INS_Output.o
  0x000228AA  init_gpio                          34      2  Init  Gb  main.o
  0x000228CC  EXTI3_IRQHandler                   34      2  Init  Gb  gd32f4xx_it.o
  0x000228E2  timer_Init                         32      2  Init  Gb  timer.o
  0x000228FE  Drv_FlashRead                      32      2  Init  Gb  FirmwareUpdateFile.o
  0x0002291A  sdxc_is_bus_idle                   30      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00022938  sd_is_card_present                 30      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00022952  loopDoOther                        30      2  Init  Gb  datado.o
  0x00022968  hpm_sdmmc_osal_event_clear
                                                 30      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022986  uart4sendmsg                       28      2  Init  Gb  gd32f4xx_it.o
  0x0002299E  sdmmchost_is_voltage_switch_supported
                                                 28      2  Init  Gb  hpm_sdmmc_host.c.o
  0x000229BA  sdmmchost_delay_ms                 28      2  Init  Gb  hpm_sdmmc_host.c.o
  0x000229D0  gptmr_enable_irq                   28      2  Init  Lc  timer.o
  0x000229EC  gd_eval_com_init                   28      2  Init  Gb  INS_Init.o
  0x00022A00  bsp_tim_init                       28      2  Init  Gb  bsp_tim.o
  0x00022A10  Led_Control                        28      2  Init  Gb  main.o
  0x00022A26  sdxc_reset_tuning_engine           26      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022A40  sdxc_is_card_inserted              26      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022A5A  hpm_sdmmc_osal_event_set           26      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022A74  Drv_SystemReset                    26      2  Init  Gb  FirmwareUpdateFile.o
  0x00022A88  sdxc_execute_tuning                24      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022AA0  sdmmchost_is_8bit_supported
                                                 24      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022AB8  protocol_send                      24      2  Init  Gb  computerFrameParse.o
  0x00022ACC  dma_get_remaining_transfer_size
                                                 24      2  Init  Lc  uart_dma.o
  0x00022AE4  delay_ms                           24      2  Init  Gb  systick.o
  0x00022AFC  Algorithm_before_otherDataDo
                                                 24      2  Init  Gb  datado.o
  0x00022B10  ACC_gyroreset_r_TAFEAG16_buf
                                                 24      2  Init  Gb  InsTestingEntry.o
  0x00022B24  get_uart_tx_idle                   22      2  Init  Gb  uart_dma.o
  0x00022B3A  gptmr_clear_status                 20      2  Init  Lc  timer.o
  0x00022B4E  sdxc_get_data7_4_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022B60  sdxc_get_data3_0_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022B72  sdxc_clear_interrupt_status
                                                 18      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022B84  sdxc_clear_interrupt_status
                                                 18      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022B96  ppor_sw_reset                      18      2  Init  Lc  FirmwareUpdateFile.o
  0x00022BA8  mchtmr_get_count                   18      2  Init  Lc  sd_fatfs.o
  0x00022BBA  sdxc_select_cardclk_delay_source
                                                 16      2  Init  Lc  hpm_sdmmc_common.c.o
  0x00022BCA  comm_read_currentFreq              16      2  Init  Gb  computerFrameParse.o
  0x00022BDA  comm_axis_read                     16      2  Init  Gb  computerFrameParse.o
  0x00022BEA  sdxc_get_present_status            14      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022BF8  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022C06  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022C14  sdxc_get_interrupt_signal          14      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022C22  sdxc_is_ddr50_supported            12      2  Init  Lc  hpm_sdmmc_port.c.o
  0x00022C2E  sd_deinit                          10      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00022C38  get_fpgadata_before                 4      2  Init  Gb  fpgad.o
  0x00022C3C  comm_nav_para_syn                   4      2  Init  Gb  computerFrameParse.o

RAM function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  fmc2sinsraw                     7 230      2  Init  Gb  readpaoche.o
  AnalyticCoordinateAxis          6 598      2  Init  Gb  InsTestingEntry.o
  f_mkfs                          4 942      2  Init  Gb  ff.c.o
  frame_pack_and_send             4 466      2  Init  Gb  frame_analysis.o
  output_gdw_do                   3 824      2  Init  Gb  INS_Output.o
  ComputeFn                       2 166      2  Init  Gb  kalman.o
  AlgorithmAct                    2 056      2  Init  Gb  InsTestingEntry.o
  mount_volume                    2 016      2  Init  Lc  ff.c.o
  FPGATo422_00BB_send             1 612      2  Init  Gb  InsTestingEntry.o
  ReadFileToSd                    1 540      2  Init  Gb  sd_fatfs.o
  ErrStore_1s                     1 516      2  Init  Gb  kalman.o
  Kalman_StartUp                  1 354      2  Init  Gb  kalman.o
  ComputeZk                       1 350      2  Init  Gb  kalman.o
  sd_decode_csd                   1 332      2  Init  Lc  hpm_sdmmc_sd.c.o
  create_name                     1 308      2  Init  Lc  ff.c.o
  f_lseek                         1 278      2  Init  Gb  ff.c.o
  f_open                          1 250      2  Init  Gb  ff.c.o
  ErrCorrect_1_Navi_Time          1 222      2  Init  Gb  kalman.o
  INS912_Output                   1 194      2  Init  Gb  INS_Output.o
  f_printf                        1 152      2  Init  Gb  ff.c.o
  SetParaAll                      1 140      2  Init  Gb  SetParaBao.o
  output_fpga_void                1 128      2  Init  Gb  INS_Output.o
  dir_register                    1 056      2  Init  Lc  ff.c.o
  UartDmaRecSetPara               1 022      2  Init  Gb  SetParaBao.o
  f_async_write                     976      2  Init  Gb  ff.c.o
  f_write                           976      2  Init  Gb  ff.c.o
  Rk_Init                           974      2  Init  Gb  kalman.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                                    968      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                                    968      2  Init  Gb  InsTestingEntry.o
  analysisRxdata                    890      2  Init  Gb  uart.o
  NaviCompute                       870      2  Init  Gb  navi.o
  f_read                            868      2  Init  Gb  ff.c.o
  InitParaToAlgorithm               786      2  Init  Gb  SetParaBao.o
  sd_card_init                      784      2  Init  Gb  hpm_sdmmc_sd.c.o
  SaveGNSSData                      774      2  Init  Gb  read_and_check_gnss_data.o
  dir_find                          724      2  Init  Lc  ff.c.o
  ReadParaFromFlash                 704      2  Init  Gb  SetParaBao.o
  Mat_Inv                           700      2  Init  Gb  matvecmath.o
  sdmmchost_init_io                 696      2  Init  Gb  hpm_sdmmc_host.c.o
  fpgadata_Predo_chen_SetAlgParm_acc
                                    694      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_OutDataSet
                                    686      2  Init  Gb  InsTestingEntry.o
  create_chain                      678      2  Init  Lc  ff.c.o
  f_sync                            670      2  Init  Gb  ff.c.o
  create_partition                  668      2  Init  Lc  ff.c.o
  get_fat                           658      2  Init  Lc  ff.c.o
  CnbToQ                            640      2  Init  Gb  navi.o
  fpgadata_Predo_chen_SetAlgParm_gyro
                                    640      2  Init  Gb  InsTestingEntry.o
  DynamicInertialSysAlignCompute
                                    606      2  Init  Gb  dynamic_align.o
  RestoreFactory                    600      2  Init  Gb  SetParaBao.o
  ComputeSi                         582      2  Init  Gb  dynamic_align.o
  put_fat                           574      2  Init  Lc  ff.c.o
  sdxc_set_adma2_desc               556      2  Init  Gb  hpm_sdxc_drv.c.o
  follow_path                       552      2  Init  Lc  ff.c.o
  dma_setup_channel                 548      2  Init  Gb  hpm_dma_drv.c.o
  remove_chain                      520      2  Init  Lc  ff.c.o
  QToCnb                            518      2  Init  Gb  navi.o
  load_xdir                         516      2  Init  Lc  ff.c.o
  sd_write_blocks                   514      2  Init  Gb  hpm_sdmmc_sd.c.o
  dir_read                          506      2  Init  Lc  ff.c.o
  sd_read_blocks                    490      2  Init  Gb  hpm_sdmmc_sd.c.o
  irq_handler_trap                  488      4  Init  Gb  trap.c.o
  InertialSysAlignCompute           486      2  Init  Gb  align.o
  ComputeKk                         484      2  Init  Gb  kalman.o
  check_fs                          476      2  Init  Lc  ff.c.o
  sd_start_write_blocks             472      2  Init  Gb  hpm_sdmmc_sd.c.o
  GNSSAndHeadDataTest               468      2  Init  Gb  read_and_check_gnss_data.o
  dma_config_linked_descriptor
                                    468      2  Init  Gb  hpm_dma_drv.c.o
  CorrectAtti                       466      2  Init  Gb  kalman.o
  putc_bfd                          458      2  Init  Lc  ff.c.o
  uart_dma_tx_send                  458      2  Init  Lc  uart_dma.o
  mcusendtopcdriversdata            452      2  Init  Gb  gdwatch.o
  SetParaGnssInitValue              448      2  Init  Gb  SetParaBao.o
  sd_decode_status                  444      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdxc_parse_interrupt_status
                                    442      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeFk                         440      2  Init  Gb  kalman.o
  ComputeQ                          440      2  Init  Gb  navi.o
  sdxc_set_adma3_desc               434      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_transfer_nonblocking         428      2  Init  Gb  hpm_sdxc_drv.c.o
  f_unlink                          418      2  Init  Gb  ff.c.o
  CnbToAtti                         416      2  Init  Gb  navi.o
  ComputeVn                         410      2  Init  Gb  navi.o
  sdmmchost_power_control           408      2  Init  Lc  hpm_sdmmc_host.c.o
  SysInit                           404      2  Init  Gb  navi.o
  ParaUpdateHandle                  400      2  Init  Gb  SetParaBao.o
  SetParaUpdateStart                400      2  Init  Gb  SetParaBao.o
  ld_qword                          400      2  Init  Lc  ff.c.o
  uart_init                         396      2  Init  Gb  hpm_uart_drv.c.o
  SetParaFactorAcc                  392      2  Init  Gb  SetParaBao.o
  SetParaFactorGyro                 392      2  Init  Gb  SetParaBao.o
  uart_dma_init                     386      2  Init  Gb  uart_dma.o
  uart_dma_recv_polling             386      2  Init  Gb  uart_dma.o
  initializationdriversettings
                                    384      2  Init  Gb  gdwatch.o
  output_fpgatxt_do                 382      2  Init  Gb  INS_Output.o
  Hk_Init                           380      2  Init  Gb  kalman.o
  WriteBB00FileToSd                 380      2  Init  Gb  sd_fatfs.o
  bsp_gpio_init                     380      2  Init  Gb  bsp_gpio.o
  gen_numname                       376      2  Init  Lc  ff.c.o
  board_init_sd_host_params         370      2  Init  Wk  hpm_sdmmc_port.c.o
  ff_wtoupper                       370      2  Init  Gb  ffunicode.c.o
  ReadPara_4                        368      2  Init  Gb  SetParaBao.o
  sdmmchost_transfer                368      2  Init  Gb  hpm_sdmmc_host.c.o
  KalCompute                        364      2  Init  Gb  kalman.o
  sd_probe_bus_voltage              362      2  Init  Lc  hpm_sdmmc_sd.c.o
  uart_calculate_baudrate           354      2  Init  Lc  hpm_uart_drv.c.o
  dir_next                          352      2  Init  Lc  ff.c.o
  SetParaBaud                       350      2  Init  Gb  SetParaBao.o
  SetParaUpdateSend                 338      2  Init  Gb  SetParaBao.o
  sdxc_error_recovery               330      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_prepare_cmd_xfer             330      2  Init  Lc  hpm_sdxc_drv.c.o
  fpgadata_Predo_chen_preAlgParm_370
                                    326      2  Init  Gb  InsTestingEntry.o
  Qua_Mul                           324      2  Init  Gb  matvecmath.o
  ReadPara_2                        322      2  Init  Gb  SetParaBao.o
  gnss_check_bind                   322      2  Init  Gb  InsTestingEntry.o
  gptmr_channel_config              322      2  Init  Gb  hpm_gptmr_drv.c.o
  st_qword                          322      2  Init  Lc  ff.c.o
  SetParaAngle                      320      2  Init  Gb  SetParaBao.o
  SetParaDeviation                  320      2  Init  Gb  SetParaBao.o
  SetParaGnss                       320      2  Init  Gb  SetParaBao.o
  SetParaVector                     320      2  Init  Gb  SetParaBao.o
  sd_switch_function                320      2  Init  Lc  hpm_sdmmc_sd.c.o
  AttiToCnb                         318      2  Init  Gb  navi.o
  SaveParaToFlash                   310      2  Init  Gb  SetParaBao.o
  SetParaUpdateEnd                  310      2  Init  Gb  SetParaBao.o
  ComputeVibn                       308      2  Init  Gb  navi.o
  sd_disk_initialize                306      2  Init  Gb  hpm_sdmmc_disk.c.o
  ComputePos                        302      2  Init  Gb  navi.o
  fpgadata_syn_count_do             296      2  Init  Gb  fpgad.o
  show_error_string                 296      2  Init  Gb  sd_fatfs.o
  sync_fs                           296      2  Init  Lc  ff.c.o
  sd_polling_card_status_busy
                                    290      2  Init  Gb  hpm_sdmmc_sd.c.o
  SetParaCoord                      286      2  Init  Gb  SetParaBao.o
  create_xdir                       282      2  Init  Lc  ff.c.o
  sd_set_bus_timing                 282      2  Init  Lc  hpm_sdmmc_sd.c.o
  SetParaFrequency                  280      2  Init  Gb  SetParaBao.o
  dir_sdi                           280      2  Init  Lc  ff.c.o
  get_fpgadata_do                   280      2  Init  Gb  fpgad.o
  tchar2uni                         280      2  Init  Lc  ff.c.o
  SetParaCalibration                278      2  Init  Gb  SetParaBao.o
  Fatfs_Init                        276      2  Init  Gb  sd_fatfs.o
  ReadPara_1                        276      2  Init  Gb  SetParaBao.o
  SetParaSdHandle                   276      2  Init  Gb  SetParaBao.o
  ComputeAttiRate                   270      2  Init  Gb  navi.o
  ReadPara                          268      2  Init  Gb  SetParaBao.o
  get_boot_reason                   268      2  Init  Gb  main.o
  DynamicNavi_Init                  266      2  Init  Gb  navi.o
  ReadPara_0                        266      2  Init  Gb  SetParaBao.o
  ReadPara_3                        266      2  Init  Gb  SetParaBao.o
  cmp_lfn                           264      2  Init  Lc  ff.c.o
  main                              264      2  Init  Gb  main.o
  SetParaDataOutType                258      2  Init  Gb  SetParaBao.o
  TIMER2_IRQHandler                 258      2  Init  Gb  gd32f4xx_it.o
  SetParaTime                       256      2  Init  Gb  SetParaBao.o
  caninfupdate                      254      2  Init  Gb  INS_Data.o
  SetParaFilter                     250      2  Init  Gb  SetParaBao.o
  SetParaKalmanQ                    250      2  Init  Gb  SetParaBao.o
  SetParaKalmanR                    250      2  Init  Gb  SetParaBao.o
  sd_read_status                    250      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdmmchost_start_transfer          250      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_vsel_pin_control
                                    250      2  Init  Gb  hpm_sdmmc_host.c.o
  default_isr_111                   248      4  Init  Gb  hpm_sdmmc_disk.c.o
  default_isr_63                    248      4  Init  Gb  timer.o
  put_lfn                           248      2  Init  Lc  ff.c.o
  Pk_Init                           246      2  Init  Gb  kalman.o
  Qk_Init                           246      2  Init  Gb  kalman.o
  default_isr_2                     246      4  Init  Gb  main.o
  SetParaDebugMode                  240      2  Init  Gb  SetParaBao.o
  SetParaGpsType                    240      2  Init  Gb  SetParaBao.o
  SetParaGyroType                   240      2  Init  Gb  SetParaBao.o
  pick_lfn                          240      2  Init  Lc  ff.c.o
  sdmmc_card_async_write            238      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmchost_init                    238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_irq_handler             238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_error_recovery_first_half
                                    238      2  Init  Lc  hpm_sdxc_drv.c.o
  FinishDynamicInertialSysAlign
                                    236      2  Init  Gb  dynamic_align.o
  FinishInertialSysAlign            236      2  Init  Gb  align.o
  find_bitmap                       236      2  Init  Lc  ff.c.o
  timer_config                      236      2  Init  Lc  timer.o
  sdmmchost_switch_to_1v8           234      2  Init  Gb  hpm_sdmmc_host.c.o
  ComputeCie                        232      2  Init  Gb  align.o
  SetParaUpdateStop                 232      2  Init  Gb  SetParaBao.o
  hpm_sdmmc_osal_event_wait         232      2  Init  Wk  hpm_sdmmc_osal.o
  GNSS_Valid_PPSStart               230      2  Init  Gb  InsTestingEntry.o
  sd_send_scr                       230      2  Init  Lc  hpm_sdmmc_sd.c.o
  Virtual_PPS_insert_5hz            228      2  Init  Gb  InsTestingEntry.o
  store_xdir                        228      2  Init  Lc  ff.c.o
  change_bitmap                     224      2  Init  Lc  ff.c.o
  dir_alloc                         222      2  Init  Lc  ff.c.o
  sdmmc_card_read                   220      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_card_write                  220      2  Init  Lc  hpm_sdmmc_disk.c.o
  Kalman_Init                       218      2  Init  Gb  kalman.o
  Navi_Init                         218      2  Init  Gb  navi.o
  ComputeCib0i                      216      2  Init  Gb  align.o
  ComputePk                         216      2  Init  Gb  kalman.o
  WriteFileOpenFromSd               216      2  Init  Gb  sd_fatfs.o
  sdxc_perform_tuning_flow_sequence
                                    216      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_receive_cmd_response         216      2  Init  Gb  hpm_sdxc_drv.c.o
  dir_remove                        214      2  Init  Lc  ff.c.o
  extract_csd_field                 214      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmchost_send_command            210      2  Init  Gb  hpm_sdmmc_host.c.o
  uart_rx_dma_autorun               210      2  Init  Lc  uart_dma.o
  ComputeDelSenbb                   208      2  Init  Gb  navi.o
  ff_queue_push                     206      2  Init  Gb  ff_queue.o
  find_volume                       206      2  Init  Lc  ff.c.o
  sdxc_init                         206      2  Init  Gb  hpm_sdxc_drv.c.o
  test_gpio_input_interrupt         206      2  Init  Gb  main.o
  ComputeXk                         204      2  Init  Gb  kalman.o
  INS912AlgorithmEntry              204      2  Init  Gb  InsTestingEntry.o
  LEDIndicator                      204      2  Init  Gb  INS_Init.o
  ff_uni2oem                        198      2  Init  Gb  ffunicode.c.o
  ComputeG                          196      2  Init  Gb  navi.o
  SdFileOperateTypeSet              196      2  Init  Gb  sd_fatfs.o
  sd_init                           196      2  Init  Gb  hpm_sdmmc_sd.c.o
  ComputeWnbb                       192      2  Init  Gb  navi.o
  ComputeCen                        188      2  Init  Gb  align.o
  ComputeVi                         186      2  Init  Gb  align.o
  ComputeSib0                       178      2  Init  Gb  dynamic_align.o
  pnavout_set                       178      2  Init  Gb  InsTestingEntry.o
  DeleteFileFromSd                  172      2  Init  Gb  sd_fatfs.o
  WriteFileToSd                     172      2  Init  Gb  sd_fatfs.o
  get_ldnumber                      170      2  Init  Lc  ff.c.o
  WriteCOM3FileToSd                 166      2  Init  Gb  sd_fatfs.o
  sdxc_set_data_timeout             166      2  Init  Gb  hpm_sdxc_drv.c.o
  CloseFileToSd                     164      2  Init  Gb  sd_fatfs.o
  dir_clear                         164      2  Init  Lc  ff.c.o
  sd_transfer                       164      2  Init  Lc  hpm_sdmmc_sd.c.o
  ComputePkk_1_Step2                162      2  Init  Gb  kalman.o
  Mat_Mul                           162      2  Init  Gb  matvecmath.o
  sd_convert_data_endian            162      2  Init  Lc  hpm_sdmmc_sd.c.o
  DynamicInertialSysAlign_Init
                                    160      2  Init  Gb  dynamic_align.o
  sd_disk_ioctl                     160      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmchost_is_card_detected
                                    160      2  Init  Gb  hpm_sdmmc_host.c.o
  xname_sum                         160      2  Init  Lc  ff.c.o
  UartIrqSendMsg                    158      2  Init  Gb  uart.o
  ComputeRmRn                       156      2  Init  Gb  navi.o
  InertialSysAlign_Init             156      2  Init  Gb  align.o
  SysVarDefaultSet                  156      2  Init  Gb  navi.o
  sdxc_set_adma_table_config
                                    156      2  Init  Gb  hpm_sdxc_drv.c.o
  get_fpgadata_after_otherDataDo
                                    154      2  Init  Gb  fpgad.o
  sd_decode_scr                     154      2  Init  Lc  hpm_sdmmc_sd.c.o
  ff_queue_get_buffer_by_sector
                                    152      2  Init  Gb  ff_queue.o
  f_mount                           144      2  Init  Gb  ff.c.o
  ff_queue_poll                     144      2  Init  Gb  ff_queue.o
  sd_set_bus_width                  144      2  Init  Lc  hpm_sdmmc_sd.c.o
  sync_window                       142      2  Init  Lc  ff.c.o
  ReadPara0_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara1_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara2_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara3_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara4_SetHead                 140      2  Init  Gb  SetParaBao.o
  SendPara_SetHead                  140      2  Init  Gb  SetParaBao.o
  UpdateEnd_SetHead                 140      2  Init  Gb  SetParaBao.o
  UpdateSend_SetHead                140      2  Init  Gb  SetParaBao.o
  UpdateStart_SetHead               140      2  Init  Gb  SetParaBao.o
  UpdateStop_SetHead                140      2  Init  Gb  SetParaBao.o
  set_pwm_waveform_edge_aligned_frequency
                                    140      2  Init  Lc  timer.o
  sdxc_transfer_cb                  138      2  Init  Lc  ff_queue.o
  INS_Init                          136      2  Init  Gb  INS_Init.o
  UartIrqInit                       136      2  Init  Gb  uart.o
  WriteBB00FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  WriteCom3FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  sduart_recv_polling               132      2  Init  Gb  uart.o
  sdxc_set_transfer_config          132      2  Init  Lc  hpm_sdxc_drv.c.o
  SdFileReadOperate                 130      2  Init  Gb  sd_fatfs.o
  Vec_Cross                         130      2  Init  Gb  matvecmath.o
  load_obj_xdir                     130      2  Init  Lc  ff.c.o
  ReadBB00FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  ReadCom3FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  sd_mount_fs                       128      2  Init  Lc  sd_fatfs.o
  sd_send_csd                       128      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_enable_auto_tuning          128      2  Init  Gb  hpm_sdmmc_common.c.o
  sd_mkfs                           126      2  Init  Lc  sd_fatfs.o
  sdxc_set_dma_config               126      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeDeg_Ex                     124      2  Init  Gb  navi.o
  KalPredict                        122      2  Init  Gb  kalman.o
  SaveINSData                       122      2  Init  Gb  kalman.o
  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_osal.o
  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmchost_set_cardclk_delay_chain
                                    122      2  Init  Gb  hpm_sdmmc_host.c.o
  test_uart_recv_polling            122      2  Init  Lc  uart.o
  SendVersionInfo                   118      2  Init  Gb  datado.o
  sd_all_send_cid                   118      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdxc_perform_auto_tuning          118      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeXkk_1                      116      2  Init  Gb  kalman.o
  sd_error_recovery                 116      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_select_card                 116      2  Init  Gb  hpm_sdmmc_common.c.o
  fill_last_frag                    114      2  Init  Lc  ff.c.o
  gptmr_channel_get_default_config
                                    114      2  Init  Gb  hpm_gptmr_drv.c.o
  ReadPara0_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara1_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara2_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara3_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara4_SetEnd                  112      2  Init  Gb  SetParaBao.o
  SendPara_SetEnd                   112      2  Init  Gb  SetParaBao.o
  UpdateEnd_SetEnd                  112      2  Init  Gb  SetParaBao.o
  UpdateSend_SetEnd                 112      2  Init  Gb  SetParaBao.o
  UpdateStart_SetEnd                112      2  Init  Gb  SetParaBao.o
  UpdateStop_SetEnd                 112      2  Init  Gb  SetParaBao.o
  sd_send_if_cond                   112      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdxc_reset                        112      2  Init  Gb  hpm_sdxc_drv.c.o
  validate                          110      2  Init  Lc  ff.c.o
  xdir_sum                          110      2  Init  Lc  ff.c.o
  ComputeVib0                       108      2  Init  Gb  align.o
  disk_ioctl                        108      2  Init  Gb  diskio.c.o
  f_close                           108      2  Init  Gb  ff.c.o
  sd_app_cmd_send_cond_op           108      2  Init  Lc  hpm_sdmmc_sd.c.o
  SDUartIrqInit                     106      2  Init  Gb  uart.o
  sd_app_cmd_set_write_block_erase_count
                                    106      2  Init  Lc  hpm_sdmmc_sd.c.o
  fill_first_frag                   104      2  Init  Lc  ff.c.o
  move_window                       104      2  Init  Lc  ff.c.o
  BindDefaultSet_by_GNSS            102      2  Init  Gb  navi.o
  Read_And_Check_GNSS_Data          102      2  Init  Gb  read_and_check_gnss_data.o
  disk_async_write                  102      2  Init  Gb  diskio.c.o
  disk_read                         102      2  Init  Gb  diskio.c.o
  disk_sync_read                    102      2  Init  Gb  diskio.c.o
  disk_write                        102      2  Init  Gb  diskio.c.o
  myget_16bit_D64                   102      2  Init  Gb  readpaoche.o
  sd_send_card_status               102      2  Init  Lc  hpm_sdmmc_sd.c.o
  st_dword                          102      2  Init  Lc  ff.c.o
  ComputePkk_1_Step1                100      2  Init  Gb  kalman.o
  init_alloc_info                    98      2  Init  Lc  ff.c.o
  sd_be2le                           98      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_disk_status                     98      2  Init  Gb  hpm_sdmmc_disk.c.o
  core_local_mem_to_sys_address
                                     96      2  Init  Lc  uart_dma.o
  core_local_mem_to_sys_address
                                     96      2  Init  Lc  hpm_sdmmc_disk.c.o
  core_local_mem_to_sys_address
                                     96      2  Init  Lc  hpm_sdmmc_port.c.o
  sd_send_rca                        96      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_send_application_command
                                     96      2  Init  Gb  hpm_sdmmc_common.c.o
  sdxc_select_voltage                96      2  Init  Gb  hpm_sdxc_drv.c.o
  fpgadata_Predo_chen_algParmCash
                                     92      2  Init  Gb  InsTestingEntry.o
  sdmmchost_enable_sdio_interrupt
                                     92      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_set_data_bus_width            92      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeWenn                        90      2  Init  Gb  navi.o
  sd_check_card_parameters           90      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_disk_sync_read                  90      2  Init  Gb  hpm_sdmmc_disk.c.o
  ComputeWien                        88      2  Init  Gb  navi.o
  ff_queue_init                      88      2  Init  Gb  ff_queue.o
  UpdateAlignPosAndVn                86      2  Init  Gb  dynamic_align.o
  sd_host_init                       86      2  Init  Gb  hpm_sdmmc_sd.c.o
  uart_default_config                86      2  Init  Gb  hpm_uart_drv.c.o
  AlgorithmDo                        84      2  Init  Gb  InsTestingEntry.o
  ReadFileOpenFromSd                 84      2  Init  Gb  sd_fatfs.o
  gpiom_set_pin_controller           84      2  Init  Lc  bsp_gpio.o
  gpiom_set_pin_controller           84      2  Init  Lc  main.o
  sdmmc_set_block_size               84      2  Init  Gb  hpm_sdmmc_common.c.o
  sdxc_set_speed_mode                84      2  Init  Gb  hpm_sdxc_drv.c.o
  set_pwm_waveform_edge_aligned_duty
                                     84      2  Init  Lc  timer.o
  Mat_Tr                             82      2  Init  Gb  matvecmath.o
  gpio_write_pin                     82      2  Init  Lc  INS_Init.o
  sd_send_cmd                        82      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_go_idle_state                82      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmchost_get_data_pin_level
                                     80      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_wait_command_done
                                     80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_idle                80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_xfer_done           80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdmmc_host.c.o
  GNSS_Lost_Time                     78      2  Init  Gb  InsTestingEntry.o
  norflash_init                      78      2  Init  Gb  flash.o
  st_clust                           78      2  Init  Lc  ff.c.o
  uart_send_byte                     78      2  Init  Gb  hpm_uart_drv.c.o
  sd_switch_voltage                  76      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmchost_select_voltage           76      2  Init  Gb  hpm_sdmmc_host.c.o
  sum_sfn                            76      2  Init  Lc  ff.c.o
  ComputeLeverArmVn                  74      2  Init  Gb  navi.o
  fpgadata_Predo                     74      2  Init  Gb  InsTestingEntry.o
  gptmr_channel_reset_count          74      2  Init  Lc  timer.o
  putc_flush                         74      2  Init  Lc  ff.c.o
  uart_tx_dma                        74      2  Init  Lc  uart_dma.o
  xor_check                          74      2  Init  Gb  frame_analysis.o
  fpgadata_Predo_chen                72      2  Init  Gb  InsTestingEntry.o
  ld_clust                           72      2  Init  Lc  ff.c.o
  disk_initialize                    70      2  Init  Gb  diskio.c.o
  disk_status                        70      2  Init  Gb  diskio.c.o
  GNSS_Last_TIME                     68      2  Init  Gb  InsTestingEntry.o
  TransHeading0to360                 68      2  Init  Gb  navi.o
  comm_param_setbits                 68      2  Init  Gb  computerFrameParse.o
  dbc_1st                            68      2  Init  Lc  ff.c.o
  ld_dword                           68      2  Init  Lc  ff.c.o
  sd_disk_async_write                68      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_select_card                     68      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_set_cardclk_delay_chain
                                     68      2  Init  Lc  hpm_sdmmc_host.c.o
  gptmr_update_cmp                   66      2  Init  Lc  timer.o
  rom_xpi_nor_program                66      2  Init  Lc  flash.o
  sdxc_tuning_error_recovery
                                     66      2  Init  Lc  hpm_sdxc_drv.c.o
  st_word                            66      2  Init  Lc  ff.c.o
  CorrectVn                          64      2  Init  Gb  kalman.o
  ff_queue_is_full                   64      2  Init  Gb  ff_queue.o
  uart_flush                         64      2  Init  Gb  hpm_uart_drv.c.o
  Drv_FlashWrite                     62      2  Init  Gb  FirmwareUpdateFile.o
  sdmmchost_check_host_availability
                                     62      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_set_card_bus_width
                                     62      2  Init  Gb  hpm_sdmmc_host.c.o
  DeviceInit                         60      2  Init  Gb  datado.o
  dma_default_channel_config
                                     60      2  Init  Gb  hpm_dma_drv.c.o
  dmamux_config                      60      2  Init  Lc  uart_dma.o
  rom_xpi_nor_read                   60      2  Init  Lc  flash.o
  sd_disk_read                       60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_write                      60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmc_get_card_and_aligned_buf_info
                                     60      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdxc_wait_card_active              60      2  Init  Lc  hpm_sdmmc_host.c.o
  uart_modem_config                  60      2  Init  Lc  hpm_uart_drv.c.o
  ld_word                            58      2  Init  Lc  ff.c.o
  norflash_read                      58      2  Init  Gb  flash.o
  norflash_write                     58      2  Init  Gb  flash.o
  rom_xpi_nor_erase_block            58      2  Init  Lc  flash.o
  rom_xpi_nor_erase_sector           58      2  Init  Lc  flash.o
  sdxc_stop_clock_during_phase_code_change
                                     58      2  Init  Lc  hpm_sdmmc_common.c.o
  ComputeLeverArmSn                  56      2  Init  Gb  navi.o
  app_accum_verify_8bit              56      2  Init  Gb  app_tool.o
  crc_verify_8bit                    56      2  Init  Gb  app_tool.o
  myget_16bit_D32                    56      2  Init  Gb  readpaoche.o
  sdmmchost_set_speed_mode           56      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_enable_auto_tuning            56      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_send_command                  56      2  Init  Gb  hpm_sdxc_drv.c.o
  uart_dma_output                    56      2  Init  Gb  uart_dma.o
  Drv_FlashErase                     54      2  Init  Gb  FirmwareUpdateFile.o
  clst2sect                          54      2  Init  Lc  ff.c.o
  f_chdrive                          54      2  Init  Gb  ff.c.o
  myget_16bit_I32                    54      2  Init  Gb  readpaoche.o
  rom_xpi_nor_erase_chip             54      2  Init  Lc  flash.o
  sdxc_enable_interrupt_signal
                                     54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_interrupt_status
                                     54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_interrupt_status
                                     54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_is_inverse_clock_enabled
                                     54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_is_inverse_clock_enabled
                                     54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_set_post_change_delay
                                     54      2  Init  Lc  hpm_sdmmc_common.c.o
  sdmmchost_set_card_clock           52      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_get_data_bus_width            52      2  Init  Gb  hpm_sdxc_drv.c.o
  tick_ms_isr                        52      4  Init  Gb  timer.o
  Uart_SendMsg                       50      2  Init  Gb  bsp_fmc.o
  norflash_erase_sector              50      2  Init  Gb  flash.o
  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_tm_clock               48      2  Init  Lc  hpm_sdxc_drv.c.o
  Pwm_Init                           46      2  Init  Gb  timer.o
  rom_xpi_nor_auto_config            46      2  Init  Lc  flash.o
  Exti_Init                          44      2  Init  Gb  main.o
  SdFileWriteOperate                 44      2  Init  Gb  sd_fatfs.o
  get_fpgadata                       44      2  Init  Gb  fpgad.o
  gpio_clear_pin_interrupt_flag
                                     44      2  Init  Lc  main.o
  gpio_enable_pin_interrupt          44      2  Init  Lc  main.o
  gpio_set_pin_input                 44      2  Init  Lc  main.o
  gpio_set_pin_output                44      2  Init  Lc  bsp_gpio.o
  gptmr_start_counter                44      2  Init  Lc  timer.o
  gptmr_stop_counter                 44      2  Init  Lc  timer.o
  ff_queue_is_empty                  42      2  Init  Gb  ff_queue.o
  gpio_toggle_pin                    42      2  Init  Lc  main.o
  sd_set_max_current                 42      2  Init  Gb  hpm_sdmmc_sd.c.o
  hpm_sdmmc_osal_event_create
                                     40      2  Init  Wk  hpm_sdmmc_osal.o
  sdmmchost_wait_card_active
                                     40      2  Init  Gb  hpm_sdmmc_host.c.o
  uart_get_current_recv_remaining_size
                                     40      2  Init  Lc  uart_dma.o
  ff_handle_poll                     38      2  Init  Gb  ff_queue.o
  hpm_sdmmc_osal_delay               38      2  Init  Wk  hpm_sdmmc_osal.o
  myget_8bit_I16                     38      2  Init  Gb  readpaoche.o
  putc_init                          38      2  Init  Lc  ff.c.o
  gptmr_check_status                 36      2  Init  Lc  timer.o
  isr_gpio                           36      4  Init  Gb  main.o
  sdmmc_get_sys_addr                 36      2  Init  Gb  hpm_sdmmc_port.c.o
  sdxc_get_default_cardclk_delay_chain
                                     36      2  Init  Lc  hpm_sdmmc_host.c.o
  xsum32                             36      2  Init  Lc  ff.c.o
  EXTI3_IRQHandler                   34      2  Init  Gb  gd32f4xx_it.o
  init_gpio                          34      2  Init  Gb  main.o
  output_normal_do                   34      2  Init  Gb  INS_Output.o
  sdmmchost_error_recovery           34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_register_xfer_complete_callback
                                     34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_switch_to_3v3_as_needed
                                     34      2  Init  Lc  hpm_sdmmc_host.c.o
  Drv_FlashRead                      32      2  Init  Gb  FirmwareUpdateFile.o
  timer_Init                         32      2  Init  Gb  timer.o
  hpm_sdmmc_osal_event_clear
                                     30      2  Init  Wk  hpm_sdmmc_osal.o
  loopDoOther                        30      2  Init  Gb  datado.o
  sd_is_card_present                 30      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdxc_is_bus_idle                   30      2  Init  Gb  hpm_sdxc_drv.c.o
  Led_Control                        28      2  Init  Gb  main.o
  bsp_tim_init                       28      2  Init  Gb  bsp_tim.o
  gd_eval_com_init                   28      2  Init  Gb  INS_Init.o
  gptmr_enable_irq                   28      2  Init  Lc  timer.o
  sdcard_isr                         28      4  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmchost_delay_ms                 28      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_voltage_switch_supported
                                     28      2  Init  Gb  hpm_sdmmc_host.c.o
  uart4sendmsg                       28      2  Init  Gb  gd32f4xx_it.o
  Drv_SystemReset                    26      2  Init  Gb  FirmwareUpdateFile.o
  hpm_sdmmc_osal_event_set           26      2  Init  Wk  hpm_sdmmc_osal.o
  sdxc_is_card_inserted              26      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_reset_tuning_engine           26      2  Init  Lc  hpm_sdxc_drv.c.o
  ACC_gyroreset_r_TAFEAG16_buf
                                     24      2  Init  Gb  InsTestingEntry.o
  Algorithm_before_otherDataDo
                                     24      2  Init  Gb  datado.o
  delay_ms                           24      2  Init  Gb  systick.o
  dma_get_remaining_transfer_size
                                     24      2  Init  Lc  uart_dma.o
  protocol_send                      24      2  Init  Gb  computerFrameParse.o
  sdmmchost_is_8bit_supported
                                     24      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_execute_tuning                24      2  Init  Lc  hpm_sdxc_drv.c.o
  get_uart_tx_idle                   22      2  Init  Gb  uart_dma.o
  gptmr_clear_status                 20      2  Init  Lc  timer.o
  mchtmr_get_count                   18      2  Init  Lc  sd_fatfs.o
  ppor_sw_reset                      18      2  Init  Lc  FirmwareUpdateFile.o
  sdxc_clear_interrupt_status
                                     18      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_clear_interrupt_status
                                     18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data3_0_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data7_4_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  comm_axis_read                     16      2  Init  Gb  computerFrameParse.o
  comm_read_currentFreq              16      2  Init  Gb  computerFrameParse.o
  sdxc_select_cardclk_delay_source
                                     16      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_get_interrupt_signal          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_present_status            14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_is_ddr50_supported            12      2  Init  Lc  hpm_sdmmc_port.c.o
  sd_deinit                          10      2  Init  Gb  hpm_sdmmc_sd.c.o
  comm_nav_para_syn                   4      2  Init  Gb  computerFrameParse.o
  get_fpgadata_before                 4      2  Init  Gb  fpgad.o

Function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_SIGNAL_SIG_DFL
                             0x80010092           2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                             0x80027886           2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                             0x800105F6           2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_assert      0x8002CE4C         112      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_errno_addr  0x8002A102          14      2  Code  Wk  errno.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                             0x8002C35E          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat   0x8002C352          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_write  0x8002C2D2         140      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_heap_lock   0x8002DA1E          24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_heap_unlock
                             0x8002DA32          24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  __SEGGER_RTL_alloc         0x8002D9CA          88      2  Code  Gb  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_isctype
                             0x8002DA46          44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                             0x8002DA82          44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_mbtowc  0x8002BD44          42      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_tolower
                             0x8002DA72          16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_toupper
                             0x8002BD28          14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towlower
                             0x8002DAAE          16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towupper
                             0x8002BD36          14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_wctomb  0x8002DABE          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_current_locale
                             0x8002DAD2          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_PolyEvalP
                             0x8002CF9E          56      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_PolyEvalQ
                             0x8002A246          54      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_asinacos_fpu
                             0x8002A486         302      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_cos_inline
                             0x8002D070         172      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_frexp_inline
                             0x8002A27C          62      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isinf
                             0x8002A2C4          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnan
                             0x8002A2BA          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnormal
                             0x8002A2CE          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_signbit
                             0x8002D134          12      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_sin_inline
                             0x8002CFD6         160      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_heap     0x8002BCF8          22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_prin     0x8002AFB0          34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_prin_l   0x8002AF8E          38      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_isctype       0x8002DAE2          36      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                             0x8002CF22         124      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_pow10         0x8002D808          60      2  Code  Gb  utilops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_pre_padding   0x8002AF74          30      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_prin_flush    0x8002AF52          34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_print_padding
                             0x8002D8EC          48      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_putc          0x8002D850         160      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_puts_no_nl    0x8002CD92          44      2  Code  Lc  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_stream_write  0x8002D844          16      2  Code  Lc  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_vfprintf      0x8002B016       3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_vfprintf_float_long
                             0x8002B016       3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xltoa         0x8002A07C         106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xtoa          0x8002A07C         106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_init_copy         0x80040ADA          28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_done         0x8001006C                  2  Code  Gb  startup.s.o
  __SEGGER_init_heap         0x8002BCE4          26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_lzss         0x80040A66          96      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_pack         0x80040A1C          74      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_zero         0x80040AC6          20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __ashldi3                  0x8002A5A8          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  __fixunsdfdi               0x8002CECE          84      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __floatundidf              0x8002A230          22      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __floatundisf              0x8002A17A         182      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __lshrdi3                  0x8002A5CE          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  __trunctfdf2               0x8002D116          36      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __udivdi3                  0x8002A5F4       1 074      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  __umoddi3                  0x8002AA14       1 102      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  _clean_up                  0x8002C70A         172      2  Code  Wk  reset.c.o
  _init                      0x8002C7D2           4      2  Code  Wk  reset.c.o
  _init_ext_ram              0x80028A1E         170      2  Code  Gb  board.c.o
  _start                     0x80010000         190      2  Code  Gb  startup.s.o
  abort                      0x8002CE42          16      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  abs                        0x8002AE50          10      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  asin                       0x8002A5A4          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  atan                       0x8002D320         272      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  board_delay_ms             0x8002BE86          24      2  Code  Gb  board.c.o
  board_init                 0x8002BE52          60      2  Code  Gb  board.c.o
  board_init_clock           0x80028542       1 586      2  Code  Gb  board.c.o
  board_init_console         0x80027DB6         118      2  Code  Gb  board.c.o
  board_init_femc_clock      0x8002821A          50      2  Code  Gb  board.c.o
  board_init_pmp             0x80028252         770      2  Code  Gb  board.c.o
  board_init_sram_pins       0x8002BE76          20      2  Code  Gb  board.c.o
  board_init_uart            0x8002810E          34      2  Code  Gb  board.c.o
  board_init_uart_clock      0x8002BE9A         492      2  Code  Gb  board.c.o
  board_print_banner         0x8002812A         110      2  Code  Gb  board.c.o
  board_print_clock_freq     0x80027E26         654      2  Code  Gb  board.c.o
  board_sd_configure_clock   0x80028D70         302      2  Code  Gb  board.c.o
  board_turnoff_rgb_led      0x800281BA          66      2  Code  Lc  board.c.o
  clock_add_to_group         0x80029E28          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_connect_group_to_cpu
                             0x8002CA3A          44      2  Code  Gb  hpm_clock_drv.c.o
  clock_cpu_delay_ms         0x80029E62         162      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_core_clock_ticks_per_ms
                             0x8002CA66          56      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency        0x80029C16         262      2  Code  Gb  hpm_clock_drv.c.o
  clock_set_source_divider   0x80029D4C         226      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock    0x80029EF4          62      2  Code  Gb  hpm_clock_drv.c.o
  console_init               0x8002C26A         108      2  Code  Gb  hpm_debug_console.c.o
  cos                        0x8002D31C           8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  enable_plic_feature        0x8002CBBA          44      2  Code  Gb  system.c.o
  exception_handler          0x8002C7DE          44      2  Code  Wk  trap.c.o
  exit                       0x80010086           2      2  Code  Gb  startup.s.o
  femc_config_sram           0x80029400         418      2  Code  Gb  hpm_femc_drv.c.o
  femc_convert_actual_size_to_memory_size
                             0x800292FE          82      2  Code  Lc  hpm_femc_drv.c.o
  femc_default_config        0x8002915A         134      2  Code  Gb  hpm_femc_drv.c.o
  femc_disable               0x80029138          34      2  Code  Lc  hpm_femc_drv.c.o
  femc_enable                0x80029122          22      2  Code  Lc  hpm_femc_drv.c.o
  femc_get_typical_sram_config
                             0x80029386         122      2  Code  Gb  hpm_femc_drv.c.o
  femc_init                  0x800291E0         302      2  Code  Gb  hpm_femc_drv.c.o
  femc_sw_reset              0x8002C36A          28      2  Code  Lc  hpm_femc_drv.c.o
  floor                      0x8002A2D8         202      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  fmod                       0x8002D1A4         380      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  fputc                      0x8002A156          42      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  frexp                      0x8002D1A0           8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  fwrite                     0x8002A110          78      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  get_frequency_for_i2s_or_adc
                             0x8002C90A         224      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ip_in_common_group
                             0x80029CDE         114      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_pwdg     0x8002CA16          36      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_source   0x8002C80A         300      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_wdg      0x8002C9E2          56      2  Code  Lc  hpm_clock_drv.c.o
  gpio_config_pin_interrupt  0x8002956C         234      2  Code  Gb  hpm_gpio_drv.c.o
  hpm_csr_get_core_cycle     0x80029B32         122      2  Code  Lc  hpm_clock_drv.c.o
  init_gptmr_pins            0x80028F98         186      2  Code  Gb  pinmux.c.o
  init_sdxc_cd_pin           0x80029042          62      2  Code  Gb  pinmux.c.o
  init_sdxc_clk_data_pins    0x80029080         162      2  Code  Gb  pinmux.c.o
  init_sdxc_cmd_pin          0x8002C1FA          96      2  Code  Gb  pinmux.c.o
  init_sdxc_pwr_pin          0x8002C25A          16      2  Code  Gb  pinmux.c.o
  init_sram_pins             0x80028E72         294      2  Code  Gb  pinmux.c.o
  init_uart_pins             0x8002C0A2         344      2  Code  Gb  pinmux.c.o
  isdigit                    0x8002BD6E          10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  isspace                    0x8002BD74          10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  itoa                       0x8002A0E6          34      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  l1c_dc_enable              0x80027852          58      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_enable_writearound  0x80027BFA          10      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_flush               0x80027B96         102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_invalidate          0x800278D2         102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_invalidate_all      0x8002BD7A          24      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_writeback           0x80027B1E         102      2  Code  Gb  hpm_l1c_drv.o
  l1c_ic_enable              0x80027896          46      2  Code  Gb  hpm_l1c_drv.o
  l1c_op                     0x800272C2          82      2  Code  Lc  hpm_l1c_drv.o
  ldexp                      0x8002D140          96      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  ldexp.localalias           0x8002D140          96      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  log                        0x8002D426         280      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  malloc                     0x8002BD0A          42      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  mchtmr_isr                 0x8002C7D6           4      2  Code  Wk  trap.c.o
  memcmp                     0x8002D648         268      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  memcpy                     0x8002AE5A         134      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  memset                     0x8002D530         104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  ns2cycle                   0x80029350          54      2  Code  Lc  hpm_femc_drv.c.o
  pcfg_dcdc_set_voltage      0x8002C386          72      2  Code  Gb  hpm_pcfg_drv.c.o
  pcfg_dcdc_switch_to_dcm_mode
                             0x80027C66         210      2  Code  Lc  board.c.o
  pllctl_get_div             0x80029BAC         106      2  Code  Lc  hpm_clock_drv.c.o
  pllctl_get_pll_freq_in_hz  0x80029704         282      2  Code  Gb  hpm_pllctl_drv.c.o
  pllctl_init_int_pll_with_freq
                             0x8002C41A         584      2  Code  Gb  hpm_pllctl_drv.c.o
  pllctl_pll_powerdown       0x8002C3CE          76      2  Code  Lc  hpm_pllctl_drv.c.o
  pllctl_pll_poweron         0x80029656         174      2  Code  Lc  hpm_pllctl_drv.c.o
  pllctl_xtal_set_rampup_time
                             0x80027C04          38      2  Code  Lc  board.c.o
  pmp_config                 0x80029A4E         242      2  Code  Gb  hpm_pmp_drv.c.o
  printf                     0x8002AFEE          46      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  putchar                    0x8002CE8E          16      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  puts                       0x8002CE9A          68      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  raise                      0x8002CDE6         108      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  read_pma_cfg               0x80029936         110      2  Code  Gb  hpm_pmp_drv.c.o
  read_pmp_cfg               0x8002981E         110      2  Code  Gb  hpm_pmp_drv.c.o
  reset_handler              0x8002C7B6          32      2  Code  Wk  reset.c.o
  sdxc_enable_inverse_clock  0x8002BDBE          80      2  Code  Lc  board.c.o
  sdxc_enable_sd_clock       0x8002BE0E          68      2  Code  Lc  board.c.o
  signal                     0x8002CDB6          52      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  sin                        0x8002D318           8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  sprintf                    0x8002D916          68      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  sqrt                       0x8002A480           6      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  start                      0x80010080                  2  Code  Gb  startup.s.o
  strcat                     0x8002D754          40      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  strchr                     0x8002AEE0         114      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strcpy                     0x8002D598          72      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strlen                     0x8002D5E0         104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strnlen                    0x8002D770         152      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  strtod                     0x8002CBE6         448      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  swi_isr                    0x8002C7DA           4      2  Code  Wk  trap.c.o
  syscall_handler            0x80029B20          18      2  Code  Wk  trap.c.o
  sysctl_clock_set_preset    0x8002BD92          44      2  Code  Lc  board.c.o
  sysctl_clock_target_is_busy
                             0x80029F52          46      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_config_clock        0x80029F80         174      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_enable_group_resource
                             0x8002CA92         304      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_resource_target_is_busy
                             0x80029F28          42      2  Code  Lc  hpm_sysctl_drv.c.o
  system_init                0x8002A022          94      2  Code  Wk  system.c.o
  tan                        0x8002A396         242      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  trgm_enable_io_output      0x8002C032          28      2  Code  Lc  pinmux.c.o
  trgm_output_config         0x8002C04E          84      2  Code  Lc  pinmux.c.o
  vfprintf                   0x8002AFC8          46      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  vfprintf_l                 0x8002D952         132      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  write_pma_addr             0x800299A4         170      2  Code  Gb  hpm_pmp_drv.c.o
  write_pma_cfg              0x8002C6AE          92      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_addr             0x8002988C         170      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_cfg              0x8002C652          92      2  Code  Gb  hpm_pmp_drv.c.o

Function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x80010000  _start                            190      2  Code  Gb  startup.s.o
  0x8001006C  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  0x80010080  start                                      2  Code  Gb  startup.s.o
  0x80010086  exit                                2      2  Code  Gb  startup.s.o
  0x80010092  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                  2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x800105F6  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                  2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x800272C2  l1c_op                             82      2  Code  Lc  hpm_l1c_drv.o
  0x80027852  l1c_dc_enable                      58      2  Code  Gb  hpm_l1c_drv.o
  0x80027886  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                  2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x80027896  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.o
  0x800278D2  l1c_dc_invalidate                 102      2  Code  Gb  hpm_l1c_drv.o
  0x80027B1E  l1c_dc_writeback                  102      2  Code  Gb  hpm_l1c_drv.o
  0x80027B96  l1c_dc_flush                      102      2  Code  Gb  hpm_l1c_drv.o
  0x80027BFA  l1c_dc_enable_writearound          10      2  Code  Gb  hpm_l1c_drv.o
  0x80027C04  pllctl_xtal_set_rampup_time
                                                 38      2  Code  Lc  board.c.o
  0x80027C66  pcfg_dcdc_switch_to_dcm_mode
                                                210      2  Code  Lc  board.c.o
  0x80027DB6  board_init_console                118      2  Code  Gb  board.c.o
  0x80027E26  board_print_clock_freq            654      2  Code  Gb  board.c.o
  0x8002810E  board_init_uart                    34      2  Code  Gb  board.c.o
  0x8002812A  board_print_banner                110      2  Code  Gb  board.c.o
  0x800281BA  board_turnoff_rgb_led              66      2  Code  Lc  board.c.o
  0x8002821A  board_init_femc_clock              50      2  Code  Gb  board.c.o
  0x80028252  board_init_pmp                    770      2  Code  Gb  board.c.o
  0x80028542  board_init_clock                1 586      2  Code  Gb  board.c.o
  0x80028A1E  _init_ext_ram                     170      2  Code  Gb  board.c.o
  0x80028D70  board_sd_configure_clock          302      2  Code  Gb  board.c.o
  0x80028E72  init_sram_pins                    294      2  Code  Gb  pinmux.c.o
  0x80028F98  init_gptmr_pins                   186      2  Code  Gb  pinmux.c.o
  0x80029042  init_sdxc_cd_pin                   62      2  Code  Gb  pinmux.c.o
  0x80029080  init_sdxc_clk_data_pins           162      2  Code  Gb  pinmux.c.o
  0x80029122  femc_enable                        22      2  Code  Lc  hpm_femc_drv.c.o
  0x80029138  femc_disable                       34      2  Code  Lc  hpm_femc_drv.c.o
  0x8002915A  femc_default_config               134      2  Code  Gb  hpm_femc_drv.c.o
  0x800291E0  femc_init                         302      2  Code  Gb  hpm_femc_drv.c.o
  0x800292FE  femc_convert_actual_size_to_memory_size
                                                 82      2  Code  Lc  hpm_femc_drv.c.o
  0x80029350  ns2cycle                           54      2  Code  Lc  hpm_femc_drv.c.o
  0x80029386  femc_get_typical_sram_config
                                                122      2  Code  Gb  hpm_femc_drv.c.o
  0x80029400  femc_config_sram                  418      2  Code  Gb  hpm_femc_drv.c.o
  0x8002956C  gpio_config_pin_interrupt         234      2  Code  Gb  hpm_gpio_drv.c.o
  0x80029656  pllctl_pll_poweron                174      2  Code  Lc  hpm_pllctl_drv.c.o
  0x80029704  pllctl_get_pll_freq_in_hz         282      2  Code  Gb  hpm_pllctl_drv.c.o
  0x8002981E  read_pmp_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  0x8002988C  write_pmp_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  0x80029936  read_pma_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  0x800299A4  write_pma_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  0x80029A4E  pmp_config                        242      2  Code  Gb  hpm_pmp_drv.c.o
  0x80029B20  syscall_handler                    18      2  Code  Wk  trap.c.o
  0x80029B32  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  0x80029BAC  pllctl_get_div                    106      2  Code  Lc  hpm_clock_drv.c.o
  0x80029C16  clock_get_frequency               262      2  Code  Gb  hpm_clock_drv.c.o
  0x80029CDE  get_frequency_for_ip_in_common_group
                                                114      2  Code  Lc  hpm_clock_drv.c.o
  0x80029D4C  clock_set_source_divider          226      2  Code  Gb  hpm_clock_drv.c.o
  0x80029E28  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  0x80029E62  clock_cpu_delay_ms                162      2  Code  Gb  hpm_clock_drv.c.o
  0x80029EF4  clock_update_core_clock            62      2  Code  Gb  hpm_clock_drv.c.o
  0x80029F28  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  hpm_sysctl_drv.c.o
  0x80029F52  sysctl_clock_target_is_busy
                                                 46      2  Code  Lc  hpm_sysctl_drv.c.o
  0x80029F80  sysctl_config_clock               174      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002A022  system_init                        94      2  Code  Wk  system.c.o
  0x8002A07C  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  0x8002A07C  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  0x8002A0E6  itoa                               34      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  0x8002A102  __SEGGER_RTL_X_errno_addr          14      2  Code  Wk  errno.o (libc_rv32gc_d_balanced.a)
  0x8002A110  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002A156  fputc                              42      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002A17A  __floatundisf                     182      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A230  __floatundidf                      22      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A246  __SEGGER_RTL_float64_PolyEvalQ
                                                 54      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A27C  __SEGGER_RTL_float64_frexp_inline
                                                 62      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A2BA  __SEGGER_RTL_float64_isnan
                                                 10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A2C4  __SEGGER_RTL_float64_isinf
                                                 10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A2CE  __SEGGER_RTL_float64_isnormal
                                                 10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A2D8  floor                             202      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A396  tan                               242      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A480  sqrt                                6      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A486  __SEGGER_RTL_float64_asinacos_fpu
                                                302      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A5A4  asin                               10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A5A8  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A5CE  __lshrdi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A5F4  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  0x8002AA14  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  0x8002AE50  abs                                10      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  0x8002AE5A  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002AEE0  strchr                            114      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002AF52  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AF74  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AF8E  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AFB0  __SEGGER_RTL_init_prin             34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AFC8  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AFEE  printf                             46      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002B016  __SEGGER_RTL_vfprintf_float_long
                                              3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  0x8002B016  __SEGGER_RTL_vfprintf           3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  0x8002BCE4  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x8002BCF8  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0x8002BD0A  malloc                             42      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x8002BD28  __SEGGER_RTL_ascii_toupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD36  __SEGGER_RTL_ascii_towupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD44  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD6E  isdigit                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD74  isspace                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD7A  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.o
  0x8002BD92  sysctl_clock_set_preset            44      2  Code  Lc  board.c.o
  0x8002BDBE  sdxc_enable_inverse_clock          80      2  Code  Lc  board.c.o
  0x8002BE0E  sdxc_enable_sd_clock               68      2  Code  Lc  board.c.o
  0x8002BE52  board_init                         60      2  Code  Gb  board.c.o
  0x8002BE76  board_init_sram_pins               20      2  Code  Gb  board.c.o
  0x8002BE86  board_delay_ms                     24      2  Code  Gb  board.c.o
  0x8002BE9A  board_init_uart_clock             492      2  Code  Gb  board.c.o
  0x8002C032  trgm_enable_io_output              28      2  Code  Lc  pinmux.c.o
  0x8002C04E  trgm_output_config                 84      2  Code  Lc  pinmux.c.o
  0x8002C0A2  init_uart_pins                    344      2  Code  Gb  pinmux.c.o
  0x8002C1FA  init_sdxc_cmd_pin                  96      2  Code  Gb  pinmux.c.o
  0x8002C25A  init_sdxc_pwr_pin                  16      2  Code  Gb  pinmux.c.o
  0x8002C26A  console_init                      108      2  Code  Gb  hpm_debug_console.c.o
  0x8002C2D2  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  0x8002C352  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  0x8002C35E  __SEGGER_RTL_X_file_bufsize
                                                 12      2  Code  Gb  hpm_debug_console.c.o
  0x8002C36A  femc_sw_reset                      28      2  Code  Lc  hpm_femc_drv.c.o
  0x8002C386  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  0x8002C3CE  pllctl_pll_powerdown               76      2  Code  Lc  hpm_pllctl_drv.c.o
  0x8002C41A  pllctl_init_int_pll_with_freq
                                                584      2  Code  Gb  hpm_pllctl_drv.c.o
  0x8002C652  write_pmp_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  0x8002C6AE  write_pma_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  0x8002C70A  _clean_up                         172      2  Code  Wk  reset.c.o
  0x8002C7B6  reset_handler                      32      2  Code  Wk  reset.c.o
  0x8002C7D2  _init                               4      2  Code  Wk  reset.c.o
  0x8002C7D6  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  0x8002C7DA  swi_isr                             4      2  Code  Wk  trap.c.o
  0x8002C7DE  exception_handler                  44      2  Code  Wk  trap.c.o
  0x8002C80A  get_frequency_for_source          300      2  Code  Gb  hpm_clock_drv.c.o
  0x8002C90A  get_frequency_for_i2s_or_adc
                                                224      2  Code  Lc  hpm_clock_drv.c.o
  0x8002C9E2  get_frequency_for_wdg              56      2  Code  Lc  hpm_clock_drv.c.o
  0x8002CA16  get_frequency_for_pwdg             36      2  Code  Lc  hpm_clock_drv.c.o
  0x8002CA3A  clock_connect_group_to_cpu
                                                 44      2  Code  Gb  hpm_clock_drv.c.o
  0x8002CA66  clock_get_core_clock_ticks_per_ms
                                                 56      2  Code  Gb  hpm_clock_drv.c.o
  0x8002CA92  sysctl_enable_group_resource
                                                304      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002CBBA  enable_plic_feature                44      2  Code  Gb  system.c.o
  0x8002CBE6  strtod                            448      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  0x8002CD92  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CDB6  signal                             52      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CDE6  raise                             108      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CE42  abort                              16      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CE4C  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CE8E  putchar                            16      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002CE9A  puts                               68      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002CECE  __fixunsdfdi                       84      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002CF22  __SEGGER_RTL_ldouble_to_double
                                                124      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002CF9E  __SEGGER_RTL_float64_PolyEvalP
                                                 56      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002CFD6  __SEGGER_RTL_float64_sin_inline
                                                160      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D070  __SEGGER_RTL_float64_cos_inline
                                                172      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D116  __trunctfdf2                       36      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D134  __SEGGER_RTL_float64_signbit
                                                 12      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D140  ldexp.localalias                   96      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D140  ldexp                              96      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D1A0  frexp                               8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D1A4  fmod                              380      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D318  sin                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D31C  cos                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D320  atan                              272      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D426  log                               280      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D530  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002D598  strcpy                             72      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002D5E0  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002D648  memcmp                            268      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  0x8002D754  strcat                             40      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  0x8002D770  strnlen                           152      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  0x8002D808  __SEGGER_RTL_pow10                 60      2  Code  Gb  utilops.o (libc_rv32gc_d_balanced.a)
  0x8002D844  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D850  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D8EC  __SEGGER_RTL_print_padding
                                                 48      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D916  sprintf                            68      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D952  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D9CA  __SEGGER_RTL_alloc                 88      2  Code  Gb  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0x8002DA1E  __SEGGER_RTL_X_heap_lock           24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x8002DA32  __SEGGER_RTL_X_heap_unlock
                                                 24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x8002DA46  __SEGGER_RTL_ascii_isctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DA72  __SEGGER_RTL_ascii_tolower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DA82  __SEGGER_RTL_ascii_iswctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DAAE  __SEGGER_RTL_ascii_towlower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DABE  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DAD2  __SEGGER_RTL_current_locale
                                                 20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DAE2  __SEGGER_RTL_isctype               36      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80040A1C  __SEGGER_init_pack                 74      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x80040A66  __SEGGER_init_lzss                 96      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x80040AC6  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x80040ADA  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)

Function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_vfprintf           3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_vfprintf_float_long
                                  3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  board_init_clock                1 586      2  Code  Gb  board.c.o
  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  board_init_pmp                    770      2  Code  Gb  board.c.o
  board_print_clock_freq            654      2  Code  Gb  board.c.o
  pllctl_init_int_pll_with_freq
                                    584      2  Code  Gb  hpm_pllctl_drv.c.o
  board_init_uart_clock             492      2  Code  Gb  board.c.o
  strtod                            448      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  femc_config_sram                  418      2  Code  Gb  hpm_femc_drv.c.o
  fmod                              380      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  init_uart_pins                    344      2  Code  Gb  pinmux.c.o
  sysctl_enable_group_resource
                                    304      2  Code  Gb  hpm_sysctl_drv.c.o
  __SEGGER_RTL_float64_asinacos_fpu
                                    302      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  board_sd_configure_clock          302      2  Code  Gb  board.c.o
  femc_init                         302      2  Code  Gb  hpm_femc_drv.c.o
  get_frequency_for_source          300      2  Code  Gb  hpm_clock_drv.c.o
  init_sram_pins                    294      2  Code  Gb  pinmux.c.o
  pllctl_get_pll_freq_in_hz         282      2  Code  Gb  hpm_pllctl_drv.c.o
  log                               280      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  atan                              272      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  memcmp                            268      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  clock_get_frequency               262      2  Code  Gb  hpm_clock_drv.c.o
  pmp_config                        242      2  Code  Gb  hpm_pmp_drv.c.o
  tan                               242      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  gpio_config_pin_interrupt         234      2  Code  Gb  hpm_gpio_drv.c.o
  clock_set_source_divider          226      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_i2s_or_adc
                                    224      2  Code  Lc  hpm_clock_drv.c.o
  pcfg_dcdc_switch_to_dcm_mode
                                    210      2  Code  Lc  board.c.o
  floor                             202      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  _start                            190      2  Code  Gb  startup.s.o
  init_gptmr_pins                   186      2  Code  Gb  pinmux.c.o
  __floatundisf                     182      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  pllctl_pll_poweron                174      2  Code  Lc  hpm_pllctl_drv.c.o
  sysctl_config_clock               174      2  Code  Gb  hpm_sysctl_drv.c.o
  __SEGGER_RTL_float64_cos_inline
                                    172      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  _clean_up                         172      2  Code  Wk  reset.c.o
  _init_ext_ram                     170      2  Code  Gb  board.c.o
  write_pma_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  clock_cpu_delay_ms                162      2  Code  Gb  hpm_clock_drv.c.o
  init_sdxc_clk_data_pins           162      2  Code  Gb  pinmux.c.o
  __SEGGER_RTL_float64_sin_inline
                                    160      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  strnlen                           152      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  femc_default_config               134      2  Code  Gb  hpm_femc_drv.c.o
  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                                    124      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  femc_get_typical_sram_config
                                    122      2  Code  Gb  hpm_femc_drv.c.o
  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  board_init_console                118      2  Code  Gb  board.c.o
  get_frequency_for_ip_in_common_group
                                    114      2  Code  Lc  hpm_clock_drv.c.o
  strchr                            114      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  board_print_banner                110      2  Code  Gb  board.c.o
  read_pma_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  read_pmp_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  console_init                      108      2  Code  Gb  hpm_debug_console.c.o
  raise                             108      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  pllctl_get_div                    106      2  Code  Lc  hpm_clock_drv.c.o
  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  l1c_dc_flush                      102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_invalidate                 102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_writeback                  102      2  Code  Gb  hpm_l1c_drv.o
  __SEGGER_init_lzss                 96      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  init_sdxc_cmd_pin                  96      2  Code  Gb  pinmux.c.o
  ldexp                              96      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  ldexp.localalias                   96      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  system_init                        94      2  Code  Wk  system.c.o
  write_pma_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  __SEGGER_RTL_alloc                 88      2  Code  Gb  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __fixunsdfdi                       84      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  trgm_output_config                 84      2  Code  Lc  pinmux.c.o
  femc_convert_actual_size_to_memory_size
                                     82      2  Code  Lc  hpm_femc_drv.c.o
  l1c_op                             82      2  Code  Lc  hpm_l1c_drv.o
  sdxc_enable_inverse_clock          80      2  Code  Lc  board.c.o
  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  pllctl_pll_powerdown               76      2  Code  Lc  hpm_pllctl_drv.c.o
  __SEGGER_init_pack                 74      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  strcpy                             72      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  puts                               68      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  sdxc_enable_sd_clock               68      2  Code  Lc  board.c.o
  sprintf                            68      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  board_turnoff_rgb_led              66      2  Code  Lc  board.c.o
  __SEGGER_RTL_float64_frexp_inline
                                     62      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock            62      2  Code  Gb  hpm_clock_drv.c.o
  init_sdxc_cd_pin                   62      2  Code  Gb  pinmux.c.o
  __SEGGER_RTL_pow10                 60      2  Code  Gb  utilops.o (libc_rv32gc_d_balanced.a)
  board_init                         60      2  Code  Gb  board.c.o
  l1c_dc_enable                      58      2  Code  Gb  hpm_l1c_drv.o
  __SEGGER_RTL_float64_PolyEvalP
                                     56      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  clock_get_core_clock_ticks_per_ms
                                     56      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_wdg              56      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_float64_PolyEvalQ
                                     54      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  ns2cycle                           54      2  Code  Lc  hpm_femc_drv.c.o
  signal                             52      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  board_init_femc_clock              50      2  Code  Gb  board.c.o
  __SEGGER_RTL_print_padding
                                     48      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.o
  printf                             46      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  sysctl_clock_target_is_busy
                                     46      2  Code  Lc  hpm_sysctl_drv.c.o
  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_isctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32gc_d_balanced.a)
  clock_connect_group_to_cpu
                                     44      2  Code  Gb  hpm_clock_drv.c.o
  enable_plic_feature                44      2  Code  Gb  system.c.o
  exception_handler                  44      2  Code  Wk  trap.c.o
  sysctl_clock_set_preset            44      2  Code  Lc  board.c.o
  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  fputc                              42      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  malloc                             42      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  hpm_sysctl_drv.c.o
  strcat                             40      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  __lshrdi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  pllctl_xtal_set_rampup_time
                                     38      2  Code  Lc  board.c.o
  __SEGGER_RTL_isctype               36      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __trunctfdf2                       36      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  get_frequency_for_pwdg             36      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_init_prin             34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  board_init_uart                    34      2  Code  Gb  board.c.o
  femc_disable                       34      2  Code  Lc  hpm_femc_drv.c.o
  itoa                               34      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  reset_handler                      32      2  Code  Wk  reset.c.o
  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  femc_sw_reset                      28      2  Code  Lc  hpm_femc_drv.c.o
  trgm_enable_io_output              28      2  Code  Lc  pinmux.c.o
  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_heap_lock           24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_heap_unlock
                                     24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  board_delay_ms                     24      2  Code  Gb  board.c.o
  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.o
  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __floatundidf                      22      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  femc_enable                        22      2  Code  Lc  hpm_femc_drv.c.o
  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_current_locale
                                     20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  board_init_sram_pins               20      2  Code  Gb  board.c.o
  syscall_handler                    18      2  Code  Wk  trap.c.o
  __SEGGER_RTL_ascii_tolower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towlower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32gc_d_balanced.a)
  abort                              16      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  init_sdxc_pwr_pin                  16      2  Code  Gb  pinmux.c.o
  putchar                            16      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_errno_addr          14      2  Code  Wk  errno.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_toupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                                     12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_float64_signbit
                                     12      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isinf
                                     10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnan
                                     10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnormal
                                     10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  abs                                10      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  asin                               10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  isdigit                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  isspace                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  l1c_dc_enable_writearound          10      2  Code  Gb  hpm_l1c_drv.o
  cos                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  frexp                               8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  sin                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  sqrt                                6      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  _init                               4      2  Code  Wk  reset.c.o
  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  swi_isr                             4      2  Code  Wk  trap.c.o
  __SEGGER_RTL_SIGNAL_SIG_DFL
                                      2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                                      2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                                      2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  exit                                2      2  Code  Gb  startup.s.o
  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  start                                      2  Code  Gb  startup.s.o

Read-write data symbols by name:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  Acc                        0x0008C288                     24      8  Zero  Gb  InsTestingEntry.o
  Aw                         0x0008C128                     72      8  Zero  Gb  InsTestingEntry.o
  BB00FileName               0x000A7B9C                      4      4  Init  Gb  sd_fatfs.o
  BB00Flag.2                 0x0009EA62  gp+0x054E           2      2  Zero  Lc  sd_fatfs.o
  BB00SdData                 0x0009E1F4  gp-0x0320         336      4  Zero  Gb  InsTestingEntry.o
  COM3Flag.3                 0x0009E982  gp+0x046E           2      2  Zero  Lc  sd_fatfs.o
  Com3FileName               0x000A7B98                      4      4  Init  Gb  sd_fatfs.o
  CurrVol                    0x0009EB7E  gp+0x066A           1         Zero  Lc  ff.c.o
  DirBuf                     0x0009CF84                    608      4  Zero  Lc  ff.c.o
  FatFs                      0x0009EA14  gp+0x0500          40      4  Zero  Lc  ff.c.o
  Fsid                       0x0009E672  gp+0x015E           2      2  Zero  Lc  ff.c.o
  Gw                         0x0008C0E0                     72      8  Zero  Gb  InsTestingEntry.o
  LastAcc                    0x0008C270                     24      8  Zero  Gb  InsTestingEntry.o
  LfnBuf                     0x0009DD14  gp-0x0800         512      4  Zero  Lc  ff.c.o
  NaviCompute_do_count       0x0009EB6C  gp+0x0658           4      4  Zero  Gb  datado.o
  SDCnt.0                    0x0009E562  gp+0x004E           2      2  Zero  Lc  gd32f4xx_it.o
  SetSdFlieType              0x0009EB7D  gp+0x0669           1         Zero  Gb  SetParaBao.o
  SetSdOperateType           0x0009EB7C  gp+0x0668           1         Zero  Gb  SetParaBao.o
  StrMiddleWare              0x0009EA9C  gp+0x0588          24      4  Zero  Gb  InsTestingEntry.o
  __RAL_global_locale        0x00080160                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_aSigTab       0x0009EA84  gp+0x0570          24      4  Zero  Lc  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_global_locale
                             0x00080160                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_heap_globals  0x0009EB68  gp+0x0654           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_locale_ptr    0x0009EB64  gp+0x0650           4      4  Zero  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_stdout_file   0x0009EB60  gp+0x064C           4      4  Zero  Lc  hpm_debug_console.c.o
  aW_real                    0x0008C258                     24      8  Zero  Gb  InsTestingEntry.o
  aWm                        0x0008C240                     24      8  Zero  Gb  InsTestingEntry.o
  aw                         0x0008C228                     24      8  Zero  Gb  InsTestingEntry.o
  axisInfo                   0x0009EB5C  gp+0x0648           4      4  Zero  Gb  frame_analysis.o
  canin                      0x0009EAB4  gp+0x05A0          20      4  Zero  Gb  InsTestingEntry.o
  current_reload             0x0009EB58  gp+0x0644           4      4  Zero  Gb  timer.o
  dma_done                   0x000A7B88                      1      4  None  Lc  uart_dma.o
  en                         0x0009EB54  gp+0x0640           4      4  Zero  Lc  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  end_ticks                  0x0008C2A0                      8      8  Zero  Gb  sd_fatfs.o
  fatfs_result               0x0009EB7B  gp+0x0667           1         Zero  Gb  sd_fatfs.o
  flag.1                     0x0009EB50  gp+0x063C           4      4  Zero  Lc  SetParaBao.o
  fpga_loop_count            0x0009EB4C  gp+0x0638           4      4  Zero  Gb  INS_Init.o
  fpga_syn                   0x0009EB7A  gp+0x0666           1         Zero  Gb  INS_Init.o
  fpga_syn_NAVI_btw          0x0009EB48  gp+0x0634           4      4  Zero  Gb  fpgad.o
  fpga_syn_btw               0x0009EB44  gp+0x0630           4      4  Zero  Gb  fpgad.o
  fpga_syn_btw_last          0x0009EB40  gp+0x062C           4      4  Zero  Gb  fpgad.o
  fpga_syn_count             0x0009EB3C  gp+0x0628           4      4  Zero  Gb  INS_Init.o
  fpgatesttxt                0x0009C384                  2 048      4  Zero  Gb  INS_Output.o
  gW_real                    0x0008C210                     24      8  Zero  Gb  InsTestingEntry.o
  gWm                        0x0008C1F8                     24      8  Zero  Gb  InsTestingEntry.o
  g_Align                    0x0008BEC8                    184      8  Zero  Gb  main.o
  g_BB00WriteSdFlag          0x0009EB79  gp+0x0665           1         Zero  Gb  InsTestingEntry.o
  g_CAN_Timeout_Cnt          0x0009EB38  gp+0x0624           4      4  Zero  Gb  INS_Init.o
  g_CAN_Timeout_Start_flag   0x0009EB34  gp+0x0620           4      4  Zero  Gb  INS_Init.o
  g_Com3WriteSdFlag          0x0009EB78  gp+0x0664           1         Zero  Gb  uart.o
  g_Compen                   0x00087C98                 10 200      8  Zero  Gb  main.o
  g_DataOutTime              0x0009EB30  gp+0x061C           4      4  Zero  Gb  SetParaBao.o
  g_DataOutTypeFlag          0x0009EB77  gp+0x0663           1         Zero  Gb  SetParaBao.o
  g_DynamicInertialSysAlign  0x0008AF48                  1 000      8  Zero  Gb  main.o
  g_GNSSData_In_Use          0x0008C070                    112      8  Zero  Gb  main.o
  g_InertialSysAlign         0x0008B330                    824      8  Zero  Gb  main.o
  g_InitBind                 0x0008C170                     64      8  Zero  Gb  main.o
  g_Kalman                   0x00084180                 15 128      8  Zero  Gb  main.o
  g_LEDIndicatorState        0x0009EB76  gp+0x0662           1         Zero  Gb  INS_Data.o
  g_Navi                     0x0008A470                  2 776      8  Zero  Gb  main.o
  g_SelfTest                 0x0008BF80                    128      8  Zero  Gb  main.o
  g_StartUpdateFirm          0x0009EB75  gp+0x0661           1         Zero  Gb  main.o
  g_SysVar                   0x0008BD30                    408      8  Zero  Gb  main.o
  g_UpdateBackFlag           0x000A7B85                      1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful         0x0009EB74  gp+0x0660           1         Zero  Gb  SetParaBao.o
  g_console_uart             0x0009EB2C  gp+0x0618           4      4  Zero  Lc  hpm_debug_console.c.o
  g_queue                    0x000A7BA4                 33 540      4  Init  Gb  hpm_sdmmc_disk.c.o
  g_ucSystemResetFlag        0x0009EB73  gp+0x065F           1         Zero  Gb  SetParaBao.o
  galgrithomresultTx         0x0009E9E8  gp+0x04D4          44      4  Zero  Gb  gdwatch.o
  gcanInfo                   0x0009EA3C  gp+0x0528          38      4  Zero  Gb  fpgad.o
  gdriverdatalist            0x0008C2A8                 59 608      4  Zero  Gb  gdwatch.o
  gdriversettings            0x0009E8F8  gp+0x03E4         138      4  Zero  Gb  gdwatch.o
  gdriverspacket             0x0009EB28  gp+0x0614           4      4  Zero  Gb  gdwatch.o
  gfpgadata                  0x0009DF14  gp-0x0600         400      4  Zero  Gb  fpgad.o
  gfpgadataPredoSend         0x0009E0A4  gp-0x0470         336      4  Zero  Gb  InsTestingEntry.o
  gfpgasenddatalen           0x0009EB24  gp+0x0610           4      4  Zero  Gb  fpgad.o
  gframeParsebuf             0x0009CB84                  1 024      4  Zero  Gb  uart.o
  gframeindex                0x0009EB20  gp+0x060C           4      4  Zero  Gb  fpgad.o
  ggdworgdata_packet         0x0009EB1C  gp+0x0608           4      4  Zero  Gb  gdwatch.o
  ggpsorgdata                0x0009E864  gp+0x0350         148      4  Zero  Gb  frame_analysis.o
  ginputdata                 0x0009E564  gp+0x0050         270      4  Zero  Gb  InsTestingEntry.o
  gins912data                0x0009E454  gp-0x00C0         270      4  Zero  Gb  InsTestingEntry.o
  gins912outputmode          0x0009EB18  gp+0x0604           4      4  Zero  Gb  INS_Output.o
  gnavout                    0x0008C000                    112      8  Zero  Gb  INS_Data.o
  gpagedata                  0x0009E344  gp-0x01D0         272      4  Zero  Gb  fpgad.o
  gprotocol_send_baudrate    0x000A7B94                      4      4  Init  Gb  datado.o
  grxbuffer                  0x0009AB80                  4 098      4  Zero  Gb  uart.o
  grxlen                     0x0009EB14  gp+0x0600           4      4  Zero  Gb  uart.o
  grxst                      0x0009EB10  gp+0x05FC           4      4  Zero  Gb  uart.o
  gw                         0x0008C1E0                     24      8  Zero  Gb  InsTestingEntry.o
  hINSData                   0x0009E774  gp+0x0260         240      4  Zero  Gb  INS_Data.o
  hSetting                   0x0009D1E4                    596      4  Zero  Gb  computerFrameParse.o
  has_card_initialized.0     0x0009EB72  gp+0x065E           1         Zero  Lc  hpm_sdmmc_disk.c.o
  hello_str                  0x0009BB84                  2 048      4  Zero  Gb  sd_fatfs.o
  hpm_core_clock             0x0009EB0C  gp+0x05F8           4      4  Zero  Gb  hpm_clock_drv.c.o
  infoArr                    0x0009EA64  gp+0x0550          32      4  Zero  Gb  fpgad.o
  init_done                  0x0009EB71  gp+0x065D           1         Zero  Lc  ff_queue.o
  inscanruning               0x0009EB70  gp+0x065C           1         Zero  Gb  readpaoche.o
  paochedata                 0x0008BB08                    552      8  Zero  Gb  InsTestingEntry.o
  r_Gyro                     0x0008C1C8                     24      8  Zero  Gb  InsTestingEntry.o
  r_LastGyro                 0x0008C1B0                     24      8  Zero  Gb  InsTestingEntry.o
  r_TAFEAG8_buf              0x0009D8D0                    578      4  Zero  Gb  InsTestingEntry.o
  r_acc                      0x0009EAD4  gp+0x05C0          12      4  Zero  Gb  InsTestingEntry.o
  r_fog                      0x0009EAC8  gp+0x05B4          12      4  Zero  Gb  InsTestingEntry.o
  rcv_state                  0x0009EB7F  gp+0x066B           1         Init  Gb  readpaoche.o
  read_count                 0x0009EB08  gp+0x05F4           4      4  Zero  Gb  sd_fatfs.o
  read_ms                    0x0009EB04  gp+0x05F0           4      4  Zero  Gb  sd_fatfs.o
  rs422_frame                0x0009E984  gp+0x0470         100      4  Zero  Gb  frame_analysis.o
  s_fileBB00                 0x0008B8B8                    592      8  Zero  Gb  sd_fatfs.o
  s_fileCOM3                 0x0008B668                    592      8  Zero  Gb  sd_fatfs.o
  s_sd                       0x00080000                    216      8  Init  Lc  hpm_sdmmc_disk.c.o
  s_sd_aligned_buf           0x00080180                 16 384     64  Zero  Lc  hpm_sdmmc_disk.c.o
  s_sd_disk                  0x0009D438                    588      4  Zero  Gb  sd_fatfs.o
  s_sd_host                  0x01100000                    788      4  Zero  Lc  hpm_sdmmc_disk.c.o
  s_xpi_nor_config           0x0009E674  gp+0x0160         256      4  Zero  Lc  flash.o
  sd_rx_descriptors          0x011003D8                     64      8  Init  Lc  uart.o
  sd_tx_descriptors          0x01100398                     64      8  Init  Lc  uart.o
  sd_uart                    0x0008011C                     68      4  Init  Lc  uart.o
  sd_uart_rx_buf             0x000A4B84                  8 192      4  None  Lc  uart.o
  sd_uart_tx_buf             0x000A6B84                  4 096      4  None  Lc  uart.o
  sd_uart_tx_done            0x000A7B84                      1      4  None  Gb  uart.o
  stSetPara                  0x0009D684                    586      4  Zero  Gb  SetParaBao.o
  start_ticks                0x00080178                      8      8  Zero  Gb  sd_fatfs.o
  stdout                     0x000A7B90                      4      4  Init  Gb  hpm_debug_console.c.o
  tCnt.0                     0x0009BB82                      2      2  Zero  Lc  InsTestingEntry.o
  tCnt.0                     0x0009D8CE                      2      2  Zero  Lc  INS_Output.o
  tCnt.0                     0x0009DB12                      2      2  Zero  Lc  sd_fatfs.o
  test_count                 0x0009EB00  gp+0x05EC           4      4  Zero  Gb  sd_fatfs.o
  test_rx_descriptors        0x01100358                     64      8  Init  Lc  uart.o
  test_tx_descriptors        0x01100318                     64      8  Init  Lc  uart.o
  test_uart                  0x000800D8                     68      4  Init  Lc  uart.o
  test_uart_rx_buf           0x0009EB80  gp+0x066C      16 384      4  None  Lc  uart.o
  test_uart_tx_buf           0x000A2B80                  8 192      4  None  Lc  uart.o
  test_uart_tx_done          0x000A4B80                      1      4  None  Gb  uart.o
  time_base_100ms_Flag       0x0009EAFC  gp+0x05E8           4      4  Zero  Gb  bsp_tim.o
  time_base_100ms_periodic_cnt
                             0x0009EAF8  gp+0x05E4           4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_Flag        0x0009EAF4  gp+0x05E0           4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_periodic_cnt
                             0x0009EAF0  gp+0x05DC           4      4  Zero  Gb  bsp_tim.o
  time_base_periodic_cnt     0x0009EAEC  gp+0x05D8           4      4  Zero  Gb  bsp_tim.o
  uiLastBaoInDex.2           0x000A7B8C                      4      4  Init  Lc  SetParaBao.o
  uiLen.0                    0x0009EAE8  gp+0x05D4           4      4  Zero  Lc  uart.o
  uiOffsetAddr.0             0x0009EAE4  gp+0x05D0           4      4  Zero  Lc  SetParaBao.o
  work                       0x0009DB14                    512      4  Zero  Gb  sd_fatfs.o
  xgpsTime                   0x0009EAE0  gp+0x05CC           4      4  Zero  Lc  frame_analysis.o
  xgpsTrueTime               0x00080174                      4      4  Zero  Lc  frame_analysis.o

Read-write data symbols by address:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00080000             s_sd                              216      8  Init  Lc  hpm_sdmmc_disk.c.o
  0x000800D8             test_uart                          68      4  Init  Lc  uart.o
  0x0008011C             sd_uart                            68      4  Init  Lc  uart.o
  0x00080160             __SEGGER_RTL_global_locale
                                                            20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x00080160             __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x00080174             xgpsTrueTime                        4      4  Zero  Lc  frame_analysis.o
  0x00080178             start_ticks                         8      8  Zero  Gb  sd_fatfs.o
  0x00080180             s_sd_aligned_buf               16 384     64  Zero  Lc  hpm_sdmmc_disk.c.o
  0x00084180             g_Kalman                       15 128      8  Zero  Gb  main.o
  0x00087C98             g_Compen                       10 200      8  Zero  Gb  main.o
  0x0008A470             g_Navi                          2 776      8  Zero  Gb  main.o
  0x0008AF48             g_DynamicInertialSysAlign       1 000      8  Zero  Gb  main.o
  0x0008B330             g_InertialSysAlign                824      8  Zero  Gb  main.o
  0x0008B668             s_fileCOM3                        592      8  Zero  Gb  sd_fatfs.o
  0x0008B8B8             s_fileBB00                        592      8  Zero  Gb  sd_fatfs.o
  0x0008BB08             paochedata                        552      8  Zero  Gb  InsTestingEntry.o
  0x0008BD30             g_SysVar                          408      8  Zero  Gb  main.o
  0x0008BEC8             g_Align                           184      8  Zero  Gb  main.o
  0x0008BF80             g_SelfTest                        128      8  Zero  Gb  main.o
  0x0008C000             gnavout                           112      8  Zero  Gb  INS_Data.o
  0x0008C070             g_GNSSData_In_Use                 112      8  Zero  Gb  main.o
  0x0008C0E0             Gw                                 72      8  Zero  Gb  InsTestingEntry.o
  0x0008C128             Aw                                 72      8  Zero  Gb  InsTestingEntry.o
  0x0008C170             g_InitBind                         64      8  Zero  Gb  main.o
  0x0008C1B0             r_LastGyro                         24      8  Zero  Gb  InsTestingEntry.o
  0x0008C1C8             r_Gyro                             24      8  Zero  Gb  InsTestingEntry.o
  0x0008C1E0             gw                                 24      8  Zero  Gb  InsTestingEntry.o
  0x0008C1F8             gWm                                24      8  Zero  Gb  InsTestingEntry.o
  0x0008C210             gW_real                            24      8  Zero  Gb  InsTestingEntry.o
  0x0008C228             aw                                 24      8  Zero  Gb  InsTestingEntry.o
  0x0008C240             aWm                                24      8  Zero  Gb  InsTestingEntry.o
  0x0008C258             aW_real                            24      8  Zero  Gb  InsTestingEntry.o
  0x0008C270             LastAcc                            24      8  Zero  Gb  InsTestingEntry.o
  0x0008C288             Acc                                24      8  Zero  Gb  InsTestingEntry.o
  0x0008C2A0             end_ticks                           8      8  Zero  Gb  sd_fatfs.o
  0x0008C2A8             gdriverdatalist                59 608      4  Zero  Gb  gdwatch.o
  0x0009AB80             grxbuffer                       4 098      4  Zero  Gb  uart.o
  0x0009BB82             tCnt.0                              2      2  Zero  Lc  InsTestingEntry.o
  0x0009BB84             hello_str                       2 048      4  Zero  Gb  sd_fatfs.o
  0x0009C384             fpgatesttxt                     2 048      4  Zero  Gb  INS_Output.o
  0x0009CB84             gframeParsebuf                  1 024      4  Zero  Gb  uart.o
  0x0009CF84             DirBuf                            608      4  Zero  Lc  ff.c.o
  0x0009D1E4             hSetting                          596      4  Zero  Gb  computerFrameParse.o
  0x0009D438             s_sd_disk                         588      4  Zero  Gb  sd_fatfs.o
  0x0009D684             stSetPara                         586      4  Zero  Gb  SetParaBao.o
  0x0009D8CE             tCnt.0                              2      2  Zero  Lc  INS_Output.o
  0x0009D8D0             r_TAFEAG8_buf                     578      4  Zero  Gb  InsTestingEntry.o
  0x0009DB12             tCnt.0                              2      2  Zero  Lc  sd_fatfs.o
  0x0009DB14             work                              512      4  Zero  Gb  sd_fatfs.o
  0x0009DD14  gp-0x0800  LfnBuf                            512      4  Zero  Lc  ff.c.o
  0x0009DF14  gp-0x0600  gfpgadata                         400      4  Zero  Gb  fpgad.o
  0x0009E0A4  gp-0x0470  gfpgadataPredoSend                336      4  Zero  Gb  InsTestingEntry.o
  0x0009E1F4  gp-0x0320  BB00SdData                        336      4  Zero  Gb  InsTestingEntry.o
  0x0009E344  gp-0x01D0  gpagedata                         272      4  Zero  Gb  fpgad.o
  0x0009E454  gp-0x00C0  gins912data                       270      4  Zero  Gb  InsTestingEntry.o
  0x0009E562  gp+0x004E  SDCnt.0                             2      2  Zero  Lc  gd32f4xx_it.o
  0x0009E564  gp+0x0050  ginputdata                        270      4  Zero  Gb  InsTestingEntry.o
  0x0009E672  gp+0x015E  Fsid                                2      2  Zero  Lc  ff.c.o
  0x0009E674  gp+0x0160  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  0x0009E774  gp+0x0260  hINSData                          240      4  Zero  Gb  INS_Data.o
  0x0009E864  gp+0x0350  ggpsorgdata                       148      4  Zero  Gb  frame_analysis.o
  0x0009E8F8  gp+0x03E4  gdriversettings                   138      4  Zero  Gb  gdwatch.o
  0x0009E982  gp+0x046E  COM3Flag.3                          2      2  Zero  Lc  sd_fatfs.o
  0x0009E984  gp+0x0470  rs422_frame                       100      4  Zero  Gb  frame_analysis.o
  0x0009E9E8  gp+0x04D4  galgrithomresultTx                 44      4  Zero  Gb  gdwatch.o
  0x0009EA14  gp+0x0500  FatFs                              40      4  Zero  Lc  ff.c.o
  0x0009EA3C  gp+0x0528  gcanInfo                           38      4  Zero  Gb  fpgad.o
  0x0009EA62  gp+0x054E  BB00Flag.2                          2      2  Zero  Lc  sd_fatfs.o
  0x0009EA64  gp+0x0550  infoArr                            32      4  Zero  Gb  fpgad.o
  0x0009EA84  gp+0x0570  __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32gc_d_balanced.a)
  0x0009EA9C  gp+0x0588  StrMiddleWare                      24      4  Zero  Gb  InsTestingEntry.o
  0x0009EAB4  gp+0x05A0  canin                              20      4  Zero  Gb  InsTestingEntry.o
  0x0009EAC8  gp+0x05B4  r_fog                              12      4  Zero  Gb  InsTestingEntry.o
  0x0009EAD4  gp+0x05C0  r_acc                              12      4  Zero  Gb  InsTestingEntry.o
  0x0009EAE0  gp+0x05CC  xgpsTime                            4      4  Zero  Lc  frame_analysis.o
  0x0009EAE4  gp+0x05D0  uiOffsetAddr.0                      4      4  Zero  Lc  SetParaBao.o
  0x0009EAE8  gp+0x05D4  uiLen.0                             4      4  Zero  Lc  uart.o
  0x0009EAEC  gp+0x05D8  time_base_periodic_cnt              4      4  Zero  Gb  bsp_tim.o
  0x0009EAF0  gp+0x05DC  time_base_20ms_periodic_cnt
                                                             4      4  Zero  Gb  bsp_tim.o
  0x0009EAF4  gp+0x05E0  time_base_20ms_Flag                 4      4  Zero  Gb  bsp_tim.o
  0x0009EAF8  gp+0x05E4  time_base_100ms_periodic_cnt
                                                             4      4  Zero  Gb  bsp_tim.o
  0x0009EAFC  gp+0x05E8  time_base_100ms_Flag                4      4  Zero  Gb  bsp_tim.o
  0x0009EB00  gp+0x05EC  test_count                          4      4  Zero  Gb  sd_fatfs.o
  0x0009EB04  gp+0x05F0  read_ms                             4      4  Zero  Gb  sd_fatfs.o
  0x0009EB08  gp+0x05F4  read_count                          4      4  Zero  Gb  sd_fatfs.o
  0x0009EB0C  gp+0x05F8  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  0x0009EB10  gp+0x05FC  grxst                               4      4  Zero  Gb  uart.o
  0x0009EB14  gp+0x0600  grxlen                              4      4  Zero  Gb  uart.o
  0x0009EB18  gp+0x0604  gins912outputmode                   4      4  Zero  Gb  INS_Output.o
  0x0009EB1C  gp+0x0608  ggdworgdata_packet                  4      4  Zero  Gb  gdwatch.o
  0x0009EB20  gp+0x060C  gframeindex                         4      4  Zero  Gb  fpgad.o
  0x0009EB24  gp+0x0610  gfpgasenddatalen                    4      4  Zero  Gb  fpgad.o
  0x0009EB28  gp+0x0614  gdriverspacket                      4      4  Zero  Gb  gdwatch.o
  0x0009EB2C  gp+0x0618  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  0x0009EB30  gp+0x061C  g_DataOutTime                       4      4  Zero  Gb  SetParaBao.o
  0x0009EB34  gp+0x0620  g_CAN_Timeout_Start_flag            4      4  Zero  Gb  INS_Init.o
  0x0009EB38  gp+0x0624  g_CAN_Timeout_Cnt                   4      4  Zero  Gb  INS_Init.o
  0x0009EB3C  gp+0x0628  fpga_syn_count                      4      4  Zero  Gb  INS_Init.o
  0x0009EB40  gp+0x062C  fpga_syn_btw_last                   4      4  Zero  Gb  fpgad.o
  0x0009EB44  gp+0x0630  fpga_syn_btw                        4      4  Zero  Gb  fpgad.o
  0x0009EB48  gp+0x0634  fpga_syn_NAVI_btw                   4      4  Zero  Gb  fpgad.o
  0x0009EB4C  gp+0x0638  fpga_loop_count                     4      4  Zero  Gb  INS_Init.o
  0x0009EB50  gp+0x063C  flag.1                              4      4  Zero  Lc  SetParaBao.o
  0x0009EB54  gp+0x0640  en                                  4      4  Zero  Lc  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x0009EB58  gp+0x0644  current_reload                      4      4  Zero  Gb  timer.o
  0x0009EB5C  gp+0x0648  axisInfo                            4      4  Zero  Gb  frame_analysis.o
  0x0009EB60  gp+0x064C  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  0x0009EB64  gp+0x0650  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x0009EB68  gp+0x0654  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0x0009EB6C  gp+0x0658  NaviCompute_do_count                4      4  Zero  Gb  datado.o
  0x0009EB70  gp+0x065C  inscanruning                        1         Zero  Gb  readpaoche.o
  0x0009EB71  gp+0x065D  init_done                           1         Zero  Lc  ff_queue.o
  0x0009EB72  gp+0x065E  has_card_initialized.0              1         Zero  Lc  hpm_sdmmc_disk.c.o
  0x0009EB73  gp+0x065F  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  0x0009EB74  gp+0x0660  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  0x0009EB75  gp+0x0661  g_StartUpdateFirm                   1         Zero  Gb  main.o
  0x0009EB76  gp+0x0662  g_LEDIndicatorState                 1         Zero  Gb  INS_Data.o
  0x0009EB77  gp+0x0663  g_DataOutTypeFlag                   1         Zero  Gb  SetParaBao.o
  0x0009EB78  gp+0x0664  g_Com3WriteSdFlag                   1         Zero  Gb  uart.o
  0x0009EB79  gp+0x0665  g_BB00WriteSdFlag                   1         Zero  Gb  InsTestingEntry.o
  0x0009EB7A  gp+0x0666  fpga_syn                            1         Zero  Gb  INS_Init.o
  0x0009EB7B  gp+0x0667  fatfs_result                        1         Zero  Gb  sd_fatfs.o
  0x0009EB7C  gp+0x0668  SetSdOperateType                    1         Zero  Gb  SetParaBao.o
  0x0009EB7D  gp+0x0669  SetSdFlieType                       1         Zero  Gb  SetParaBao.o
  0x0009EB7E  gp+0x066A  CurrVol                             1         Zero  Lc  ff.c.o
  0x0009EB7F  gp+0x066B  rcv_state                           1         Init  Gb  readpaoche.o
  0x0009EB80  gp+0x066C  test_uart_rx_buf               16 384      4  None  Lc  uart.o
  0x000A2B80             test_uart_tx_buf                8 192      4  None  Lc  uart.o
  0x000A4B80             test_uart_tx_done                   1      4  None  Gb  uart.o
  0x000A4B84             sd_uart_rx_buf                  8 192      4  None  Lc  uart.o
  0x000A6B84             sd_uart_tx_buf                  4 096      4  None  Lc  uart.o
  0x000A7B84             sd_uart_tx_done                     1      4  None  Gb  uart.o
  0x000A7B85             g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  0x000A7B88             dma_done                            1      4  None  Lc  uart_dma.o
  0x000A7B8C             uiLastBaoInDex.2                    4      4  Init  Lc  SetParaBao.o
  0x000A7B90             stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  0x000A7B94             gprotocol_send_baudrate             4      4  Init  Gb  datado.o
  0x000A7B98             Com3FileName                        4      4  Init  Gb  sd_fatfs.o
  0x000A7B9C             BB00FileName                        4      4  Init  Gb  sd_fatfs.o
  0x000A7BA4             g_queue                        33 540      4  Init  Gb  hpm_sdmmc_disk.c.o
  0x01100000             s_sd_host                         788      4  Zero  Lc  hpm_sdmmc_disk.c.o
  0x01100318             test_tx_descriptors                64      8  Init  Lc  uart.o
  0x01100358             test_rx_descriptors                64      8  Init  Lc  uart.o
  0x01100398             sd_tx_descriptors                  64      8  Init  Lc  uart.o
  0x011003D8             sd_rx_descriptors                  64      8  Init  Lc  uart.o

Read-write data symbols by descending size:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  gdriverdatalist                59 608      4  Zero  Gb  gdwatch.o
  g_queue                        33 540      4  Init  Gb  hpm_sdmmc_disk.c.o
  s_sd_aligned_buf               16 384     64  Zero  Lc  hpm_sdmmc_disk.c.o
  test_uart_rx_buf               16 384      4  None  Lc  uart.o
  g_Kalman                       15 128      8  Zero  Gb  main.o
  g_Compen                       10 200      8  Zero  Gb  main.o
  sd_uart_rx_buf                  8 192      4  None  Lc  uart.o
  test_uart_tx_buf                8 192      4  None  Lc  uart.o
  grxbuffer                       4 098      4  Zero  Gb  uart.o
  sd_uart_tx_buf                  4 096      4  None  Lc  uart.o
  g_Navi                          2 776      8  Zero  Gb  main.o
  fpgatesttxt                     2 048      4  Zero  Gb  INS_Output.o
  hello_str                       2 048      4  Zero  Gb  sd_fatfs.o
  gframeParsebuf                  1 024      4  Zero  Gb  uart.o
  g_DynamicInertialSysAlign       1 000      8  Zero  Gb  main.o
  g_InertialSysAlign                824      8  Zero  Gb  main.o
  s_sd_host                         788      4  Zero  Lc  hpm_sdmmc_disk.c.o
  DirBuf                            608      4  Zero  Lc  ff.c.o
  hSetting                          596      4  Zero  Gb  computerFrameParse.o
  s_fileBB00                        592      8  Zero  Gb  sd_fatfs.o
  s_fileCOM3                        592      8  Zero  Gb  sd_fatfs.o
  s_sd_disk                         588      4  Zero  Gb  sd_fatfs.o
  stSetPara                         586      4  Zero  Gb  SetParaBao.o
  r_TAFEAG8_buf                     578      4  Zero  Gb  InsTestingEntry.o
  paochedata                        552      8  Zero  Gb  InsTestingEntry.o
  LfnBuf                            512      4  Zero  Lc  ff.c.o
  work                              512      4  Zero  Gb  sd_fatfs.o
  g_SysVar                          408      8  Zero  Gb  main.o
  gfpgadata                         400      4  Zero  Gb  fpgad.o
  BB00SdData                        336      4  Zero  Gb  InsTestingEntry.o
  gfpgadataPredoSend                336      4  Zero  Gb  InsTestingEntry.o
  gpagedata                         272      4  Zero  Gb  fpgad.o
  ginputdata                        270      4  Zero  Gb  InsTestingEntry.o
  gins912data                       270      4  Zero  Gb  InsTestingEntry.o
  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  hINSData                          240      4  Zero  Gb  INS_Data.o
  s_sd                              216      8  Init  Lc  hpm_sdmmc_disk.c.o
  g_Align                           184      8  Zero  Gb  main.o
  ggpsorgdata                       148      4  Zero  Gb  frame_analysis.o
  gdriversettings                   138      4  Zero  Gb  gdwatch.o
  g_SelfTest                        128      8  Zero  Gb  main.o
  g_GNSSData_In_Use                 112      8  Zero  Gb  main.o
  gnavout                           112      8  Zero  Gb  INS_Data.o
  rs422_frame                       100      4  Zero  Gb  frame_analysis.o
  Aw                                 72      8  Zero  Gb  InsTestingEntry.o
  Gw                                 72      8  Zero  Gb  InsTestingEntry.o
  sd_uart                            68      4  Init  Lc  uart.o
  test_uart                          68      4  Init  Lc  uart.o
  g_InitBind                         64      8  Zero  Gb  main.o
  sd_rx_descriptors                  64      8  Init  Lc  uart.o
  sd_tx_descriptors                  64      8  Init  Lc  uart.o
  test_rx_descriptors                64      8  Init  Lc  uart.o
  test_tx_descriptors                64      8  Init  Lc  uart.o
  galgrithomresultTx                 44      4  Zero  Gb  gdwatch.o
  FatFs                              40      4  Zero  Lc  ff.c.o
  gcanInfo                           38      4  Zero  Gb  fpgad.o
  infoArr                            32      4  Zero  Gb  fpgad.o
  Acc                                24      8  Zero  Gb  InsTestingEntry.o
  LastAcc                            24      8  Zero  Gb  InsTestingEntry.o
  StrMiddleWare                      24      4  Zero  Gb  InsTestingEntry.o
  __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32gc_d_balanced.a)
  aW_real                            24      8  Zero  Gb  InsTestingEntry.o
  aWm                                24      8  Zero  Gb  InsTestingEntry.o
  aw                                 24      8  Zero  Gb  InsTestingEntry.o
  gW_real                            24      8  Zero  Gb  InsTestingEntry.o
  gWm                                24      8  Zero  Gb  InsTestingEntry.o
  gw                                 24      8  Zero  Gb  InsTestingEntry.o
  r_Gyro                             24      8  Zero  Gb  InsTestingEntry.o
  r_LastGyro                         24      8  Zero  Gb  InsTestingEntry.o
  __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_global_locale
                                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  canin                              20      4  Zero  Gb  InsTestingEntry.o
  r_acc                              12      4  Zero  Gb  InsTestingEntry.o
  r_fog                              12      4  Zero  Gb  InsTestingEntry.o
  end_ticks                           8      8  Zero  Gb  sd_fatfs.o
  start_ticks                         8      8  Zero  Gb  sd_fatfs.o
  BB00FileName                        4      4  Init  Gb  sd_fatfs.o
  Com3FileName                        4      4  Init  Gb  sd_fatfs.o
  NaviCompute_do_count                4      4  Zero  Gb  datado.o
  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  axisInfo                            4      4  Zero  Gb  frame_analysis.o
  current_reload                      4      4  Zero  Gb  timer.o
  en                                  4      4  Zero  Lc  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  flag.1                              4      4  Zero  Lc  SetParaBao.o
  fpga_loop_count                     4      4  Zero  Gb  INS_Init.o
  fpga_syn_NAVI_btw                   4      4  Zero  Gb  fpgad.o
  fpga_syn_btw                        4      4  Zero  Gb  fpgad.o
  fpga_syn_btw_last                   4      4  Zero  Gb  fpgad.o
  fpga_syn_count                      4      4  Zero  Gb  INS_Init.o
  g_CAN_Timeout_Cnt                   4      4  Zero  Gb  INS_Init.o
  g_CAN_Timeout_Start_flag            4      4  Zero  Gb  INS_Init.o
  g_DataOutTime                       4      4  Zero  Gb  SetParaBao.o
  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  gdriverspacket                      4      4  Zero  Gb  gdwatch.o
  gfpgasenddatalen                    4      4  Zero  Gb  fpgad.o
  gframeindex                         4      4  Zero  Gb  fpgad.o
  ggdworgdata_packet                  4      4  Zero  Gb  gdwatch.o
  gins912outputmode                   4      4  Zero  Gb  INS_Output.o
  gprotocol_send_baudrate             4      4  Init  Gb  datado.o
  grxlen                              4      4  Zero  Gb  uart.o
  grxst                               4      4  Zero  Gb  uart.o
  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  read_count                          4      4  Zero  Gb  sd_fatfs.o
  read_ms                             4      4  Zero  Gb  sd_fatfs.o
  stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  test_count                          4      4  Zero  Gb  sd_fatfs.o
  time_base_100ms_Flag                4      4  Zero  Gb  bsp_tim.o
  time_base_100ms_periodic_cnt
                                      4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_Flag                 4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_periodic_cnt
                                      4      4  Zero  Gb  bsp_tim.o
  time_base_periodic_cnt              4      4  Zero  Gb  bsp_tim.o
  uiLastBaoInDex.2                    4      4  Init  Lc  SetParaBao.o
  uiLen.0                             4      4  Zero  Lc  uart.o
  uiOffsetAddr.0                      4      4  Zero  Lc  SetParaBao.o
  xgpsTime                            4      4  Zero  Lc  frame_analysis.o
  xgpsTrueTime                        4      4  Zero  Lc  frame_analysis.o
  BB00Flag.2                          2      2  Zero  Lc  sd_fatfs.o
  COM3Flag.3                          2      2  Zero  Lc  sd_fatfs.o
  Fsid                                2      2  Zero  Lc  ff.c.o
  SDCnt.0                             2      2  Zero  Lc  gd32f4xx_it.o
  tCnt.0                              2      2  Zero  Lc  InsTestingEntry.o
  tCnt.0                              2      2  Zero  Lc  INS_Output.o
  tCnt.0                              2      2  Zero  Lc  sd_fatfs.o
  CurrVol                             1         Zero  Lc  ff.c.o
  SetSdFlieType                       1         Zero  Gb  SetParaBao.o
  SetSdOperateType                    1         Zero  Gb  SetParaBao.o
  dma_done                            1      4  None  Lc  uart_dma.o
  fatfs_result                        1         Zero  Gb  sd_fatfs.o
  fpga_syn                            1         Zero  Gb  INS_Init.o
  g_BB00WriteSdFlag                   1         Zero  Gb  InsTestingEntry.o
  g_Com3WriteSdFlag                   1         Zero  Gb  uart.o
  g_DataOutTypeFlag                   1         Zero  Gb  SetParaBao.o
  g_LEDIndicatorState                 1         Zero  Gb  INS_Data.o
  g_StartUpdateFirm                   1         Zero  Gb  main.o
  g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  has_card_initialized.0              1         Zero  Lc  hpm_sdmmc_disk.c.o
  init_done                           1         Zero  Lc  ff_queue.o
  inscanruning                        1         Zero  Gb  readpaoche.o
  rcv_state                           1         Init  Gb  readpaoche.o
  sd_uart_tx_done                     1      4  None  Gb  uart.o
  test_uart_tx_done                   1      4  None  Gb  uart.o

Read-only data symbols by name:

  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  LfnOfs                     0x80027E18                     13      4  Cnst  Lc  ff.c.o
  SetDir                     0x800109BC                     96      4  Cnst  Lc  InsTestingEntry.o
  __SEGGER_RTL_Moeller_inverse_lut
                             0x80026BA4                  1 024      4  Cnst  Lc  intops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_aPower2       0x80010798                     72      8  Cnst  Lc  utilops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_ctype_map
                             0x80027140                    128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_ctype_mask
                             0x80028244                     13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale      0x800270BC                     12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_day_names
                             0x800281FC                     29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_month_names
                             0x80028188                     49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_am_pm_indicator
                             0x80028D10                      7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_data
                             0x800270C8                     88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_date_format
                             0x80028A14                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_date_time_format
                             0x80028D00                     15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_day_names
                             0x80027C2C                     58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_month_names
                             0x80028D18                     87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_time_format
                             0x80028538                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_codeset_ascii
                             0x80027120                     32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_data_empty_string
                             0x80028128                      1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_data_utf8_period
                             0x800105F4                      2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_ASinACos
                             0x800106E8                    112      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_ATan  0x80010628                     96      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_Log   0x800106C0                     40      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_SinCos
                             0x80010758                     64      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_Tan   0x80010688                     56      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_hex_lc        0x80026FA4                     16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_hex_uc        0x80026FB4                     16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ipow10        0x800107E0                    160      8  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_init_data__       0x8002DBE0               [77 372]      4  Cnst  Lc  [ Linker created ]
  __SEGGER_init_table__      0x8002DB00                  [224]      4  Cnst  Lc  [ Linker created ]
  axisTab                    0x80010958                    100      4  Cnst  Gb  frame_analysis.o
  cst.0                      0x800278C4                     14      4  Cnst  Lc  ff.c.o
  cst32.1                    0x80027888                     14      4  Cnst  Lc  ff.c.o
  cvt1.1                     0x8002792C                    498      4  Cnst  Lc  ffunicode.c.o
  cvt2.0                     0x80026A10                    188      4  Cnst  Lc  ffunicode.c.o
  defopt.2                   0x80011558                     16      4  Cnst  Lc  ff.c.o
  driver_num_buf             0x80028C50                      3      4  Cnst  Gb  sd_fatfs.o
  fw_info                    0x8000E010                    128      4  Cnst  Gb  hpm_bootheader.c.o
  header                     0x8000E000                     16      4  Cnst  Gb  hpm_bootheader.c.o
  option                     0x8000D000                     16      4  Cnst  Gb  board.c.o
  s_adc_clk_mux_node         0x80010094                      4      4  Cnst  Lc  hpm_clock_drv.c.o
  s_i2s_clk_mux_node         0x8001050C                      4      4  Cnst  Lc  hpm_clock_drv.c.o
  s_wdgs                     0x80026B0C                     16      4  Cnst  Lc  hpm_clock_drv.c.o
  uni2oem936                 0x80011568                 87 172      4  Cnst  Lc  ffunicode.c.o

Read-only data symbols by address:

     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x8000D000             option                             16      4  Cnst  Gb  board.c.o
  0x8000E000             header                             16      4  Cnst  Gb  hpm_bootheader.c.o
  0x8000E010             fw_info                           128      4  Cnst  Gb  hpm_bootheader.c.o
  0x80010094             s_adc_clk_mux_node                  4      4  Cnst  Lc  hpm_clock_drv.c.o
  0x8001050C             s_i2s_clk_mux_node                  4      4  Cnst  Lc  hpm_clock_drv.c.o
  0x800105F4             __SEGGER_RTL_data_utf8_period
                                                             2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80010628             __SEGGER_RTL_float64_ATan          96      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x80010688             __SEGGER_RTL_float64_Tan           56      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x800106C0             __SEGGER_RTL_float64_Log           40      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x800106E8             __SEGGER_RTL_float64_ASinACos
                                                           112      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x80010758             __SEGGER_RTL_float64_SinCos
                                                            64      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x80010798             __SEGGER_RTL_aPower2               72      8  Cnst  Lc  utilops.o (libc_rv32gc_d_balanced.a)
  0x800107E0             __SEGGER_RTL_ipow10               160      8  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x80010958             axisTab                           100      4  Cnst  Gb  frame_analysis.o
  0x800109BC             SetDir                             96      4  Cnst  Lc  InsTestingEntry.o
  0x80011558             defopt.2                           16      4  Cnst  Lc  ff.c.o
  0x80011568             uni2oem936                     87 172      4  Cnst  Lc  ffunicode.c.o
  0x80026A10             cvt2.0                            188      4  Cnst  Lc  ffunicode.c.o
  0x80026B0C             s_wdgs                             16      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80026BA4             __SEGGER_RTL_Moeller_inverse_lut
                                                         1 024      4  Cnst  Lc  intops.o (libc_rv32gc_d_balanced.a)
  0x80026FA4             __SEGGER_RTL_hex_lc                16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x80026FB4             __SEGGER_RTL_hex_uc                16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x800270BC             __SEGGER_RTL_c_locale              12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x800270C8             __SEGGER_RTL_c_locale_data
                                                            88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027120             __SEGGER_RTL_codeset_ascii
                                                            32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027140             __SEGGER_RTL_ascii_ctype_map
                                                           128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027888             cst32.1                            14      4  Cnst  Lc  ff.c.o
  0x800278C4             cst.0                              14      4  Cnst  Lc  ff.c.o
  0x8002792C             cvt1.1                            498      4  Cnst  Lc  ffunicode.c.o
  0x80027C2C             __SEGGER_RTL_c_locale_day_names
                                                            58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027E18             LfnOfs                             13      4  Cnst  Lc  ff.c.o
  0x80028128             __SEGGER_RTL_data_empty_string
                                                             1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028188             __SEGGER_RTL_c_locale_abbrev_month_names
                                                            49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x800281FC             __SEGGER_RTL_c_locale_abbrev_day_names
                                                            29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028244             __SEGGER_RTL_ascii_ctype_mask
                                                            13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028538             __SEGGER_RTL_c_locale_time_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028A14             __SEGGER_RTL_c_locale_date_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028C50             driver_num_buf                      3      4  Cnst  Gb  sd_fatfs.o
  0x80028D00             __SEGGER_RTL_c_locale_date_time_format
                                                            15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028D10             __SEGGER_RTL_c_locale_am_pm_indicator
                                                             7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028D18             __SEGGER_RTL_c_locale_month_names
                                                            87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DB00             __SEGGER_init_table__           [224]      4  Cnst  Lc  [ Linker created ]
  0x8002DBE0             __SEGGER_init_data__         [77 372]      4  Cnst  Lc  [ Linker created ]

Thread-local data symbols by name:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_errno         0x000A7BA0  tp+0x0000           4      4  Zero  Lc  errno.o (libc_rv32gc_d_balanced.a)

Thread-local data symbols by address:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x000A7BA0  tp+0x0000  __SEGGER_RTL_errno                  4      4  Zero  Lc  errno.o (libc_rv32gc_d_balanced.a)

Untyped symbols by name:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
  Symbol name                     Value     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __AHB_SRAM_segment_end__   0xF0308000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_size__  0x00008000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_start__
                             0xF0300000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_end__
                             0xF0300000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_start__
                             0xF0300000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_end__   0xF40F2000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_size__  0x00002000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_start__
                             0xF40F0000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_used_end__
                             0xF40F0000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_used_start__
                             0xF40F0000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_end__   0x01100000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_size__  0x00080000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_start__
                             0x01080000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_used_end__
                             0x01080000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_used_start__
                             0x01080000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_end__
                             0x80010000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_size__
                             0x00002000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_end__
                             0x8000E090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_size__
                             0x00000090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __DLM_segment_end__        0x000C0000                                ----  Gb  [ Linker created ]
  __DLM_segment_size__       0x00040000                                ----  Gb  [ Linker created ]
  __DLM_segment_start__      0x00080000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_end__   0x000C0000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_size__  0x00040000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_start__
                             0x00080000                                ----  Gb  [ Linker created ]
  __HEAPSIZE__               0x00004000                                ----  Gb  [ Linker created ]
  __ILM_segment_end__        0x00040000                                ----  Gb  [ Linker created ]
  __ILM_segment_size__       0x00040000                                ----  Gb  [ Linker created ]
  __ILM_segment_start__      0x00000000                                ----  Gb  [ Linker created ]
  __ILM_segment_used_end__   0x00022C40                                ----  Gb  [ Linker created ]
  __ILM_segment_used_size__  0x00022C40                                ----  Gb  [ Linker created ]
  __ILM_segment_used_start__
                             0x00000000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_end__
                             0x01140000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_size__
                             0x00040000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_start__
                             0x01100000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_used_end__
                             0x01100418                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_used_size__
                             0x00000418                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_used_start__
                             0x01100000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_end__
                             0x8000DC00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_size__
                             0x00000C00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_end__
                             0x8000D010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_size__
                             0x00000010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __SEGGER_RTL_2pow32        0x80010618                      8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_2pow64        0x80010610                      8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_2powNeg32     0x80010620                      8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SHARE_RAM_segment_end__  0x01180000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_size__
                             0x00004000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_start__
                             0x0117C000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_used_end__
                             0x0117C000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_used_start__
                             0x0117C000                                ----  Gb  [ Linker created ]
  __STACKSIZE__              0x00004000                                ----  Gb  [ Linker created ]
  __XPI0_segment_end__       0x80074000                                ----  Gb  [ Linker created ]
  __XPI0_segment_size__      0x00064000                                ----  Gb  [ Linker created ]
  __XPI0_segment_start__     0x80010000                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_end__  0x80040AF6                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_size__
                             0x00030AF6                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_start__
                             0x80010000                                ----  Gb  [ Linker created ]
  __app_load_addr__          0x80010000                                ----  Gb  [ Linker created ]
  __app_offset__             0x00002000                                ----  Gb  [ Linker created ]
  __boot_header_length__     0x00000090                                ----  Gb  [ Linker created ]
  __boot_header_load_addr__  0x8000E000                                ----  Gb  [ Linker created ]
  __fsymtab_end              0x8002792A                                ----  Gb  [ Linker created ]
  __fsymtab_start            0x8002792A                                ----  Gb  [ Linker created ]
  __fw_size__                0x00001000                                ----  Gb  [ Linker created ]
  __global_pointer$          0x0009E514  gp+0x0000                     ----  Gb  [ Linker created ]
  __heap_end__               0x000B3EA8                                ----  Gb  [ Linker created ]
  __heap_start__             0x000AFEA8                                ----  Gb  [ Linker created ]
  __noncacheable_end__       0x01140000                                ----  Gb  [ Linker created ]
  __noncacheable_start__     0x01100000                                ----  Gb  [ Linker created ]
  __nor_cfg_option_load_addr__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __rt_init_end              0x8002792A                                ----  Gb  [ Linker created ]
  __rt_init_start            0x8002792A                                ----  Gb  [ Linker created ]
  __rtmsymtab_end            0x8002792A                                ----  Gb  [ Linker created ]
  __rtmsymtab_start          0x8002792A                                ----  Gb  [ Linker created ]
  __share_mem_end__          0x01180000                                ----  Gb  [ Linker created ]
  __share_mem_start__        0x0117C000                                ----  Gb  [ Linker created ]
  __stack_end__              0x000C0000                                ----  Gb  [ Linker created ]
  __startup_complete         0x80010080                             2  Code  Lc  startup.s.o
  __thread_pointer$          0x000A7BA0                                ----  Gb  [ Linker created ]
  __usbh_class_info_end__    0x00080000                                ----  Gb  [ Linker created ]
  __usbh_class_info_start__  0x00080000                                ----  Gb  [ Linker created ]
  __vector_table             0x00000000                  [512]    512  Init  Gb  startup.s.o
  __vsymtab_end              0x8002792A                                ----  Gb  [ Linker created ]
  __vsymtab_start            0x8002792A                                ----  Gb  [ Linker created ]
  _extram_size               0x02000000                                ----  Gb  [ Linker created ]
  _flash_size                0x01000000                                ----  Gb  [ Linker created ]
  _stack                     0x000C0000                                ----  Gb  [ Linker created ]
  _stack_safe                0x000C0000                                ----  Gb  [ Linker created ]
  default_irq_handler        0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_1              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_10             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_100            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_101            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_102            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_103            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_104            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_105            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_106            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_107            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_108            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_109            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_11             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_110            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_111            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_112            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_113            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_114            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_115            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_116            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_117            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_118            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_119            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_12             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_120            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_121            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_122            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_123            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_124            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_125            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_126            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_127            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_13             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_14             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_15             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_16             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_17             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_18             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_19             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_2              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_20             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_21             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_22             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_23             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_24             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_25             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_26             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_27             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_28             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_29             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_3              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_30             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_31             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_32             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_33             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_34             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_35             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_36             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_37             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_38             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_39             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_4              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_40             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_41             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_42             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_43             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_44             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_45             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_46             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_47             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_48             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_49             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_5              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_50             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_51             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_52             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_53             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_54             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_55             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_56             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_57             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_58             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_59             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_6              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_60             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_61             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_62             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_63             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_64             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_65             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_66             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_67             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_68             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_69             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_7              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_70             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_71             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_72             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_73             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_74             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_75             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_76             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_77             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_78             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_79             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_8              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_80             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_81             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_82             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_83             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_84             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_85             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_86             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_87             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_88             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_89             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_9              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_90             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_91             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_92             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_93             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_94             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_95             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_96             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_97             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_98             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_99             0x0000054E                             4  Init  Wk  startup.s.o
  nmi_handler                0x0000054C                    [6]      4  Init  Wk  startup.s.o

Untyped symbols by address:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA0
  
       Value     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00000000             __vector_table                  [512]    512  Init  Gb  startup.s.o
  0x00000000             __SHARE_RAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_start__                         ----  Gb  [ Linker created ]
  0x00000000             __AXI_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __APB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __AHB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000010             __NOR_CFG_OPTION_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000090             __boot_header_length__                        ----  Gb  [ Linker created ]
  0x00000090             __BOOT_HEADER_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000418             __NONCACHEABLE_RAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x0000054C             nmi_handler                       [6]      4  Init  Wk  startup.s.o
  0x0000054E             default_isr_99                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_98                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_97                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_96                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_95                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_94                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_93                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_92                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_91                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_90                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_9                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_89                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_88                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_87                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_86                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_85                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_84                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_83                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_82                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_81                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_80                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_8                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_79                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_78                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_77                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_76                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_75                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_74                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_73                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_72                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_71                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_70                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_7                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_69                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_68                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_67                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_66                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_65                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_64                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_63                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_62                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_61                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_60                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_6                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_59                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_58                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_57                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_56                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_55                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_54                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_53                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_52                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_51                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_50                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_5                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_49                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_48                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_47                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_46                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_45                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_44                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_43                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_42                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_41                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_40                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_4                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_39                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_38                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_37                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_36                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_35                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_34                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_33                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_32                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_31                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_30                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_3                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_29                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_28                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_27                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_26                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_25                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_24                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_23                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_22                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_21                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_20                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_2                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_19                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_18                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_17                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_16                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_15                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_14                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_13                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_127                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_126                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_125                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_124                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_123                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_122                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_121                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_120                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_12                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_119                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_118                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_117                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_116                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_115                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_114                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_113                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_112                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_111                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_110                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_11                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_109                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_108                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_107                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_106                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_105                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_104                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_103                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_102                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_101                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_100                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_10                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_1                              4  Init  Wk  startup.s.o
  0x0000054E             default_irq_handler                        4  Init  Wk  startup.s.o
  0x00000C00             __NOR_CFG_OPTION_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00001000             __fw_size__                                   ----  Gb  [ Linker created ]
  0x00002000             __app_offset__                                ----  Gb  [ Linker created ]
  0x00002000             __BOOT_HEADER_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00002000             __APB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00004000             __STACKSIZE__                                 ----  Gb  [ Linker created ]
  0x00004000             __SHARE_RAM_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00004000             __HEAPSIZE__                                  ----  Gb  [ Linker created ]
  0x00008000             __AHB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00022C40             __ILM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00022C40             __ILM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x00030AF6             __XPI0_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00040000             __NONCACHEABLE_RAM_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00040000             __ILM_segment_size__                          ----  Gb  [ Linker created ]
  0x00040000             __ILM_segment_end__                           ----  Gb  [ Linker created ]
  0x00040000             __DLM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00040000             __DLM_segment_size__                          ----  Gb  [ Linker created ]
  0x00064000             __XPI0_segment_size__                         ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_start__                     ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_end__                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_start__                         ----  Gb  [ Linker created ]
  0x00080000             __AXI_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x0009E514  gp+0x0000  __global_pointer$                             ----  Gb  [ Linker created ]
  0x000A7BA0             __thread_pointer$                             ----  Gb  [ Linker created ]
  0x000AFEA8             __heap_start__                                ----  Gb  [ Linker created ]
  0x000B3EA8             __heap_end__                                  ----  Gb  [ Linker created ]
  0x000C0000             _stack_safe                                   ----  Gb  [ Linker created ]
  0x000C0000             _stack                                        ----  Gb  [ Linker created ]
  0x000C0000             __stack_end__                                 ----  Gb  [ Linker created ]
  0x000C0000             __DLM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x000C0000             __DLM_segment_end__                           ----  Gb  [ Linker created ]
  0x01000000             _flash_size                                   ----  Gb  [ Linker created ]
  0x01080000             __AXI_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x01080000             __AXI_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x01080000             __AXI_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x01100000             __noncacheable_start__                        ----  Gb  [ Linker created ]
  0x01100000             __NONCACHEABLE_RAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x01100000             __NONCACHEABLE_RAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x01100000             __AXI_SRAM_segment_end__                      ----  Gb  [ Linker created ]
  0x01100418             __NONCACHEABLE_RAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x01140000             __noncacheable_end__                          ----  Gb  [ Linker created ]
  0x01140000             __NONCACHEABLE_RAM_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x0117C000             __share_mem_start__                           ----  Gb  [ Linker created ]
  0x0117C000             __SHARE_RAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x0117C000             __SHARE_RAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x0117C000             __SHARE_RAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x01180000             __share_mem_end__                             ----  Gb  [ Linker created ]
  0x01180000             __SHARE_RAM_segment_end__                     ----  Gb  [ Linker created ]
  0x02000000             _extram_size                                  ----  Gb  [ Linker created ]
  0x8000D000             __nor_cfg_option_load_addr__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D010             __NOR_CFG_OPTION_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000DC00             __NOR_CFG_OPTION_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __boot_header_load_addr__                     ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E090             __BOOT_HEADER_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __app_load_addr__                             ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_start__                        ----  Gb  [ Linker created ]
  0x80010000             __BOOT_HEADER_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010080             __startup_complete                         2  Code  Lc  startup.s.o
  0x80010610             __SEGGER_RTL_2pow64                 8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x80010618             __SEGGER_RTL_2pow32                 8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x80010620             __SEGGER_RTL_2powNeg32              8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002792A             __vsymtab_start                               ----  Gb  [ Linker created ]
  0x8002792A             __vsymtab_end                                 ----  Gb  [ Linker created ]
  0x8002792A             __rtmsymtab_start                             ----  Gb  [ Linker created ]
  0x8002792A             __rtmsymtab_end                               ----  Gb  [ Linker created ]
  0x8002792A             __rt_init_start                               ----  Gb  [ Linker created ]
  0x8002792A             __rt_init_end                                 ----  Gb  [ Linker created ]
  0x8002792A             __fsymtab_start                               ----  Gb  [ Linker created ]
  0x8002792A             __fsymtab_end                                 ----  Gb  [ Linker created ]
  0x80040AF6             __XPI0_segment_used_end__                     ----  Gb  [ Linker created ]
  0x80074000             __XPI0_segment_end__                          ----  Gb  [ Linker created ]
  0xF0300000             __AHB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0300000             __AHB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF0300000             __AHB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0308000             __AHB_SRAM_segment_end__                      ----  Gb  [ Linker created ]
  0xF40F0000             __APB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF40F0000             __APB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF40F0000             __APB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF40F2000             __APB_SRAM_segment_end__                      ----  Gb  [ Linker created ]


***********************************************************************************************
***                                                                                         ***
***                                      LINK SUMMARY                                       ***
***                                                                                         ***
***********************************************************************************************

Memory breakdown:

  165 762 bytes read-only  code    + 
  176 183 bytes read-only  data    = 341 945 bytes read-only (total)
  230 071 bytes read-write data

Region summary:

  Name        Range                     Size                 Used               Unused       Alignment Loss
  ----------  -----------------  -----------  -------------------  -------------------  -------------------
  ILM         00000000-0003ffff      262 144      142 392  54.32%      119 744  45.68%            8   0.00%
  DLM         00080000-000bffff      262 144      229 027  87.37%       33 114  12.63%            3   0.00%
  NONCACHEABLE_RAM
              01100000-0113ffff      262 144        1 044   0.40%      261 100  99.60%            0   0.00%
  NOR_CFG_OPTION
              8000d000-8000dbff        3 072           16   0.52%        3 056  99.48%            0   0.00%
  BOOT_HEADER
              8000e000-8000ffff        8 192          144   1.76%        8 048  98.24%            0   0.00%
  XPI0        80010000-80073fff      409 600      199 393  48.68%      210 190  51.32%           17   0.00%

Link complete: 0 errors, 0 warnings, 0 remarks
