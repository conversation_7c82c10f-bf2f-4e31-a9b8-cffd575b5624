/*!
    \file    RTE_Components.h
    \brief   RTE Components configuration file for GD32F4xx
    \note    This file is automatically generated by <PERSON><PERSON> MDK
*/

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

/* Define the Device Header File: */
#define CMSIS_device_header "gd32f4xx.h"

/* GigaDevice::Device:GD32F4xx_StdPeripherals:ADC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_ADC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:CAN:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_CAN
/* GigaDevice::Device:GD32F4xx_StdPeripherals:CRC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_CRC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:CTC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_CTC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:DAC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_DAC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:DBG:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_DBG
/* GigaDevice::Device:GD32F4xx_StdPeripherals:DCI:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_DCI
/* GigaDevice::Device:GD32F4xx_StdPeripherals:DMA:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_DMA
/* GigaDevice::Device:GD32F4xx_StdPeripherals:ENET:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_ENET
/* GigaDevice::Device:GD32F4xx_StdPeripherals:EXMC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_EXMC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:EXTI:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_EXTI
/* GigaDevice::Device:GD32F4xx_StdPeripherals:FMC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_FMC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:FWDGT:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_FWDGT
/* GigaDevice::Device:GD32F4xx_StdPeripherals:GPIO:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_GPIO
/* GigaDevice::Device:GD32F4xx_StdPeripherals:I2C:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_I2C
/* GigaDevice::Device:GD32F4xx_StdPeripherals:IPA:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_IPA
/* GigaDevice::Device:GD32F4xx_StdPeripherals:IREF:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_IREF
/* GigaDevice::Device:GD32F4xx_StdPeripherals:MISC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_MISC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:PMU:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_PMU
/* GigaDevice::Device:GD32F4xx_StdPeripherals:RCU:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_RCU
/* GigaDevice::Device:GD32F4xx_StdPeripherals:RTC:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_RTC
/* GigaDevice::Device:GD32F4xx_StdPeripherals:SDIO:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_SDIO
/* GigaDevice::Device:GD32F4xx_StdPeripherals:SPI:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_SPI
/* GigaDevice::Device:GD32F4xx_StdPeripherals:SYSCFG:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_SYSCFG
/* GigaDevice::Device:GD32F4xx_StdPeripherals:TIMER:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_TIMER
/* GigaDevice::Device:GD32F4xx_StdPeripherals:TLI:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_TLI
/* GigaDevice::Device:GD32F4xx_StdPeripherals:TRNG:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_TRNG
/* GigaDevice::Device:GD32F4xx_StdPeripherals:USART:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_USART
/* GigaDevice::Device:GD32F4xx_StdPeripherals:WWDGT:3.0.3 */
#define RTE_DEVICE_STDPERIPHERALS_WWDGT

#endif /* RTE_COMPONENTS_H */
