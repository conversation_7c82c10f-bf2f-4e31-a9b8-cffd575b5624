<!DOCTYPE CrossStudio_Session_File>
<session>
 <Bookmarks/>
 <Breakpoints groups="Breakpoints" active_group="Breakpoints"/>
 <ExecutionProfileWindow/>
 <FrameBuffer>
  <FrameBufferWindow width="1465605168" keepAspectRatio="0" zoomToFitWindow="0" addressSpace="" format="0" height="293" autoEvaluate="0" scaleFactor="1" refreshPeriod="0" name="gpio_example - hpm6750_Debug" addressText="" accessByDisplayWidth="0"/>
 </FrameBuffer>
 <Memory1>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="4" addressText="&amp;test_uart_tx_buf"/>
 </Memory1>
 <Memory2>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="" addressText=""/>
 </Memory2>
 <Memory3>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="" addressText=""/>
 </Memory3>
 <Memory4>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="" addressText=""/>
 </Memory4>
 <Project>
  <ProjectSessionItem path="gpio_example"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;bsp"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;bsp;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;INAV"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Protocol"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Source"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Source;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;boards"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;boards;hpm6750"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;drivers"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;drivers;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;middleware"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;middleware;fatfs"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;middleware;fatfs;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;middleware;fatfs;src;portable"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc;HPM6700"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc;HPM6700;HPM6750"/>
 </Project>
 <Register1>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register1>
 <Register2>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register2>
 <Register3>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register3>
 <Register4>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register4>
 <Threads>
  <ThreadsWindow showLists=""/>
 </Threads>
 <TraceWindow>
  <Trace enabled="Yes"/>
 </TraceWindow>
 <Watch1>
  <Watches active="1" update="Never">
   <Watchpoint expression="old_recvlen" name="old_recvlen" radix="10" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="new_recvlen" name="new_recvlen" radix="10" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="size" name="size" radix="10" linenumber="0" filename="../segger_embedded_studio"/>
  </Watches>
 </Watch1>
 <Watch2>
  <Watches active="0" update="Never"/>
 </Watch2>
 <Watch3>
  <Watches active="0" update="Never"/>
 </Watch3>
 <Watch4>
  <Watches active="0" update="Never"/>
 </Watch4>
 <Files>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="189" useTextEdit="1" path="../../src/uart.c" left="0" top="169" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="1" y="127" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/sdxc/hpm_sdmmc_disk.c" left="0" top="125" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="29" y="133" useTextEdit="1" path="../../src/INAV/adxl355.c" left="0" top="117" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="12" y="31" useTextEdit="1" path="../../src/uart_dma.h" left="0" top="6" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="103" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/ff_queue.c" left="0" top="62" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="25" y="16" useTextEdit="1" path="../../src/uart.h" left="0" top="4" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="21" y="146" useTextEdit="1" path="../../src/main.c" left="0" top="137" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="6" useTextEdit="1" path="../../src/Protocol/protocol.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="278" useTextEdit="1" path="../../src/INAV/ins.h" left="0" top="254" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="247" useTextEdit="1" path="../../src/Source/src/INS_Init.c" left="0" top="228" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="33" useTextEdit="1" path="../../src/Source/inc/deviceconfig.h" left="0" top="33" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="182" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/trap.c" left="0" top="141" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="48" useTextEdit="1" path="../../src/Source/src/systick.c" left="0" top="9" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="18" y="1914" useTextEdit="1" path="../../src/Source/src/SetParaBao.c" left="0" top="1910" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="20" y="33" useTextEdit="1" path="../../src/Source/src/FirmwareUpdateFile.c" left="0" top="10" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="17" y="162" useTextEdit="1" path="../../src/Source/inc/fpgad.h" left="0" top="131" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="284" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_dma_drv.h" left="0" top="267" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="58" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_csr_drv.h" left="0" top="40" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="238" useTextEdit="1" path="../../src/Source/src/gd32f4xx_it.c" left="0" top="197" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="23" y="165" useTextEdit="1" path="../../src/Source/inc/SetParaBao.h" left="0" top="147" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="163" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_plic_drv.h" left="0" top="135" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="12" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_ppor_drv.h" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="595" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc.h" left="0" top="577" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="27" y="60" useTextEdit="1" path="../../src/Source/src/fpgad.c" left="0" top="44" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="6" y="196" useTextEdit="1" path="../../src/sd_fatfs.c" left="0" top="171" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="10" y="25" useTextEdit="1" path="../../src/Source/src/INS_Output.c" left="0" top="9" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="171" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_interrupt.h" left="0" top="154" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="46" y="129" useTextEdit="1" path="../../src/flash.c" left="0" top="102" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="13" y="18" useTextEdit="1" path="../../src/Source/src/INS_Data.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="297" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/segger/startup.s" left="0" top="274" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="78" y="1313" useTextEdit="1" path="../../src/Protocol/InsTestingEntry.c" left="0" top="1328" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="62" useTextEdit="1" path="../../src/Source/src/datado.c" left="0" top="22" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="2" y="362" useTextEdit="1" path="../../src/INAV/readpaoche.c" left="0" top="340" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="100" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_gpio_drv.h" left="0" top="78" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="134" useTextEdit="1" path="../../src/Source/src/gdwatch.c" left="0" top="94" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="622" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_clock_drv.c" left="0" top="582" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="1076" useTextEdit="1" path="../../../../../hpm_sdk/drivers/src/hpm_sdxc_drv.c" left="0" top="1075" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="18" y="31" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/ff_queue.h" left="0" top="11" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="11" y="563" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/common/ff.c" left="0" top="546" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="24" y="64" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/diskio.c" left="0" top="35" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="191" useTextEdit="1" path="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_host.c" left="0" top="171" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="14" y="25" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/diskio.h" left="0" top="13" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="1" y="1286" useTextEdit="1" path="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_sd.c" left="0" top="1258" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="14" y="97" useTextEdit="1" path="../../src/sdram.c" left="0" top="68" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="21" y="90" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/common/ffconf.h" left="0" top="70" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="282" useTextEdit="1" path="../../src/bsp/src/bsp_fmc.c" left="0" top="269" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="152" useTextEdit="1" path="../../src/Protocol/InsTestingEntry.h" left="0" top="114" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="70" y="784" useTextEdit="1" path="../../src/Protocol/frame_analysis.c" left="0" selected="1" top="764" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="24" y="963" useTextEdit="1" path="../../src/Protocol/computerFrameParse.c" left="0" top="960" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="25" y="152" useTextEdit="1" path="../../../../boards/hpm6750/board.c" left="0" top="135" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="54" y="356" useTextEdit="1" path="../../../../boards/hpm6750/board.h" left="0" top="345" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="gpio_example.emProject" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="51" y="37" useTextEdit="1" path="../../../../../hpm_sdk/drivers/src/hpm_uart_drv.c" left="0" top="21" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="17" useTextEdit="1" path="../../../../boards/hpm6750/pinmux.c" left="0" top="17" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="78" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc_feature.h" left="0" top="61" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="4" y="98" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_uart_drv.h" left="0" top="81" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="19" y="59" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_dmamux_drv.h" left="0" top="22" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="9" y="118" useTextEdit="1" path="../../src/uart_dma.c" left="0" top="118" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="50" y="19" useTextEdit="1" path="../../src/Protocol/frame_analysis.h" left="0" top="9" codecName="Default"/>
 </Files>
 <EMStudioWindow activeProject="gpio_example - hpm6750" fileDialogDefaultFilter="*.c" autoConnectTarget="J-Link" buildConfiguration="Debug" sessionSettings="" debugSearchFileMap="" fileDialogInitialDirectory="D:/workspace/01_code/ae_workspace/HPM6750_INS-370M_SDXC_ASYNC_U2/HPM6750_INS-370M/my_project/app/gpio/src" debugSearchPath="" autoConnectCapabilities="3199"/>
</session>
