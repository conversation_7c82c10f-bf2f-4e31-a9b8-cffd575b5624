# INAV算法移植总结

## 项目概述

本项目成功将my_project中的INAV（惯性导航）算法移植到INS370-8K24-INS600M-21A项目中，保持了目标项目的驱动程序不变，仅移植了核心算法逻辑。

## 移植的文件列表

### 1. 头文件 (INAV/)
- `CONST.h` - 常量定义
- `TYPEDEFINE.h` - 数据类型定义  
- `DATASTRUCT.h` - 数据结构定义
- `FUNCTION.h` - 函数声明
- `GLOBALDATA.h` - 全局变量声明
- `ins.h` - INS算法接口头文件

### 2. 源文件 (INAV/)
- `navi.c` - 核心导航解算算法
- `kalman.c` - Kalman滤波算法
- `align.c` - 初始对准算法
- `matvecmath.c` - 矩阵向量数学运算
- `inav_main.c` - INAV算法主入口

### 3. 测试文件 (INAV/)
- `test_inav.c` - 算法测试程序
- `Makefile` - 编译配置文件

## 核心算法模块

### 1. 导航解算 (navi.c)
- **功能**: 实现惯性导航系统的核心解算
- **主要函数**:
  - `NaviCompute()` - 导航主计算函数
  - `ComputeQ()` - 四元数更新
  - `ComputeCnb()` - 姿态矩阵计算
  - `ComputeVn()` - 速度更新
  - `ComputePos()` - 位置更新
  - `ComputeDeg_Ex()` - 角度输出转换

### 2. Kalman滤波 (kalman.c)
- **功能**: 实现GNSS/INS组合导航的Kalman滤波
- **主要函数**:
  - `Kalman_Init()` - 滤波器初始化
  - `KalCompute()` - Kalman滤波计算
  - `ComputeFn()` - 状态转移矩阵计算
  - `ComputeKk()` - Kalman增益计算
  - `ErrCorrect()` - 误差修正

### 3. 初始对准 (align.c)
- **功能**: 实现惯性系统的初始对准
- **主要函数**:
  - `InertialSysAlign_Init()` - 对准初始化
  - `InertialSysAlignCompute()` - 对准计算
  - `FinishInertialSysAlign()` - 完成对准
  - `ComputeVG()` - 垂直陀螺对准

### 4. 数学运算 (matvecmath.c)
- **功能**: 提供矩阵和向量运算支持
- **主要函数**:
  - `Mat_Mul()` - 矩阵乘法
  - `Mat_Inv()` - 矩阵求逆
  - `Vec_Cross()` - 向量叉积
  - `Qua_Mul()` - 四元数乘法

## 系统工作流程

### 1. 初始化阶段
```
INAV_Init() -> 系统初始化
设置初始位置参数
初始化各算法模块
```

### 2. 运行阶段
```
INAV_Process() -> 主处理循环
├── 待机阶段 (PHASE_STANDBY)
├── 粗对准阶段 (PHASE_COARSE_ALIGN)
├── 纯惯导阶段 (PHASE_INS_NAVI)
└── 组合导航阶段 (PHASE_INTEGRATED_NAVI)
```

### 3. 数据流
```
IMU数据 -> 导航解算 -> 位置/速度/姿态
GNSS数据 -> Kalman滤波 -> 误差修正
```

## 关键特性

### 1. 多阶段工作模式
- **待机阶段**: 系统启动等待
- **粗对准阶段**: 惯性系统初始对准
- **纯惯导阶段**: GNSS失效时的纯惯性导航
- **组合导航阶段**: GNSS/INS组合导航

### 2. 自适应滤波
- 根据GNSS数据有效性自动切换工作模式
- 支持GNSS信号丢失后的自主导航
- 信号恢复后自动重新进入组合模式

### 3. 高精度算法
- 15状态Kalman滤波器
- 四元数姿态表示避免万向锁
- 多种观测模式支持

## 编译和测试

### 编译命令
```bash
cd INS370-8K24-INS600M-21A/INAV
make all
```

### 运行测试
```bash
make test
```

### 语法检查
```bash
make check
```

## 配置参数

### 1. 时间常数
- `TIME_NAVI` = 0.01s (导航周期)
- `TIME_FILTER` = 0.1s (滤波周期)
- `TIME_COARSE_ALIGN` = 300s (粗对准时间)

### 2. 精度参数
- 位置精度: 1.7e-7 rad
- 速度精度: 0.25 m/s
- 姿态精度: 0.5°

### 3. 默认初始位置
- 纬度: 31.2304° (上海)
- 经度: 121.4737°
- 高度: 50.0m

## 接口函数

### 主要API
```c
void INAV_Init(void);
void INAV_Process(DPARA Gyro[3], DPARA Acc[3], PAOCHE_FRAME_STRUCT* gnss_data);
p_Navi INAV_GetNaviResult(void);
p_SysVar INAV_GetSystemStatus(void);
void INAV_SetInitialPosition(DPARA latitude_deg, DPARA longitude_deg, DPARA height_m);
```

## 移植注意事项

### 1. 保持兼容性
- 保留了原项目的驱动程序结构
- 使用相同的数据类型定义
- 保持了原有的编程风格

### 2. 模块化设计
- 算法模块独立，便于维护
- 清晰的接口定义
- 良好的代码组织结构

### 3. 可扩展性
- 支持多种传感器配置
- 可配置的滤波参数
- 灵活的工作模式切换

## 验证结果

通过测试程序验证，移植的算法能够：
1. 正确完成系统初始化
2. 成功进行初始对准
3. 实现稳定的导航解算
4. 正确处理GNSS数据
5. 平滑切换工作模式

## 总结

本次移植成功实现了完整的INAV算法功能，包括：
- ✅ 惯性导航解算
- ✅ Kalman滤波
- ✅ 初始对准
- ✅ 数学运算库
- ✅ 系统状态管理
- ✅ 测试验证程序

算法移植保持了原有的高精度和稳定性，同时适配了目标平台的硬件环境。
