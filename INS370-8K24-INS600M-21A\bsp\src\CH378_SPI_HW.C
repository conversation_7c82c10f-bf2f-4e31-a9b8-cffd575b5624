/********************************** (C) COPYRIGHT *******************************
* File Name          : SPI_HW.C
* Author             : MJX
* Version            : V1.20
* Date               : 2015/08/28
* Description        : CH378芯片 CH378芯片 硬件标准SPI串行连接的硬件抽象层 V1.0
*                      提供I/O接口子程序
*******************************************************************************/



/*******************************************************************************/
/* 头文件包含 */
#include "CH378_HAL.h"										 /* CH378硬件定义相关头文件 */	
#include "stdio.h"
#include "systick.h"
#include "CH378INC.H"
#include "FILE_SYS.h"
/*******************************************************************************/

/*******************************************************************************
* Function Name  : CH378_Port_Init
* Description    : CH378端口初始化
*                  由于使用SPI读写时序,所以进行初始化
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void CH378_Port_Init( void )  
{
#if CHIP_USED == USE_CHIP_GD32
	spi_parameter_struct spi_para;
	
	/* 初始化SPI接口 */
	rcu_periph_clock_enable(RCU_GPIOG);
	rcu_periph_clock_enable(RCU_SPI3);
	//MOSI -- PG13    MISO -- PG12   CLK -- PG11
	gpio_af_set(CH378_SPI_Port, GPIO_AF_6, CH378_SPI_MOSI_PIN | CH378_SPI_MISO_PIN | CH378_SPI_CLK_PIN);
	// SPI SCK/MISO/MOSI pin configuration
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_AF, GPIO_PUPD_NONE, CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN );
	gpio_output_options_set(CH378_SPI_Port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_SPI_MOSI_PIN | CH378_SPI_CLK_PIN);
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_AF, GPIO_PUPD_PULLUP, CH378_SPI_MISO_PIN );
	gpio_output_options_set(CH378_SPI_Port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_SPI_MISO_PIN );
	
	// Configure GPIO PIN for Chip select  CS Pin -- PG14
	gpio_mode_set(CH378_SPI_Port, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, CH378_SPI_CS_PIN);
	gpio_output_options_set(CH378_SPI_Port, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_SPI_CS_PIN);
	
	/* SPI configuration */ 
	spi_para.device_mode = SPI_MASTER;
	spi_para.trans_mode = SPI_TRANSMODE_FULLDUPLEX;
	spi_para.frame_size = SPI_FRAMESIZE_8BIT;
	spi_para.endian = SPI_ENDIAN_MSB;
	spi_para.nss = SPI_NSS_SOFT;
	spi_para.clock_polarity_phase = SPI_CK_PL_HIGH_PH_2EDGE;
	spi_para.prescale = SPI_PSC_16;
	spi_init(SPI3, &spi_para);
	spi_crc_off(SPI3);

	//Enable SPI
	spi_enable(SPI3);
	
	/*CS high */
	CH378_SPI_SCS_HIGH();
	// INT Pin
	rcu_periph_clock_enable(RCU_GPIOA);
	gpio_mode_set(CH378_INT_Port, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, CH378_INT_PIN);
	// RST Pin
	rcu_periph_clock_enable(RCU_GPIOB);
	gpio_mode_set(CH378_RST_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, CH378_RST_IO_PIN);
	gpio_output_options_set(CH378_RST_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, CH378_RST_IO_PIN);
	
#else
	
	SPI_InitTypeDef  SPI_InitStructure;
	GPIO_InitTypeDef GPIO_InitStructure;

	/* 初始化SPI接口 */   
	/* Enable SPI1 and GPIOA clocks */
	RCC_APB2PeriphClockCmd( RCC_APB2Periph_SPI1 | RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB, ENABLE );/* 实际在这之前已经使能过 */

	/* Configure SPI1 pins: NSS, SCK, MISO and MOSI */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7 ;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode =  GPIO_Mode_AF_PP;			/* 推拉输出备用功能 */
	GPIO_Init( GPIOA, &GPIO_InitStructure );

	/* Configure PA.4 as Output push-pull, used as Flash Chip select */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;			/* 推拉输出 */
	GPIO_Init( GPIOB, &GPIO_InitStructure );

	/* Deselect the SD: Chip Select high */
	CH378_SPI_SCS_HIGH( );

	/* SPI1 configuration */ 
	SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;	/* SPI配置成两线的单向全双工通信 */
	SPI_InitStructure.SPI_Mode = SPI_Mode_Master;						/* SPI主机 */
	SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;					/* SPI8位数据格式传输 */
	SPI_InitStructure.SPI_CPOL = SPI_CPOL_Low;							/* 时钟低时活动 */  				//	改成高则不行?????????????????
	SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;						/* 数据在时钟第二个边沿时捕获 */
	SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;							/* 内部NSS信号由SSI控制 */
	SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_4;	/* 波特率预分频数为4 */
	SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;					/* 传输时高位在前 */
	SPI_InitStructure.SPI_CRCPolynomial = 7;
	SPI_Init( SPI1, &SPI_InitStructure );

	/* Enable SPI1  */
	SPI_Cmd( SPI1, ENABLE );

	/* 初始化中断引脚 */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;			/* 上拉输入 */
	GPIO_Init( GPIOA, &GPIO_InitStructure );
#endif
}

/*******************************************************************************
* Function Name  : Spi376Exchange
* Description    : 硬件SPI输出且输入8个位数据
* Input          : d---将要送入到CH378的数据
* Output         : None
* Return         : None
*******************************************************************************/
UINT8 SPI_Exchange( UINT8 d )  
{
#if CHIP_USED == USE_CHIP_GD32
	while(RESET == spi_i2s_flag_get(SPI3,SPI_FLAG_TBE));
	spi_i2s_data_transmit(SPI3,d);
	while(RESET == spi_i2s_flag_get(SPI3,SPI_FLAG_RBNE));
	return spi_i2s_data_receive(SPI3);
	
#else
	
	/* Loop while DR register in not emplty */
	while( ( SPI1->SR & SPI_I2S_FLAG_TXE ) == RESET );

	/* Send byte through the SPI1 peripheral */
//	SPI_I2S_SendData(SPI1, byte);
	SPI1->DR = d;

	/* Wait to receive a byte */
	while( ( SPI1->SR & SPI_I2S_FLAG_RXNE ) == RESET );

	/* Return the byte read from the SPI bus */
//	return  SPI_I2S_ReceiveData(SPI1);
	return( SPI1->DR );
#endif
}

void SPI_Transmit(UINT8 d)
{
#if CHIP_USED == USE_CHIP_GD32
	while(RESET == spi_i2s_flag_get(SPI3,SPI_FLAG_TBE));
	spi_i2s_data_transmit(SPI3,d);
#else
	while( ( SPI1->SR & SPI_I2S_FLAG_TXE ) == RESET );
	SPI1->DR = d;
#endif
}

UINT8 SPI_Receive(void)
{
#if CHIP_USED == USE_CHIP_GD32
	while(RESET == spi_i2s_flag_get(SPI3,SPI_FLAG_RBNE));
	return spi_i2s_data_receive(SPI3);
#else
	while( ( SPI1->SR & SPI_I2S_FLAG_RXNE ) == RESET );
	return( SPI1->DR );
#endif
}

/*******************************************************************************
* Function Name  : xWriteCH378Cmd
* Description    : 向CH378写命令
* Input          : mCmd---将要写入CH378的命令码
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Cmd( UINT8 mCmd )  
{
	 
	CH378_SPI_SCS_HIGH( );									/* 防止之前未通过xEndCH378Cmd禁止SPI片选 */
	delay_us(5);
	/* 对于双向I/O引脚模拟SPI接口,那么必须确保已经设置SPI_SCS,SPI_SCK,SPI_SDI为输出方向,SPI_SDO为输入方向 */
	CH378_SPI_SCS_LOW( );									/* SPI片选有效 */
	/* 发送命令码 */
	SPI_Exchange( mCmd );									/* 发出命令码 */
	delay_us( 2 );											/* 延时1.5uS确保读写周期大于1.5uS */
	CH378_SPI_SCS_HIGH( );
//	delay_us( 1 );
}

/*******************************************************************************
* Function Name  : xWriteCH378Data
* Description    : 向CH378写数据
* Input          : mData---将要写入CH378的数据
* Output         : None
* Return         : None
*******************************************************************************/
void xWriteCH378Data( UINT8 mData ) 
{
	CH378_SPI_SCS_LOW( );
	SPI_Exchange( mData );									/* 发送数据 */
}

/*******************************************************************************
* Function Name  : xReadCH378Data
* Description    : 从CH378读数据
* Input          : None
* Output         : None
* Return         : 返回读取的数据
*******************************************************************************/
UINT8 xReadCH378Data( void )
{
	return SPI_Exchange( 0xff );
}


/*******************************************************************************
* Function Name  : Query378Interrupt
* Description    : 查询CH378中断(INT#低电平)
* Input          : None
* Output         : None
* Return         : 返回中断状态
*******************************************************************************/
UINT8 Query378Interrupt( void )
{
	/* 如果连接了CH378的中断引脚则直接查询中断引脚 */
	/* 如果未连接CH378的中断引脚则查询兼做中断输出的SDO引脚状态 */
#ifdef	CH378_INT_WIRE
	return( CH378_INT_PIN_WIRE( ) ? FALSE : TRUE );  
#else
	return( CH378_SPI_SDO_PIN( ) ? FALSE : TRUE );  
#endif
}

/*******************************************************************************
* Function Name  : mInitCH378Host
* Description    : 初始化CH378
* Input          : None
* Output         : None
* Return         : 返回操作状态
*******************************************************************************/
UINT8 mInitCH378Host( void )
{
	UINT8  res;
//	UINT8 version;

	/* 检测CH378连接是否正常 */
	CH378_Port_Init( );											/* 接口硬件初始化 */
	CH378_RST_Low();
	delay_us(10);
	CH378_RST_High();
	delay_ms(500);
	
//	version = 
	CH378GetICVer();
	
	xWriteCH378Cmd( CMD11_CHECK_EXIST );						/* 测试单片机与CH378之间的通讯接口 */
	xWriteCH378Data( 0x55 );
	res = xReadCH378Data( );
	xEndCH378Cmd( );
	if( res != 0xAA ) return( ERR_USB_UNKNOWN );
	/* 通讯接口不正常,可能原因有:接口连接异常,其它设备影响(片选不唯一),串口波特率,一直在复位,晶振不工作 */

	/* 设置CH378工作模式 */
	xWriteCH378Cmd( CMD11_SET_USB_MODE );						/* 设备工作模式 */
	xWriteCH378Data( 0x04 );									//SD卡
//	xWriteCH378Data( 0x07 );									//USB

	/* 等待模式设置完毕,对于操作SD卡大概需要10mS左右时间,对于操作USB设备大概需要35mS左右时间 */
	delay_ms( 50 );
	res = xReadCH378Data( );
	xEndCH378Cmd( );
#ifndef	CH378_INT_WIRE
	xWriteCH378Cmd( CMD20_SET_SDO_INT );						/* 设置SPI的SDO引脚的中断方式 */
	xWriteCH378Data( 0x16 );
	xWriteCH378Data( 0x01 );									/* SDO引脚在SCS片选无效时兼做中断请求输出 */
	xEndCH378Cmd( );
#endif
	if( res == CMD_RET_SUCCESS ) return( ERR_SUCCESS );
	else return( ERR_USB_UNKNOWN );								/* 设置模式错误 */
	
	
}


