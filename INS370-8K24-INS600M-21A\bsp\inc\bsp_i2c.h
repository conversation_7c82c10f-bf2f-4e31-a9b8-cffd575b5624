#ifndef ____BSP_I2C_H____
#define ____BSP_I2C_H____

#include "gd32f4xx.h"

#define I2C1_OWN_ADDRESS7      0x72

typedef enum {
	I2C_START = 0,
	I2C_RESTART,
	I2C_SEND_ADDRESS,
	I2C_CLEAR_ADDRESS_FLAG,
	I2C_TRANSMIT_DATA,
	I2C_TRANSMIT_ADDR,
	I2C_RECEIVE_ADDA,
	I2C_RECEIVE_DATA,
	I2C_STOP
} i2c_process_enum;

#define I2C_TIME_OUT    (uint16_t)(5000)

void bsp_i2c_init(void);
void i2c_bus_reset(void);
int8_t i2c_write_timeout(uint8_t dev_addr,uint8_t reg_addr, uint8_t *p_buffer, uint8_t nBytes);
int8_t i2c_read_timeout(uint8_t dev_addr, uint8_t reg_addr, uint8_t *reg_data,uint16_t nBytes);

#endif //____BSP_I2C_H____
