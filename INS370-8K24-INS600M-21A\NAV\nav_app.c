/*!
    \file  nav_app.c
    \brief Navigation application layer
    \note  This file provides application interface for navigation system
*/

#include "nav_app.h"
#include "nav.h"
#include "../INAV/FUNCTION.h"
#include "../INAV/GLOBALDATA.h"
#include <string.h>

/* Application state */
static nav_app_state_t app_state = NAV_APP_STATE_IDLE;
static nav_app_config_t app_config;

/*!
    \brief      Initialize navigation application
    \param[in]  config: application configuration
    \param[out] none
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_init(const nav_app_config_t *config)
{
    if (config != NULL) {
        memcpy(&app_config, config, sizeof(nav_app_config_t));
    } else {
        /* Default configuration */
        app_config.update_rate = 100;  /* 100Hz */
        app_config.output_rate = 10;   /* 10Hz */
        app_config.init_latitude = 31.2304;
        app_config.init_longitude = 121.4737;
        app_config.init_altitude = 50.0;
    }
    
    /* Initialize navigation system */
    if (nav_init() != NAV_STATUS_INIT) {
        app_state = NAV_APP_STATE_ERROR;
        return NAV_APP_STATUS_ERROR;
    }
    
    /* Set initial position */
    nav_set_param(NAV_PARAM_INIT_LAT, app_config.init_latitude);
    nav_set_param(NAV_PARAM_INIT_LON, app_config.init_longitude);
    nav_set_param(NAV_PARAM_INIT_ALT, app_config.init_altitude);
    
    app_state = NAV_APP_STATE_READY;
    return NAV_APP_STATUS_OK;
}

/*!
    \brief      Start navigation application
    \param[in]  none
    \param[out] none
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_start(void)
{
    if (app_state != NAV_APP_STATE_READY) {
        return NAV_APP_STATUS_ERROR;
    }
    
    app_state = NAV_APP_STATE_RUNNING;
    return NAV_APP_STATUS_OK;
}

/*!
    \brief      Stop navigation application
    \param[in]  none
    \param[out] none
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_stop(void)
{
    app_state = NAV_APP_STATE_READY;
    return NAV_APP_STATUS_OK;
}

/*!
    \brief      Process navigation application
    \param[in]  imu_data: IMU data
    \param[in]  gnss_data: GNSS data
    \param[out] nav_result: navigation result
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_process(const nav_imu_data_t *imu_data, const nav_gnss_data_t *gnss_data, nav_result_t *nav_result)
{
    if (app_state != NAV_APP_STATE_RUNNING) {
        return NAV_APP_STATUS_ERROR;
    }
    
    /* Update navigation system */
    nav_status_t status = nav_update(imu_data, gnss_data, nav_result);
    
    if (status == NAV_STATUS_ERROR) {
        app_state = NAV_APP_STATE_ERROR;
        return NAV_APP_STATUS_ERROR;
    }
    
    return NAV_APP_STATUS_OK;
}

/*!
    \brief      Get navigation application status
    \param[in]  none
    \param[out] none
    \retval     nav_app_state_t
*/
nav_app_state_t nav_app_get_state(void)
{
    return app_state;
}

/*!
    \brief      Reset navigation application
    \param[in]  none
    \param[out] none
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_reset(void)
{
    nav_reset();
    app_state = NAV_APP_STATE_IDLE;
    return NAV_APP_STATUS_OK;
}

/*!
    \brief      Get navigation application configuration
    \param[in]  none
    \param[out] config: application configuration
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_get_config(nav_app_config_t *config)
{
    if (config == NULL) {
        return NAV_APP_STATUS_ERROR;
    }
    
    memcpy(config, &app_config, sizeof(nav_app_config_t));
    return NAV_APP_STATUS_OK;
}

/*!
    \brief      Set navigation application configuration
    \param[in]  config: application configuration
    \param[out] none
    \retval     nav_app_status_t
*/
nav_app_status_t nav_app_set_config(const nav_app_config_t *config)
{
    if (config == NULL) {
        return NAV_APP_STATUS_ERROR;
    }
    
    memcpy(&app_config, config, sizeof(nav_app_config_t));
    
    /* Update navigation parameters */
    nav_set_param(NAV_PARAM_INIT_LAT, app_config.init_latitude);
    nav_set_param(NAV_PARAM_INIT_LON, app_config.init_longitude);
    nav_set_param(NAV_PARAM_INIT_ALT, app_config.init_altitude);
    
    return NAV_APP_STATUS_OK;
}
