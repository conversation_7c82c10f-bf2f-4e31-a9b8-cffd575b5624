# 编译配置指南

## 概述

本指南说明如何编译完全替换后的INS370-8K24-INS600M-21A项目。该项目已将my_project的完整应用程序移植到GD32F4xx平台。

## 编译环境要求

### 1. 开发工具
- **Keil MDK-ARM** v5.29或更高版本
- **GD32F4xx Device Family Pack** 最新版本
- **ARM Compiler** v6.x 或 v5.x

### 2. 硬件平台
- **目标MCU**: GD32F470xx系列
- **开发板**: 支持GD32F470的开发板
- **调试器**: J-Link、ST-Link或其他兼容调试器

## 项目配置

### 1. 打开项目
```
文件路径: INS370-8K24-INS600M-21A/Project/INS.uvprojx
```

### 2. 包含路径配置
在Keil项目设置中添加以下包含路径：

```
C/C++ Include Paths:
../INAV
../Source/inc
../Source/Edwoy
../Protocol
../Common/inc
../NAV
../bsp/inc
../Library/CMSIS
../Library/GD32F4xx_standard_peripheral/Include
../RTT
```

### 3. 预编译宏定义
```
Preprocessor Symbols:
GD32F470
USE_STDPERIPH_DRIVER
ARM_MATH_CM4
__FPU_PRESENT=1
CONFIG_FATFS=1
CONFIG_SDMMC=1
DEVICE_ACC_TYPE_ADLX355
```

### 4. 源文件组织

#### INAV算法模块
```
INAV/
├── navi.c
├── kalman.c
├── align.c
├── matvecmath.c
├── ins.c
├── ahrs.c
├── compen.c
├── dynamic_align.c
├── fuseGnssVelPosHeight.c
├── fuseTwoAntHeading.c
├── fuseBodyOdomVel.c
├── sins.c
├── sins2.c
├── private_math.c
├── interrupt.c
├── adxl355.c
├── AnnTempCompen.c
├── read_and_check_gnss_data.c
├── readpaoche.c
├── readpaoche2.c
└── readpaoche3.c
```

#### 应用程序模块
```
Source/src/
├── main.c
├── INS_Data.c
├── INS_Init.c
├── INS_Output.c
├── INS_Sys.c
├── systick.c
├── Time_Unify.c
├── flash.c
├── uart.c
├── timer.c
├── sdram.c
├── uart_dma.c
└── sd_fatfs.c
```

#### 协议处理模块
```
Protocol/
├── computerFrameParse.c
├── frame_analysis.c
├── protocol.c
└── InsTestingEntry.c
```

#### 公共模块
```
Common/src/
└── data_convert.c
```

#### BSP驱动模块（保留原有）
```
bsp/src/
├── bsp_gpio.c
├── bsp_uart.c
├── bsp_tim.c
├── bsp_fmc.c
├── bsp_can.c
├── bsp_i2c.c
├── bsp_rtc.c
├── bsp_sys.c
├── bsp_adc.c
├── bsp_exti.c
├── bsp_flash.c
├── bsp_fwdgt.c
├── bsp_soft_i2c_master.c
├── Logger.c
├── TCP_Server.c
├── FILE_SYS.c
├── common.c
├── ADIS16460.c
├── mpu9250.c
├── bmp280.c
├── bmp2.c
└── CH395/CH378相关驱动文件
```

#### 标准库
```
Library/GD32F4xx_standard_peripheral/Source/
├── gd32f4xx_gpio.c
├── gd32f4xx_rcu.c
├── gd32f4xx_exti.c
├── gd32f4xx_syscfg.c
├── gd32f4xx_usart.c
├── gd32f4xx_timer.c
├── gd32f4xx_fmc.c
├── gd32f4xx_can.c
├── gd32f4xx_i2c.c
├── gd32f4xx_rtc.c
├── gd32f4xx_adc.c
├── gd32f4xx_dma.c
├── gd32f4xx_spi.c
├── gd32f4xx_sdio.c
├── gd32f4xx_wwdgt.c
├── gd32f4xx_fwdgt.c
└── gd32f4xx_misc.c
```

### 5. 链接器配置
```
Memory Layout:
ROM: 0x08000000, Size: 0x00300000 (3MB Flash)
RAM: 0x20000000, Size: 0x00050000 (320KB SRAM)

Stack Size: 0x2000 (8KB)
Heap Size: 0x8000 (32KB)
```

## 编译步骤

### 1. 清理项目
```
Project -> Clean Targets
```

### 2. 重新编译
```
Project -> Rebuild all target files
```

### 3. 检查编译结果
确保没有编译错误和警告：
- 0 Error(s)
- 0 Warning(s)

## 常见编译问题及解决方案

### 1. 头文件找不到
**问题**: `fatal error: 'xxx.h' file not found`
**解决**: 检查包含路径配置，确保所有必要的路径都已添加

### 2. 函数未定义
**问题**: `undefined reference to 'xxx'`
**解决**: 检查对应的.c文件是否已添加到项目中

### 3. 宏定义冲突
**问题**: `'XXX' macro redefined`
**解决**: 检查预编译宏定义，移除重复定义

### 4. 平台适配问题
**问题**: HPM相关函数编译错误
**解决**: 确保platform_adapter.h正确包含，检查适配层实现

### 5. 内存不足
**问题**: `region 'ROM' overflowed`
**解决**: 优化代码大小或调整内存配置

## 调试配置

### 1. 调试器设置
```
Debug: J-Link/J-Trace Cortex
Target: GD32F470xx
Flash Download: 启用
Reset and Run: 启用
```

### 2. Flash编程算法
```
Programming Algorithm:
GD32F4xx Flash (0x08000000 - 0x082FFFFF)
```

### 3. 调试选项
```
Load Application at Startup: 启用
Go till main(): 启用
RTT Viewer: 启用（用于调试输出）
```

## 性能优化建议

### 1. 编译优化
```
Optimization Level: -O2 (平衡性能和调试)
Link Time Optimization: 启用
```

### 2. 代码优化
- 使用内联函数减少函数调用开销
- 优化数学运算使用ARM CMSIS DSP库
- 合理使用DMA减少CPU负载

### 3. 内存优化
- 使用const修饰只读数据
- 合理分配堆栈大小
- 使用内存池管理动态内存

## 验证测试

### 1. 基本功能测试
- [ ] 系统启动正常
- [ ] LED指示正常
- [ ] 串口通信正常
- [ ] GPIO中断响应正常

### 2. 算法功能测试
- [ ] INS算法运行正常
- [ ] 数据采集正常
- [ ] 滤波算法工作正常
- [ ] 输出数据格式正确

### 3. 性能测试
- [ ] 实时性满足要求
- [ ] CPU占用率合理
- [ ] 内存使用正常
- [ ] 功耗符合预期

## 发布配置

### 1. Release版本设置
```
Optimization: -O3 (最高性能)
Debug Information: 禁用
Assertions: 禁用
```

### 2. 代码保护
```
Flash Protection: 启用
Read Protection: Level 1
Write Protection: 根据需要配置
```

## 技术支持

如遇到编译问题，请检查：
1. 开发环境版本是否符合要求
2. 项目配置是否正确
3. 源文件是否完整
4. 平台适配是否正确

更多技术支持请参考：
- GD32F4xx用户手册
- Keil MDK用户指南
- 项目源码注释和文档
