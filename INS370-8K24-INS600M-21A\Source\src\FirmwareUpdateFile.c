//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// FirmwareUpdateFile.c
// 
// 
//
// V1.0
//     zhangjianzhou
// 2024.10.23
//---------------------------------------------------------

#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "flash.h"
#include "hpm_ppor_drv.h"
#include "hpm_soc.h"

#include "FirmwareUpdateFile.h"

//
void  Drv_FlashErase(uint32_t address)
{
    disable_global_irq(CSR_MSTATUS_MIE_MASK);
    norflash_erase_sector(address);
    enable_global_irq(CSR_MSTATUS_MIE_MASK);
    
}

//FLASH
void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
{
    disable_global_irq(CSR_MSTATUS_MIE_MASK);
    norflash_write(uiAddress, pucBuff, uiLen);
    enable_global_irq(CSR_MSTATUS_MIE_MASK);
}

//FLASH
void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
{
  norflash_read(uiAddress, pucBuff, uiLen);
}



//MCU 复位
void Drv_SystemReset(void)
{
    ppor_sw_reset(HPM_PPOR, 10);
}


//CRC
uint8_t Get_CRC8(uint8_t *ptr, uint8_t len)
{
	uint8_t i,j;
	uint8_t crc = 0x00;

	for(i = 0; i < len; i++)
	{
		crc ^= ptr[i];
		for (j = 0; j < 8; j++)
		{
			if (crc & 0x80)
				crc = (crc << 1) ^ 0x31;
			else
				crc = (crc << 1);
		}
	}

	return (crc);
}


//
void UpdateFileHandle(uint8_t *pucBuf,uint16_t usIndex,uint16_t usTotalBao,uint8_t ucLen)
{
	static uint32_t uiOffsetAddr=0,uiLastBaoInDex=1,flag=0;
	uint8_t UpdateFlagBuff[5]={0};//,FLASH

	if(uiLastBaoInDex == usIndex)
	{
		return;
	}

	if(flag==0)
	{
		flag=1;
		if(0 !=usIndex)
		{
			//UpdateFirmWareTx.info.ucRxFlag=2;//
			return;
		}
	}

#if 0
	if(usIndex == 0)
	{
		uiOffsetAddr=0;
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, TRUE);
		uiOffsetAddr += ucLen;
	}
	else if(usIndex<4096)
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen, FALSE);
		uiOffsetAddr += ucLen;
	}
	else if(usIndex == 4096)
	{
		uiOffsetAddr1=0;
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS1+uiOffsetAddr1, ucLen, TRUE);
		uiOffsetAddr1 += ucLen;
	}
	else
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS1+uiOffsetAddr1, ucLen, FALSE);
		uiOffsetAddr1 += ucLen;
	}
#else
	if(usIndex == 0)
	{
		uiOffsetAddr=0;
		for(int i=0;i<70;i++)
		{
			Drv_FlashErase(APP_UPDATE_ADDRESS+i*FLASH_WRITE_SECTOR_SIZE);
		}
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen);
		uiOffsetAddr += ucLen;
	}
	else
	{
		Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS+uiOffsetAddr, ucLen);
		uiOffsetAddr += ucLen;
	}
#endif

	

	//
	if(usIndex>=usTotalBao-1)
	{
		UpdateFlagBuff[0] =0xC5;//
		//
		UpdateFlagBuff[4] = (uint8_t)(uiOffsetAddr>>24);//
		UpdateFlagBuff[3] = (uint8_t)(uiOffsetAddr>>16);//
		UpdateFlagBuff[2] = (uint8_t)(uiOffsetAddr>>8);//
		UpdateFlagBuff[1] = (uint8_t)(uiOffsetAddr);//
		
		//Drv_FlashWrite_8bit(APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff), UpdateFlagBuff);//
                Drv_FlashErase(APP_UPDATE_CFG_ADDR);
		Drv_FlashWrite(UpdateFlagBuff, APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff));//

		uiOffsetAddr=0;

		//
		//Drv_SystemReset();
	}

	uiLastBaoInDex = usIndex;
}


//FLASH
uint8_t WriteTestData[20] = {0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x06,0x04,0x55,0x15};
uint8_t WriteTestData1[20] = {0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x43,0x48,0x69,0x7A,0xCB,0xAC,0x1D,0x6E,0x8F,0x06,0x04,0x35,0x97,0xAA};

uint32_t ReadTestData[10];
uint32_t AppReadTestData[10];
void FlashTest(void)
{
    Drv_FlashErase(APP_UPDATE_ADDRESS);
	//
    memset(ReadTestData,0,sizeof(ReadTestData));
    Drv_FlashWrite(WriteTestData,APP_UPDATE_ADDRESS,20);
    Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);

    memset(ReadTestData,0,sizeof(ReadTestData));
    Drv_FlashWrite(WriteTestData1,APP_UPDATE_ADDRESS+20,20);
    Drv_FlashRead(ReadTestData+5,APP_UPDATE_ADDRESS+20,5);


	//
    memset(ReadTestData,0,sizeof(ReadTestData));
    Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);

}


