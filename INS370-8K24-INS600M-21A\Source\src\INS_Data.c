#include "INS_Data.h"
#include "bsp_flash.h"

INSDataTypeDef hINSData;
FPGA_FRAME_DEF hINSFPGAData;
FPGA_FRAME_DEF hINSCANData;
uint16_t fpga_data_read_flag;
uint16_t fpga_setting_update_flag;
//AppSettingTypeDef hSetting;
//AppSettingTypeDef hDefaultSetting;
FPGA_Setting_TypeDef hFPGASetting;

LEDStateEnumTypeDef g_LEDIndicatorState;

uint32_t g_week;
double g_second;

void save_flash(void)
{
	InitFlashAddr(APP_SETTING_FLASH_OFFSET);
	WriteFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
	EndWrite();
}

void read_flash(void)
{
	InitFlashAddr(APP_SETTING_FLASH_OFFSET);	//bill 2023-06-25
	InitFlashAddr(0);
	ReadFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
}

