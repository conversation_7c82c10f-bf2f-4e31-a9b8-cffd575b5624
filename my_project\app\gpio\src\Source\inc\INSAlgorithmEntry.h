/*!
    \file    INS912AlgorithmEntry.h
    \brief   INS912 algorithm interface functions 
    \version v1.0.1
	\author	 Bill
	\date    2023/10/23
*/
#ifndef __INSALGORITHMENTRY_H
#define	__INSALGORITHMENTRY_H

typedef  struct _navcanin
{
	unsigned short Gear;				/* 汽车档位  0: 空档 和 停车档	2：前进档	4：倒车档 */
	unsigned short counter;			/* CAN信息更新次数，任何一个CAN信息有改变，此变量加 1 */
	float WheelSpeed_Front_Left;	/* 轮速 左前, 单位: km/h, 精度：待定 */
	float WheelSpeed_Front_Right;	/* 轮速 右前, 单位: km/h, 精度：待定 */
	float WheelSpeed_Back_Left;		/* 轮速 左后, 单位: km/h, 精度：待定 */
	float WheelSpeed_Back_Right;	/* 轮速 右后, 单位: km/h, 精度：待定 */
} navcanin_t;


typedef  struct _polldata
{
    unsigned short	data1;
    unsigned short 	data2;
    unsigned short	data3;
} polldata_t;

typedef struct _navoutdata {
    float 			roll;			//roll angle
    float 			pitch;			//angle of pitch
    float			azimuth;		//azimuth
    float 			gyroX;			//Gyro x axis
    float 			gyroY;			//Gyro y axis
	float			gyroZ;			//Gyro z axis
    float 			accelX;			//Accelerometer x axis
    float 			accelY;			//Accelerometer y axis
    float			accelZ;			//Accelerometer z axis
    double			latitude;		//latitude
    double			longitude;		//longitude
    double			altitude;		//altitude
    float			ve;				//East direction velocity
    float			vn;				//North direction velocity
    float			vu;				//upward velocity
    unsigned char	status;			//bit0: Position bit1: Velocity bit2: attitude bit3: heading Angle
    unsigned char	reserved[6];
    polldata_t		poll_frame;		//Polling data, Refer to INS instruction manual for specific meaning tabl
    unsigned int	gps_time;		//gps weekly seconds
    unsigned char	type;			//Polling data types
    unsigned int	gps_week;		//gps week
	
//    short 			roll;			//roll angle
//    short 			pitch;			//angle of pitch
//    short			azimuth;		//azimuth
//    short 			gyroX;			//Gyro x axis
//    short 			gyroY;			//Gyro y axis
//    short			gyroZ;			//Gyro z axis
//    short 			accelX;			//Accelerometer x axis
//    short 			accelY;			//Accelerometer y axis
//    short			accelZ;			//Accelerometer z axis
//    long			latitude;		//latitude
//    long			longitude;		//longitude
//    long			altitude;		//altitude
//    short			ve;				//East direction velocity
//    short			vn;				//North direction velocity
//    short			vu;				//upward velocity
//    unsigned char	status;			//bit0: Position bit1: Velocity bit2: attitude bit3: heading Angle
//    unsigned char	reserved[6];
//    polldata_t		poll_frame;		//Polling data, Refer to INS instruction manual for specific meaning tabl
//    unsigned int	gps_time;		//gps weekly seconds
//    unsigned char	type;			//Polling data types
//    unsigned char	gps_week;		//gps week
	
	short			temperature;	//device temperature
} navoutdata_t;


extern	unsigned short gfpgadata[200];
extern	navoutdata_t	gnavout;
/*
input:	pfpgadata -- Algorithm input parameters, FPGA 96 bytes. See Andrew's FPGA documentation for details on what each byte represents
		pnavout -- This parameter is the result of the algorithm, and the firmware code will transfer this result to the host computer
				-- Refer to the struct definition for the meaning of each variable in the struct
return: none
*/
extern	void INSAlgorithmEntry(unsigned short *pfpgadata, navcanin_t *pcanin, navoutdata_t *pnavout);






#endif
